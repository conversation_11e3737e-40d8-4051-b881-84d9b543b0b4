#!/usr/bin/env python3
"""
专门测试bounce.py中设备不一致修复的脚本
"""

import torch
import logging
from bounce.benchmarks import MaxSat60
from bounce.projection import AxUS
from bounce.ga_tabpfn_integration import GATabPFNIntegration
from bounce.genetic_algorithm import GAConfig

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_predicted_center_device_consistency():
    """测试predicted_center在不同设备上的一致性"""
    print("🔧 测试predicted_center设备一致性...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建测试数据
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建GA集成
    ga_config = GAConfig(population_size=30, max_generations=2)
    ga_integration = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        ga_config=ga_config,
        device=device,
        enable_ensemble=True
    )
    
    # 创建训练数据
    X_train = torch.rand(8, benchmark.representation_dim, dtype=torch.float64)
    y_train = torch.rand(8, dtype=torch.float64)
    
    try:
        # 获取预测中心点
        predicted_center = ga_integration.predict_best_center_with_tabpfn(
            existing_X=X_train,
            existing_y=y_train,
            n_candidates=10
        )
        
        print(f"✅ predicted_center获取成功")
        print(f"   设备: {predicted_center.device}")
        print(f"   形状: {predicted_center.shape}")
        print(f"   设备一致性: {str(predicted_center.device).startswith(device) or (device == 'cpu' and predicted_center.device.type == 'cpu')}")
        
        # 模拟bounce.py中的设备转换操作
        target_device = torch.device(device)
        
        # 测试转换操作（bounce.py第635行的修复）
        predicted_center_fixed = predicted_center.to(device=target_device)
        center_01 = (predicted_center_fixed + 1) / 2
        
        # 创建模拟的x_scaled
        x_scaled = torch.rand(5, axus.target_dim, dtype=torch.float64, device=target_device)
        batch_size = 1
        
        x_bests_for_tr = center_01.unsqueeze(0).repeat(batch_size, 1).to(dtype=x_scaled.dtype, device=x_scaled.device)
        
        print(f"✅ bounce.py第635行修复测试通过")
        print(f"   x_bests_for_tr设备: {x_bests_for_tr.device}")
        
        # 测试混合变量的情况（bounce.py第674行的修复）
        x_bests_for_interleaved = predicted_center_fixed.unsqueeze(0).repeat(batch_size, 1).to(dtype=x_scaled.dtype, device=x_scaled.device)
        
        print(f"✅ bounce.py第674行修复测试通过")
        print(f"   x_bests_for_interleaved设备: {x_bests_for_interleaved.device}")
        
        # 测试连续变量处理（bounce.py第697行的修复）
        continuous_indices = torch.tensor([0, 1, 2], device=target_device)  # 模拟连续变量索引
        x_best = torch.rand(1, axus.target_dim, dtype=torch.float64, device=target_device)
        
        true_center = predicted_center_fixed.to(device=x_best.device)
        x_best[:, continuous_indices] = true_center[continuous_indices]
        
        print(f"✅ bounce.py第697行修复测试通过")
        print(f"   x_best设备: {x_best.device}")
        print(f"   true_center设备: {true_center.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_continuous_indices_device():
    """测试continuous_indices的设备一致性"""
    print("\n🔧 测试continuous_indices设备一致性...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 模拟bounce.py中continuous_indices的创建
    from bounce.util.benchmark import ParameterType
    
    # 创建测试数据
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    try:
        # 原来的方式（可能导致设备不一致）
        continuous_indices_old = torch.tensor([
            i for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)
        ])
        
        # 新的方式（修复后）
        continuous_indices_new = torch.tensor([
            i for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)
        ], device=device)
        
        print(f"✅ continuous_indices创建成功")
        print(f"   原方式设备: {continuous_indices_old.device}")
        print(f"   新方式设备: {continuous_indices_new.device}")
        
        # 测试与其他张量的兼容性
        x_best = torch.rand(1, axus.target_dim, dtype=torch.float64, device=device)
        true_center = torch.rand(axus.target_dim, dtype=torch.float64, device=device)
        
        # 这个操作在修复前可能会失败，修复后应该成功
        x_best[:, continuous_indices_new] = true_center[continuous_indices_new]
        
        print(f"✅ 设备一致性操作测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ continuous_indices测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🚀 Bounce.py设备一致性修复专项测试")
    print("=" * 60)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    device = 'cuda' if cuda_available else 'cpu'
    
    print(f"CUDA可用: {cuda_available}")
    print(f"测试设备: {device}")
    
    # 执行测试
    test_results = []
    
    # 1. 测试predicted_center设备一致性
    test_results.append(test_predicted_center_device_consistency())
    
    # 2. 测试continuous_indices设备一致性
    test_results.append(test_continuous_indices_device())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有设备一致性修复测试通过！")
        print("💡 bounce.py中的设备不一致问题应该已解决。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")