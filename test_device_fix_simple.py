#!/usr/bin/env python3
"""
简化的设备一致性测试，用于验证修复是否有语法错误
"""

def test_imports():
    """测试导入是否正常"""
    try:
        # 测试设备管理器导入
        from bounce.device_manager import DeviceManager
        print("✅ DeviceManager导入成功")
        
        # 测试代理模型导入
        from bounce.gp_surrogate import GPGlobalSurrogate
        from bounce.rbf_surrogate import RBFGlobalSurrogate  
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        print("✅ 所有代理模型导入成功")
        
        # 测试集成管理器导入
        from bounce.ensemble_manager import EnsembleManager
        print("✅ EnsembleManager导入成功")
        
        # 测试GA集成导入
        from bounce.ga_tabpfn_integration import GATabPFNIntegration
        print("✅ GATabPFNIntegration导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_class_creation():
    """测试类的基本创建"""
    try:
        # 只测试能否成功导入和实例化类（不运行实际计算）
        import torch
        
        # 测试设备管理器创建
        from bounce.device_manager import DeviceManager
        device_manager = DeviceManager('cpu')
        print("✅ DeviceManager创建成功")
        
        # 获取设备信息
        info = device_manager.get_device_info()
        print(f"📊 设备信息: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 类创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def check_method_signatures():
    """检查方法签名是否正确"""
    try:
        import inspect
        from bounce.device_manager import DeviceManager
        
        # 检查设备管理器方法
        dm = DeviceManager('cpu')
        required_methods = [
            'ensure_tensor_device',
            'safe_to_device', 
            'numpy_to_tensor',
            'tensor_to_numpy',
            'device_context'
        ]
        
        for method_name in required_methods:
            if hasattr(dm, method_name):
                method = getattr(dm, method_name)
                sig = inspect.signature(method)
                print(f"✅ {method_name}{sig}")
            else:
                print(f"❌ 缺少方法: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {e}")
        return False

def verify_device_manager_functionality():
    """验证设备管理器基础功能"""
    try:
        import torch
        from bounce.device_manager import DeviceManager
        
        # 测试CPU设备管理器
        dm_cpu = DeviceManager('cpu')
        print(f"✅ CPU设备管理器: {dm_cpu}")
        
        # 测试张量创建
        test_data = [1.0, 2.0, 3.0]
        tensor = dm_cpu.safe_to_device(test_data)
        print(f"✅ 张量创建成功: {tensor.device}, {tensor.dtype}")
        
        # 测试张量转换
        cpu_tensor = torch.tensor([4.0, 5.0, 6.0], dtype=torch.float32)
        converted = dm_cpu.ensure_tensor_device(cpu_tensor)
        print(f"✅ 张量转换成功: {converted.device}, {converted.dtype}")
        
        # 测试CUDA（如果可用）
        if torch.cuda.is_available():
            dm_cuda = DeviceManager('cuda')
            print(f"✅ CUDA设备管理器: {dm_cuda}")
            
            cuda_tensor = dm_cuda.safe_to_device(test_data)
            print(f"✅ CUDA张量创建: {cuda_tensor.device}")
        else:
            print("ℹ️  CUDA不可用，跳过CUDA测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 设备管理器功能测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🔧 多代理模型集成CUDA设备修复验证")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试导入
    print("\n📦 测试模块导入...")
    test_results.append(test_imports())
    
    # 2. 测试类创建
    print("\n🏗️  测试类创建...")
    test_results.append(test_class_creation())
    
    # 3. 检查方法签名
    print("\n🔍 检查方法签名...")
    test_results.append(check_method_signatures())
    
    # 4. 验证设备管理器功能
    print("\n⚙️  验证设备管理器功能...")
    test_results.append(verify_device_manager_functionality())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有基础测试通过！设备管理器修复代码语法正确。")
        print("💡 建议在具有PyTorch环境的系统上运行完整测试。")
        success = True
    else:
        print("⚠️  部分测试失败，需要检查代码。")
        success = False
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")