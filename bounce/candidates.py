import logging
import warnings
from typing import Optional

import gin
import numpy as np
import torch
from botorch.acquisition import ExpectedImprovement, qExpectedImprovement
from botorch.models import SingleTaskGP
from botorch.optim import optimize_acqf
from botorch.sampling import SobolQMCNormalSampler
from gpytorch.kernels import MaternKernel

from bounce.kernel.categorical_mixture import MixtureKernel
from bounce.neighbors import hamming_distance, hamming_neighbors_within_tr
from bounce.projection import AxUS
from bounce.trust_region import TrustRegion
from bounce.util.benchmark import ParameterType


@gin.configurable
def create_candidates_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int = 1,
    x_bests: Optional[list[torch.Tensor]] = None,
    add_spray_points: bool = True,
    sampler: Optional[SobolQMCNormalSampler] = None,
    use_surrogate_local: bool = True,  # 新增：是否使用代理模型作为局部代理模型
    local_model_type: str = 'tabpfn',  # 新增：局部代理模型类型
    surrogate_manager = None,  # 新增：代理模型管理器
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    Create candidate points for the next batch.

    Args:
        model: The current GP model
        batch_size: The number of candidate points to create
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The approximate posterior samples
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        x_bests: The center of the trust region, should be in [0, 1]^d
        add_spray_points: Whether to add spray points (points within hamming distance 1 of the center)
        sampler: The sampler to use for the acquisition function


    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    # Get the indices of the continuous parameters
    indices_not_to_optimize = torch.tensor(
        [i for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)],
        dtype=torch.long
    )

    # Find the center of the trust region
    if x_bests is not None:
        # 🎯 关键修复：优先使用TabPFN预测的中心点
        # 检查是否为纯二分问题（没有连续变量需要保留）
        if len(indices_not_to_optimize) == 0:
            # 纯二分问题：完全使用TabPFN预测的中心点
            x_centers = x_bests.clone()
            logging.debug(f"使用TabPFN预测的完整中心点: {x_centers[0][:5]}...")
        else:
            # 混合变量问题：使用已评估的最佳点，但替换连续变量部分
            x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
            x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)
            x_centers[:, indices_not_to_optimize] = (
                x_bests[:, indices_not_to_optimize] + 1
            ) / 2
            logging.debug(f"混合变量：使用TabPFN预测的连续变量部分")
    else:
        # 没有TabPFN预测的中心点：使用已评估的最佳点
        x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
        # x_center should be in [0, 1]^d at this point
        x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)
        logging.debug(f"使用已评估的最佳点作为中心")

    # define the number of candidates as in the TuRBO paper
    n_candidates = min(5000, max(2000, 200 * axus.target_dim))

    x_batch_return = torch.zeros(
        (batch_size, axus.target_dim), dtype=x_scaled.dtype, device=x_scaled.device
    )
    fx_batch_return = torch.zeros(
        (batch_size, 1), dtype=fx_scaled.dtype, device=fx_scaled.device
    )

    for batch_index in range(batch_size):
        _acquisition_function = acquisition_function
        if acquisition_function is None:
            assert (
                sampler is not None
            ), "Either acquisition_function or sampler must be provided"
            x_pending = x_batch_return[:batch_index, :] if batch_index > 0 else None
            _acquisition_function = qExpectedImprovement(
                model=model,
                best_f=(-fx_scaled).max().item(),
                sampler=sampler,
                X_pending=x_pending,
            )

        def ts(x: torch.Tensor, batch_index: int):
            """
            Get the approximate posterior sample of a specific batch index.

            Args:
                x: The points to evaluate the posterior sample at
                batch_index: The index of the batch to evaluate the posterior sample for

            Returns:
                The approximate posterior sample at the given points

            """

            return -_acquisition_function(x.unsqueeze(1))

        x_candidates = sample_initial_points_discrete(
            x_center=x_centers[batch_index],
            axus=axus,
            tr_length=trust_region.length_discrete,
            n_initial_points=n_candidates,
        )

        if add_spray_points:
            x_spray = hamming_neighbors_within_tr(
                x_center=x_centers[batch_index],
                x=x_centers[batch_index],
                tr_length=trust_region.length_discrete,
                axus=axus,
            )
            x_candidates = torch.vstack((x_candidates, x_spray))

        # 修改。合并之后在删除重复点。
        x_candidates = torch.unique(x_candidates, dim=0)
        
        # 🚀 评估候选点：使用代理模型或传统采集函数
        if use_surrogate_local and surrogate_manager is not None:
            # 获取或创建局部代理模型
            local_surrogate = surrogate_manager.get_model(local_model_type)
            if local_surrogate is None:
                # 如果局部模型不存在，尝试使用全局模型的数据创建
                global_surrogate = None
                for model_type in surrogate_manager.get_created_models():
                    model = surrogate_manager.get_model(model_type)
                    if model.is_fitted:
                        global_surrogate = model
                        break

                if global_surrogate is not None:
                    # 使用全局模型的训练数据创建局部模型
                    if hasattr(global_surrogate, 'X_train') and hasattr(global_surrogate, 'y_train'):
                        if local_model_type == 'tabpfn':
                            local_surrogate = surrogate_manager.create_model(local_model_type, n_bins=5)
                        else:
                            local_surrogate = surrogate_manager.create_model(local_model_type)

                        # 使用全局模型的数据训练局部模型
                        if hasattr(global_surrogate, 'X_train') and global_surrogate.X_train is not None:
                            X_train = torch.tensor(global_surrogate.X_train) if not isinstance(global_surrogate.X_train, torch.Tensor) else global_surrogate.X_train
                            y_train = torch.tensor(global_surrogate.y_train) if not isinstance(global_surrogate.y_train, torch.Tensor) else global_surrogate.y_train
                            local_surrogate.fit(X_train, y_train)

            if local_surrogate is not None and local_surrogate.is_fitted:
                # 🔧 修复：低维候选点 → 投影到高维 → 使用代理模型预测
                with torch.no_grad():
                    try:
                        # 🔧 修复：正确的转置调用方式
                        x_candidates_high = axus.project_up(x_candidates.T).T

                        # 使用已训练的代理模型预测质量分数
                        candidate_acquisition_values = local_surrogate.predict_quality(x_candidates_high)
                        logging.info(f"使用{local_model_type.upper()}评估了{len(x_candidates)}个候选点")
                    except Exception as e:
                        logging.error(f"{local_model_type.upper()}候选点评估失败: {e}")
                        logging.error(f"候选点形状: {x_candidates.shape}, target_dim: {axus.target_dim}")
                        # 🚨 回退到传统采集函数
                        candidate_acquisition_values = acquisition_function(x_candidates.unsqueeze(-2)).squeeze(-1)
                        logging.info(f"回退到传统采集函数评估了{len(x_candidates)}个候选点")
            else:
                # 使用传统采集函数
                candidate_acquisition_values = acquisition_function(x_candidates.unsqueeze(-2)).squeeze(-1)
                logging.info(f"使用传统采集函数评估了{len(x_candidates)}个候选点")
        else:
            # 使用传统采集函数评估候选点
            with torch.no_grad():
                candidate_acquisition_values = ts(x_candidates, batch_index=batch_index)
        # Find the top k candidates with the highest acquisition function value
        top_k_candidate_indices = torch.topk(
            candidate_acquisition_values,
            k=min(3, len(candidate_acquisition_values)),
            largest=False,
        )[1]
        # Start local search
        best_posterior_value = torch.inf
        x_best = None

        for top_index in top_k_candidate_indices:
            x_candidate = x_candidates[top_index, :].clone().unsqueeze(0)

            posterior_value_k = candidate_acquisition_values[top_index].item()

            if posterior_value_k < best_posterior_value:
                best_posterior_value = posterior_value_k
                x_best = x_candidate
            while True:
                x_start_neighbors = hamming_neighbors_within_tr(
                    x_center=x_centers[batch_index],
                    x=x_candidate,
                    tr_length=trust_region.length_discrete,
                    axus=axus,
                )

                # remove rows from x_start_neighbors that are already in self.x (which is a 2d tensor of shape (n, d))
                for x_eval in x_scaled.to(device=device):
                    x_start_neighbors = x_start_neighbors[
                        ~torch.all(x_start_neighbors == x_eval, dim=1)
                    ]

                if x_start_neighbors.numel() == 0:
                    # no neighbors left, continue with next top candidate
                    break

                # 🚀 评估邻居点：使用代理模型或传统采集函数
                if use_surrogate_local and surrogate_manager is not None:
                    # 获取局部代理模型
                    local_surrogate = surrogate_manager.get_model(local_model_type)
                    if local_surrogate is not None and local_surrogate.is_fitted:
                        # 🔧 修复：直接投影到高维空间并使用代理模型预测
                        with torch.no_grad():
                            try:
                                # 🔧 修复：正确的转置调用方式
                                x_neighbors_high = axus.project_up(x_start_neighbors.T).T
                                neighbors_acq_val = local_surrogate.predict_quality(x_neighbors_high)
                            except Exception as e:
                                logging.error(f"{local_model_type.upper()}邻居评估失败: {e}")
                                logging.error(f"邻居点形状: {x_start_neighbors.shape}, target_dim: {axus.target_dim}")
                                # 🚨 回退到传统采集函数
                                neighbors_acq_val = ts(x_start_neighbors, batch_index=batch_index)
                    else:
                        # 使用传统采集函数评估邻居点
                        with torch.no_grad():
                            neighbors_acq_val = ts(x_start_neighbors, batch_index=batch_index)
                else:
                    # 使用传统采集函数评估邻居点
                    with torch.no_grad():
                        neighbors_acq_val = ts(x_start_neighbors, batch_index=batch_index)

                if (
                    len(neighbors_acq_val) > 0
                    and torch.min(neighbors_acq_val) < posterior_value_k
                ):
                    x_candidate = x_start_neighbors[torch.argmin(neighbors_acq_val)]
                    posterior_value_k = torch.min(neighbors_acq_val).item()
                else:
                    # could not find a better neighbor, continue with next top candidate
                    break
                if posterior_value_k < best_posterior_value:
                    best_posterior_value = posterior_value_k
                    x_best = x_candidate.unsqueeze(0)
        if x_best is None:
            warnings.warn(
                "Could not find a better point than the center of the trust region"
            )
            # choose random point
            x_best = x_centers[batch_index].unsqueeze(0)
        # repeat x_cand batch_size many times
        x_batch_return[batch_index, :] = x_best.squeeze()
        fx_batch_return[batch_index, :] = best_posterior_value

    assert len(indices_not_to_optimize) == 0 or torch.any(
        x_centers[:, indices_not_to_optimize].squeeze()
        == x_batch_return[:, indices_not_to_optimize].squeeze()
    ), "x_ret should not be optimized at indices_not_to_optimize"

    # transform to [-1, 1], was [0, 1]
    x_batch_return = x_batch_return * 2 - 1

    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_discrete]),
    }

    return x_batch_return, fx_batch_return.reshape(batch_size), tr_state


def create_candidates_continuous(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int,
    indices_to_optimize: Optional[torch.Tensor] = None,
    x_bests: Optional[list[torch.Tensor]] = None,
    sampler: Optional[SobolQMCNormalSampler] = None,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    Create candidate points for the next batch.

    Args:
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The acquisition function to use
        model: The current GP model
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        indices_to_optimize: The indices of the candidate points to optimize (in case of mixed spaces)
        x_bests: The center of the trust region
        batch_size: int

    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    if indices_to_optimize is None:
        indices_to_optimize = torch.arange(axus.target_dim, device=device)
    
    # 🔧 关键修复：确保所有张量在同一设备上
    # 使用 indices_to_optimize 的设备来创建其他张量
    target_device = indices_to_optimize.device if hasattr(indices_to_optimize, 'device') else device
    full_range = torch.arange(axus.target_dim, device=target_device)
    
    # 🔍 设备调试信息
    logging.debug(f"🔍 [CANDIDATES_DEBUG] indices_to_optimize设备: {indices_to_optimize.device if hasattr(indices_to_optimize, 'device') else 'N/A'}")
    logging.debug(f"🔍 [CANDIDATES_DEBUG] full_range设备: {full_range.device}")
    logging.debug(f"🔍 [CANDIDATES_DEBUG] target_device: {target_device}")
    
    indices_not_to_optimize = full_range[
        ~torch.isin(full_range, indices_to_optimize)
    ]
    
    logging.debug(f"🔍 [CANDIDATES_DEBUG] indices_not_to_optimize设备: {indices_not_to_optimize.device}")

    # 🎯 关键修复：优先使用TabPFN预测的中心点
    if x_bests is not None:
        # 检查是否为纯连续问题或混合变量问题
        if indices_to_optimize is not None and len(indices_to_optimize) < axus.target_dim:
            # 混合变量问题：使用已评估的最佳点，但替换需要优化的部分
            x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
            x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)
            x_centers[:, indices_not_to_optimize] = (
                x_bests[:, indices_not_to_optimize] + 1
            ) / 2
            logging.debug(f"混合变量连续优化：使用TabPFN预测的部分中心点")
        else:
            # 纯连续问题：完全使用TabPFN预测的中心点
            x_centers = x_bests.clone()
            logging.debug(f"使用TabPFN预测的完整中心点（连续优化）")
    else:
        # 没有TabPFN预测的中心点：使用已评估的最佳点
        x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
        # repeat x_centers batch_size many times
        x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)
        logging.debug(f"使用已评估的最佳点作为中心（连续优化）")

    assert len(x_centers.shape) == 2, "x_center should be a 2d tensor"

    fx_argmins = torch.zeros(batch_size, dtype=torch.long, device=device)
    fx_mins = torch.zeros(batch_size, dtype=torch.double, device=device)
    x_cand_downs = torch.zeros(
        (batch_size, axus.target_dim), dtype=torch.double, device=device
    )
    for batch_index in range(batch_size):
        x_center = x_centers[batch_index, :]

        if isinstance(model.covar_module.base_kernel, MixtureKernel):
            weights = model.covar_module.base_kernel.continuous_kernel.lengthscale.detach().squeeze(
                0
            )
        elif isinstance(model.covar_module.base_kernel, MaternKernel):
            weights = model.covar_module.base_kernel.lengthscale.detach().squeeze(0)
        else:
            raise NotImplementedError(
                "Only MixtureKernel and MaternKernel are supported"
            )
        weights /= weights.mean()
        weights /= torch.prod(torch.pow(weights, 1 / len(weights)))
        _x_center = x_center[indices_to_optimize]
        _tr_lb = torch.clip(
            _x_center - trust_region.length_continuous * weights / 2, 0, 1
        )
        _tr_ub = torch.clip(
            _x_center + trust_region.length_continuous * weights / 2, 0, 1
        )
        tr_lb = torch.zeros(axus.target_dim, dtype=torch.double, device=device)
        tr_ub = torch.ones(axus.target_dim, dtype=torch.double, device=device)
        tr_lb[indices_to_optimize] = _tr_lb
        tr_ub[indices_to_optimize] = _tr_ub

        _acquisition_function = acquisition_function
        if acquisition_function is None:
            assert (
                sampler is not None
            ), "Either acquisition_function or sampler must be provided"
            x_pending = x_cand_downs[:batch_index, :] if batch_index > 0 else None
            _acquisition_function = qExpectedImprovement(
                model=model,
                best_f=(-fx_scaled).max().item(),
                sampler=sampler,
                X_pending=x_pending,
            )

        # EI-based acquisition function
        x_cand_down = optimize_acqf(
            acq_function=_acquisition_function,
            bounds=torch.stack([tr_lb, tr_ub], dim=0),
            q=1,
            fixed_features={
                i: x_center[i].item() for i in indices_not_to_optimize.tolist()
            },
            num_restarts=10,
            raw_samples=512,
        )
        x_cand_down, y_cand_down = x_cand_down
        x_cand_downs[batch_index, :] = x_cand_down
        fx_argmins[batch_index] = -y_cand_down

    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_continuous]),
        "lb": tr_lb.detach().cpu().numpy(),
        "ub": tr_ub.detach().cpu().numpy(),
    }
    
    return x_cand_downs * 2 - 1, fx_argmins.reshape(batch_size), tr_state


def sample_initial_points_discrete(
    x_center: torch.Tensor,
    tr_length: torch.Tensor,
    axus: AxUS,
    n_initial_points: int,
) -> torch.Tensor:
    """
    这是一个辅助函数，用于在离散搜索阶段随机采样一批候选点，作为后续采集函数打分的输入。

    Sample initial points for the discrete parameters

    Args:
        x_center: the center of the trust region
        tr_length: the length of the trust region
        axus: the AxUS embedding
        n_initial_points: the number of initial points to sample

    Returns:
        x_cand: the sampled initial points

    """

    # 获取所有离散参数类型（排除连续类型）
    discrete_parameter_types = [
        pt for pt in ParameterType if pt != ParameterType.CONTINUOUS
    ]

    # copy x_center n_initial_points times
    # # 复制中心点 n_initial_points 次；接下来会在这些复制的点上“有选择地修改某些维度”。
    x_cand = torch.repeat_interleave(x_center.unsqueeze(0), n_initial_points, dim=0)

    # 对不同类型的离散参数进行随机化
    for parameter_type in discrete_parameter_types:
        # 如果当前类型的bin数量为0，不需要优化
        if axus.n_bins_of_type(parameter_type) == 0:
            # No parameters of this type
            continue

        # 二进制参数
        if parameter_type == ParameterType.BINARY:
            # 拿到bin的索引
            indices = torch.tensor(
                [i for b, i in axus.bins_and_indices_of_type(parameter_type)]
            )
            # draw min(tr_length, len(indices)) indices for each candidate
            # 为每个候选点，随机选出 tr_length - 1 个 binary 维度
            indices_for_cand = torch.tensor(
                np.array(
                    [
                        np.random.choice(
                            indices, min(tr_length - 1, len(indices)), replace=False
                        )
                        for _ in range(n_initial_points)
                    ]
                ),
                dtype=torch.long,
                device=x_cand.device,
            )
            # draw values for each index
            # 为这些位置采样新的值（0 或 1）
            values_for_cand = torch.randint(
                0,
                2,
                (n_initial_points, len(indices_for_cand[0])),
                dtype=x_cand.dtype,
                device=x_cand.device,
            )
            # set values for each candidate
            # 把这些新值写入对应的候选点位置
            x_cand = x_cand.scatter_(1, indices_for_cand, values_for_cand)
        elif parameter_type == ParameterType.CATEGORICAL:
            # 获取所有分类参数的索引组;例如 indicess = [[3,4], [7,8,9], [12,13]]，表示有 3 个分类变量；
            indicess = [i for b, i in axus.bins_and_indices_of_type(parameter_type)]

            # # 如果分类变量组数量超过信任域长度，随机选择部分 min(tr_length, len(indicess) 组
            if len(indicess) > tr_length:
                # index_setss的形式：[[1,2], [0,2], [1,2]]
                index_setss = [
                    np.random.choice(
                        np.arange(len(indicess)),
                        min(tr_length, len(indicess)),
                        replace=False,
                    )
                    # 选择n_initial_points次
                    for _ in range(n_initial_points)
                ]
                # 拿到索引以及对应索引组 例如i=1,index_sets=[0,2],这里的0,2又对应indicess中的[3,4],[12,13]
                for i, index_sets in enumerate(index_setss):
                    # 这里就是替换index_sets=[0,2]为[[3,4],[12,13]]
                    index_sets = [indicess[i] for i in index_sets]
                    # set x_cand to 0 for each index
                    # 涉及的one-hot变量全部变为0
                    x_cand[i, torch.cat(index_sets)] = 0

                    # 随机选择一位为1
                    if True:  # else:
                        # this is the expensive part
                        for indices in index_sets:
                            # set one index to 1
                            x_cand[i, np.random.choice(indices)] = 1
            else:
                # 处理和之前类似，所有涉及的fenlie
                for indices in indicess:
                    # set x_cand to 0 for each index
                    x_cand[:, indices] = 0
                    # sample n_initial_points indices
                    indices_for_cand = np.random.choice(indices, n_initial_points)
                    # set one index to 1
                    x_cand[torch.arange(n_initial_points), indices_for_cand] = 1
            pass

        elif parameter_type == ParameterType.ORDINAL:
            raise NotImplementedError("Ordinal parameters are not supported yet")
        else:
            raise ValueError(f"Unknown parameter type {parameter_type}")

    # remove duplicates
    # 移除重复点，移动到和最优点邻居合并之后
    # x_cand = torch.unique(x_cand, dim=0)

    # remove points that coincide with x_center
    # 移除与中心点相同的点
    x_cand = x_cand[torch.any(x_cand != x_center, dim=1), :]

    # remove candidates that are not within the trust region
    # 移除不在信任域内的点
    x_cand_in_tr = x_cand[hamming_distance(x_cand, x_center) <= tr_length, :]
    if len(x_cand_in_tr) == 0:
        logging.debug(f"No initial points in trust region, returning all candidates")
        
    # 如果信任域内有点，返回这些点；否则返回所有候选点，
    # 就是说如果点都不在TR内，可以适当放宽条件，保证算法运行
    return x_cand_in_tr if len(x_cand_in_tr) > 0 else x_cand
