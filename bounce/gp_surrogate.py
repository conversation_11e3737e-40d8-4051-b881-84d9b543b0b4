import logging
import torch
import numpy as np
from typing import Optional, Tuple
from botorch.models import SingleTaskGP
from botorch.acquisition import ExpectedImprovement
from botorch.optim import optimize_acqf
import gpytorch

from bounce.gaussian_process import get_gp, fit_mll
from bounce.util.benchmark import ParameterType
from bounce.device_manager import DeviceManager


class GPGlobalSurrogate:
    """GP全局代理模型，用于替代TabPFN进行全局搜索"""
    
    def __init__(self, benchmark, axus, device: str = 'cpu', **gp_params):
        """
        初始化GP代理模型
        
        Args:
            benchmark: 基准函数
            axus: AxUS投影对象
            device: 计算设备
            **gp_params: GP参数
        """
        self.benchmark = benchmark
        self.axus = axus
        self.device_manager = DeviceManager(device)
        self.is_fitted = False
        
        # GP默认参数
        self.gp_params = {
            'lengthscale_prior_shape': 1.5,
            'lengthscale_prior_rate': 0.1,
            'outputscale_prior_shape': 1.5,
            'outputscale_prior_rate': 0.5,
            'noise_prior_shape': 1.1,
            'noise_prior_rate': 0.05,
            'discrete_ard': False,
            'continuous_ard': True,
        }
        self.gp_params.update(gp_params)
        
        # 存储训练数据
        self.model = None
        self.train_x = None
        self.train_y = None
        self.y_mean = None
        self.y_std = None
        
        # logging.info(f"GP全局代理模型初始化完成，设备: {self.device_manager.device}, 参数: {self.gp_params}")
    
    def fit(self, X: torch.Tensor, y: torch.Tensor) -> None:
        """
        训练GP模型
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features) - 高维空间数据
            y: 目标值，形状为 (n_samples,)
        """
        try:
            # 使用设备管理器确保数据在正确设备
            X_train = self.device_manager.safe_to_device(X, dtype=self.device_manager.dtype)
            y_train = self.device_manager.safe_to_device(y, dtype=self.device_manager.dtype)
            
            # 确保数据形状正确
            if X_train.ndim == 1:
                X_train = X_train.unsqueeze(0)
            if y_train.ndim > 1:
                y_train = y_train.flatten()
            
            # 🔧 关键修复：数据预处理 - 将高维数据映射到[0,1]范围用于GP训练
            # GP期望输入在[0,1]范围内，避免维度不匹配
            
            # 将高维数据标准化到[0,1]范围
            X_min = X_train.min(dim=0)[0]
            X_max = X_train.max(dim=0)[0]
            
            # 避免除零错误
            X_range = X_max - X_min
            X_range = torch.where(X_range < 1e-8, torch.ones_like(X_range), X_range)
            
            X_normalized = (X_train - X_min) / X_range
            
            # 存储标准化参数（保持设备一致）
            self.X_min = X_min
            self.X_max = X_max  
            self.X_range = X_range
            
            # 标准化目标值
            self.y_mean = y_train.mean()
            self.y_std = y_train.std()
            if self.y_std < 1e-6:
                self.y_std = self.device_manager.safe_to_device(1.0)
            
            y_normalized = (y_train - self.y_mean) / self.y_std
            
            # 创建GP模型（保持设备一致）
            with self.device_manager.device_context():
                from botorch.models import SingleTaskGP
                from botorch.fit import fit_gpytorch_mll
                from gpytorch.mlls import ExactMarginalLogLikelihood
                
                # 创建标准的GP模型
                self.model = SingleTaskGP(X_normalized, y_normalized.unsqueeze(-1))
                mll = ExactMarginalLogLikelihood(self.model.likelihood, self.model)
                
                # 训练模型
                fit_gpytorch_mll(mll)
            
            # 存储训练数据
            self.train_x = X_normalized
            self.train_y = y_normalized
            
            self.is_fitted = True
            
            # logging.info(f"GP模型训练完成，设备: {self.device_manager.device}, 训练样本数: {len(X_train)}, 输入维度: {X_train.shape[1]}")
            
        except Exception as e:
            logging.error(f"GP模型训练失败: {e}")
            self.is_fitted = False
            raise e
    
    def predict(self, X: torch.Tensor) -> torch.Tensor:
        """
        使用GP模型进行预测
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features) - 高维空间数据
            
        Returns:
            预测值，形状为 (n_samples,)
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        try:
            # 确保输入在正确设备
            X_test = self.device_manager.ensure_tensor_device(X)
            
            # 🔍 设备调试信息
            # logging.info(f"🔍 [GP_DEBUG] 输入X设备: {X_test.device}, 形状: {X_test.shape}")
            # logging.info(f"🔍 [GP_DEBUG] device_manager.device: {self.device_manager.device}")
            
            if X_test.ndim == 1:
                X_test = X_test.unsqueeze(0)
            
            # 🔧 关键修复：调试信息 - 检查输入数据的差异性
            logging.debug(f"GP预测输入: 形状={X_test.shape}, 范围=[{X_test.min():.6f}, {X_test.max():.6f}]")
            
            # 检查输入是否所有点都相同
            if X_test.shape[0] > 1:
                input_variance = torch.var(X_test, dim=0)
                logging.debug(f"GP输入方差: {input_variance.mean():.8f}")
                if input_variance.mean() < 1e-10:
                    logging.warning(f"⚠️ GP输入数据方差过小，所有候选点几乎相同！")
            
            # 🔧 关键修复：使用训练时的标准化参数对测试数据进行标准化
            X_normalized = (X_test - self.X_min) / self.X_range
            
            # 检查标准化后的数据
            logging.debug(f"GP标准化后: 形状={X_normalized.shape}, 范围=[{X_normalized.min():.6f}, {X_normalized.max():.6f}]")
            if X_normalized.shape[0] > 1:
                norm_variance = torch.var(X_normalized, dim=0)
                logging.debug(f"GP标准化后方差: {norm_variance.mean():.8f}")
                if norm_variance.mean() < 1e-10:
                    logging.warning(f"⚠️ GP标准化后数据方差过小，可能导致预测相同！")
            
            # 设置模型为评估模式
            self.model.eval()
            
            # 预测（保持设备一致）
            self.model.eval()
            with torch.no_grad(), gpytorch.settings.fast_pred_var():
                posterior = self.model(X_normalized)
                mean = posterior.mean
                
                # 确保predictions始终是1维张量
                if mean.ndim > 1:
                    predictions = mean.squeeze(-1) * self.y_std + self.y_mean
                else:
                    predictions = mean * self.y_std + self.y_mean
                
                if predictions.ndim == 0:
                    predictions = predictions.unsqueeze(0)
            
            # 🔧 调试信息：检查预测结果
            # logging.debug(f"GP预测结果: {predictions}, 形状: {predictions.shape}")
            # if predictions.numel() > 1:
            #     pred_variance = torch.var(predictions)
            #     logging.debug(f"GP预测方差: {pred_variance:.8f}")
            #     if pred_variance < 1e-8:
            #         logging.warning(f"⚠️ GP预测值方差过小，所有预测几乎相同：{predictions[0]:.6f}")
            
            return predictions  # 已在正确设备上
            
        except Exception as e:
            logging.error(f"GP预测失败: {e}")
            raise e
    
    def predict_quality(self, X: torch.Tensor) -> torch.Tensor:
        """
        预测质量分数（用于与TabPFN接口兼容）
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            
        Returns:
            质量分数，形状为 (n_samples,)，值越小表示质量越好
        """
        # 对于最小化问题，直接返回预测值
        predictions = self.predict(X)
        return predictions
    
    def update(self, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        增量更新模型（重新训练）
        
        Args:
            X_new: 新的输入特征 - 可能是完整数据集或新增数据
            y_new: 新的目标值 - 可能是完整数据集或新增数据
        """
        if not self.is_fitted:
            # 如果模型未训练，直接训练
            self.fit(X_new, y_new)
            return
        
        try:
            # 转换新数据使用设备管理器
            X_new_tensor = self.device_manager.safe_to_device(X_new)
            y_new_tensor = self.device_manager.safe_to_device(y_new)
            
            # 确保数据形状正确
            if X_new_tensor.ndim == 1:
                X_new_tensor = X_new_tensor.unsqueeze(0)
            if y_new_tensor.ndim > 1:
                y_new_tensor = y_new_tensor.flatten()
            
            # 🎯 关键修复：检测是否为完整数据集更新，避免重复累积
            if hasattr(self, 'X_min') and hasattr(self, 'train_x'):
                # 反标准化旧数据以进行比较
                X_old_denormalized = self.train_x * self.X_range + self.X_min
                y_old_denormalized = self.train_y * self.y_std + self.y_mean
                
                current_samples = len(X_old_denormalized)
                new_samples = len(X_new_tensor)
                
                # 📊 检测数据更新类型
                if new_samples >= current_samples:
                    # 传入的可能是完整数据集（包含历史数据）
                    logging.debug(f"GP检测到完整数据集更新: 当前{current_samples}样本 -> 输入{new_samples}样本")
                    
                    # 检查前N个样本是否与历史数据相同（允许小误差）
                    if new_samples >= current_samples:
                        # 取前current_samples个样本进行比较
                        X_prefix = X_new_tensor[:current_samples]
                        y_prefix = y_new_tensor[:current_samples]
                        
                        # 计算差异
                        X_diff = torch.norm(X_prefix - X_old_denormalized, dim=1).mean()
                        y_diff = torch.norm(y_prefix - y_old_denormalized).item()
                        
                        if X_diff < 1e-6 and y_diff < 1e-6:
                            # 确认是完整数据集更新，直接用新数据重新训练
                            # logging.info(f"GP确认完整数据集更新，直接重新训练，样本数: {new_samples}")
                            self.fit(X_new_tensor, y_new_tensor)
                            return
                
                # 否则作为新增数据处理（原有逻辑）
                logging.debug(f"GP作为增量数据处理: {current_samples} + {new_samples} = {current_samples + new_samples}")
                X_combined = torch.cat([X_old_denormalized, X_new_tensor], dim=0)
                y_combined = torch.cat([y_old_denormalized, y_new_tensor], dim=0)
            else:
                # 首次训练
                X_combined = X_new_tensor
                y_combined = y_new_tensor
            
            # 重新训练模型
            self.fit(X_combined, y_combined)
            
            # logging.info(f"GP模型更新完成，总样本数: {len(X_combined)}")
            
        except Exception as e:
            logging.error(f"GP模型更新失败: {e}")
            raise e
    
    def get_acquisition_function(self, best_f: float = None):
        """
        获取采集函数（用于优化）
        
        Args:
            best_f: 当前最佳函数值
            
        Returns:
            采集函数
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        
        if best_f is None:
            # 使用训练数据中的最佳值
            best_f = self.train_y.min().item()
        else:
            # 标准化最佳值
            best_f = (best_f - self.y_mean) / self.y_std
        
        return ExpectedImprovement(model=self.model, best_f=best_f)
    
    def optimize_acquisition(self, bounds: torch.Tensor, num_restarts: int = 10, raw_samples: int = 512):
        """
        优化采集函数找到下一个评估点
        
        Args:
            bounds: 变量边界，形状为 (2, n_features)
            num_restarts: 重启次数
            raw_samples: 原始样本数
            
        Returns:
            最优候选点
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        
        # 获取采集函数
        acq_func = self.get_acquisition_function()
        
        # 优化采集函数
        candidate, acq_value = optimize_acqf(
            acq_function=acq_func,
            bounds=bounds,
            q=1,
            num_restarts=num_restarts,
            raw_samples=raw_samples,
        )
        
        return candidate.squeeze(0)
    
    def get_feature_importance(self) -> np.ndarray:
        """
        获取特征重要性（基于GP的lengthscale）
        
        Returns:
            特征重要性数组
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        
        try:
            # 获取lengthscale参数
            lengthscales = self.model.covar_module.base_kernel.lengthscale.detach().cpu().numpy()
            
            # 重要性与lengthscale成反比
            importance = 1.0 / (lengthscales + 1e-6)
            importance = importance / importance.sum()  # 归一化
            
            return importance.flatten()
        except Exception as e:
            logging.warning(f"无法获取GP特征重要性: {e}")
            return np.ones(self.axus.target_dim) / self.axus.target_dim
