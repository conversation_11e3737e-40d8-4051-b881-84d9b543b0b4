import pandas as pd
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from tabpfn import TabPFNClassifier

# 1. 加载数据
X, y = load_iris(return_X_y=True, as_frame=True)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 2. 初始化本地 TabPFN 分类器
clf = TabPFNClassifier(device='cpu')  # 如果没有GPU可改成 'cpu'（会很慢）

# 3. 拟合模型
clf.fit(X_train, y_train)

# 4. 预测
preds = clf.predict(X_test)
print("Predictions:", preds.tolist())
print("Accuracy:", (preds == y_test).mean())


from sklearn.datasets import load_diabetes
from sklearn.model_selection import train_test_split
from tabpfn import TabPFNRegressor

# 加载数据
X, y = load_diabetes(return_X_y=True, as_frame=True)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 初始化
reg = TabPFNRegressor(device='cpu')
reg.fit(X_train, y_train)

# 预测
preds = reg.predict(X_test)
print("First 10 predictions:", preds[:10])
print("R^2 score:", reg.score(X_test, y_test))


import numpy as np
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from tabpfn import TabPFNRegressor

# 1. 生成高维黑箱函数数据
# n_features=100 表示 100 维输入
X, y = make_regression(
    n_samples=2000,      # 样本数
    n_features=100,      # 特征数（维度）
    noise=0.2,           # 噪声
    random_state=42
)

# 2. 划分训练 / 测试集
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# 3. 初始化 TabPFN 回归器（本地 GPU）
reg = TabPFNRegressor(device='cuda')  # 如果没有GPU可改成 'cpu'

# 4. 拟合
reg.fit(X_train, y_train)

# 5. 预测 & 评估
preds = reg.predict(X_test)
r2 = reg.score(X_test, y_test)

print("First 5 predictions:", preds[:5])
print("R^2 score:", r2)

import numpy as np
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from tabpfn import TabPFNRegressor
import os

# ==== 参数设置 ====
n_samples = 400   # 样本数（≤1000，CPU 可直接跑）
n_features = 100  # 特征数（维度）
use_gpu = False   # 改成 True 则用 GPU

# 如果改成 CPU 大样本（>1000），需要加这一行：
# os.environ["TABPFN_ALLOW_CPU_LARGE_DATASET"] = "1"

# 1. 生成高维黑箱函数数据
X, y = make_regression(
    n_samples=n_samples,
    n_features=n_features,
    noise=0.2,
    random_state=42
)

# 2. 划分训练 / 测试集
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# 3. 初始化 TabPFN 回归器
device = 'cuda' if use_gpu else 'cpu'
reg = TabPFNRegressor(device=device)

# 4. 拟合模型
reg.fit(X_train, y_train)

# 5. 预测 & 评估
preds = reg.predict(X_test)
r2 = reg.score(X_test, y_test)

print("First 5 predictions:", preds[:5])
print("R^2 score:", r2)

