# nihao1
AckleyEffectiveDim, ShiftedAckley10, RosenbrockEffectiveDim 等
所有参数都是连续的（ParameterType.CONTINUOUS）
离散变量基准测试：
MaxSat60, MaxSat125, Labs 等
所有参数都是离散的（如 ParameterType.BINARY, ParameterType.CATEGORICAL）
混合变量基准测试：
SVMMixed, Ackley53 等
同时包含连续和离散参数


假设目前我需要执行Ackley53问题：
1、main.py文件中的    bounce = Bounce()；gin文件中会初始化benchmark对象
2、接着会执行Bounce类的初始化。
    bounce.py的__init__方法，传入一些必要的参数例如benchmark对象，还会新建一个AXUS对象以及TR对象。
        all_target_dims存放所有可能的维度，例如 = tensor([ 5, 10, 20, 40, 80])
        _all_split_budgets一个字典，存放所有维度对应的预算{5: 3, 10: 6, 20: 12, 40: 25, 80: 200}
    AXUS对象中会存放一个字典_param_indices，存放变量名对应在高维空间中的索引、当前程序中的bin数量
        在AXUS对象初始化时，还会调用一个 _reset 方法用于该方法负责创建和初始化 bin，假设初始化5维，也就是一开始5个bin，那么就是这个方法分配那些bin存储连续变量，那些bin存储离散变量
            具体流程就是计算每种类型的变量有多少个，然后按比例分配bin的数量，假设现在1个bin存储连续，4个bin存储离散，那么会将离散变量随机分为4组。
    TR对象中有一个dimensionality属性记录当前维度。
    bin对象的变量中比较重要的就是当前bin需要多少维度（one-hot变量）
3、目前为止所有的初始化操作算是结束了，接下来    bounce.run()这一步
4、然后会进入self.sample_init()，开始初步的采样，在这个采样过程中，
    有一个比较重要的变量 indices_of_type ：创建一个大的 一维 Tensor，把所有索引拼接起来，就是当前类型（二分，连续，分类）所涉及的维度
    遍历所有benchmark涉及到的变量类型，拿到他们涉及到的bin。然后在indices_of_type创建这个bin的维度。
    举个例子：还是之前那个例子，现在1个bin存储连续，4个bin存储离散，对于每个类型，创建一个 indices_of_type 存储所有维度，然后根据不同类型调用不同采样函数
        返回值都是x_init（采样数，取值），每个类型都有一个 indices_of_type 之后，会汇总到 types_points_and_indices 字典[变量类型：（采样点，每个维度的取值）（二维张量）]
    之后拿到了 types_points_and_indices 之后，需要所有变量类型组合成一个完整的点。
5、进入 construct_mixed_point（） 方法组合一个完整点。
6、进入 from_1_around_origin（） 方法将低维点投影为原始决策空间的点，后续进行评估，存储点和评估值，到这里第一步初始采样就结束了。
7、之后呢用这些数据训练GP模型，然后进入优化阶段了。
8、我们的问题是一个混合问题，优化阶段呢，需要先把离散变量进行优化，然后在选出的点上，把连续变量进行优化。所以第一步是先得出连续变量的索引（低维空间上，最开始当然就是0，其余1，2，3，4就是离散的bin）。在混合优化中，有一个参数 n_interleaved 表示交替优化的轮次，也就是离散-连续（1轮）-离散-连续。。。。
9、进入 n_interleaved 循环，每次循环会执行 create_candidates_discrete（） 方法生成候选解。详细讲讲：
    这个方法开始会找到最小目标值对应的点作为TR中心点，如果这不是第一次优化，也就是说之前会有一个最优点，那么连续变量部分会直接使用这个最优点的部分（玄学）。
    然后得到需要的候选点数量，在TR内随机生成候选点，之后还会得到一批和最优点为邻居的候选点，然后去重。





































关于将bounce运用进化算法优化的一些想法（一般就是想到啥写啥）：
    关于初始化最开始分组，有没有办法让其分组合理一些？这样能够保证
    关于填充采样：
        1、选择 GP 预测方差最大的点进行评估，以提升模型在不熟悉区域的预测能力。
        2、在优化和不确定性之间采用权衡
        3、动态调整采样数
        4、batch_size=k 时，选择部分来自不确定性最大点，另一些来自预测目标最优点；
    
    关于交替优化：
        如果交替优化过一定次数之后最优值没有改变，那就改变策略，GA初始化种群的时候从交替优化最优点附近生成。

    种群运行多代，最优的一直都没变，而且运行多代之后，种群中的解几乎都是一样，是否和代理模型？


    非常容易陷入局部最优：
        原因可能有：每次迭代都是在当前TR中心生成的候选点，所以多样性不足，因此是否可以引入多个TR？
        
            × 效果不好，每个TR我都建立了一个代理模型，效果不太行！！！！！！！！！！

            1、只在第一次执行循环时，找到一个最优，一个离最优最远的点，一个GP认为方差最大的点作为TR中心点。
            2、然后根据每一个TR中心点创建一个TR区域，其中需要维护自己TR的局部数据以及TR中心，TR中心会更新的，如果找到的点比最优中心点目标值更优就进行替换。
            3、3个TR每次选的点根据GP来选择，没选中的会存放在自己TR区域中，每个TR区域自己进行优化。


        每次迭代观察种群状态，如果太过于集中需要对其进行额外处理
        这里有两个问题
            1、用什么标准判断种群处于什么状态
            2、如何对种群进行多样性补充












梳理一下逻辑：
    while FE < FEmax:
        训练一个代理模型(局部数据训练的)
        交替优化5次：
            执行离散优化：
                在当前TR中心点附近生成种群
                接下来进行种群迭代：
                    选择种群中的精英解；
                    精英解生成子代（如何生成）
                    子代种群和父代种群合并，替换原种群
            执行连续优化
                将上一次离散优化得到的点离散部分固定，然后优化连续部分
            



























        





1. AckleyEffectiveDim
维度：默认 200（可配置）
有效维度：默认 10
变量类型：连续（Continuous）
1. ShiftedAckley10
维度：默认 200
有效维度：10
变量类型：连续（Continuous）
1. RosenbrockEffectiveDim
维度：默认 200
有效维度：10
变量类型：连续（Continuous）
1. HartmannEffectiveDim
维度：默认 200
有效维度：6
变量类型：连续（Continuous）
1. BraninEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
1. LevyEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
1. DixonPriceEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
8. GriewankEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
1. MichalewiczEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
1.  RastriginEffectiveDim
维度：默认 200
有效维度：2
变量类型：连续（Continuous）
1.  SVM
维度：388
变量类型：连续（Continuous）
1.  SVMMixed
维度：n_features（二进制）+ 3（连续），默认 n_features=50，即 53
变量类型：混合（二进制+连续，Binary + Continuous）
1.  LassoDNA
维度：180
变量类型：连续（Continuous）
1.  LassoSimple
维度：60
变量类型：连续（Continuous）
1.  LassoMedium
维度：100
变量类型：连续（Continuous）
1.  LassoHigh
维度：300
变量类型：连续（Continuous）
1.  LassoHard
维度：1000
变量类型：连续（Continuous）
1.  Mopta08
维度：124
变量类型：连续（Continuous）
1.  MaxSat60
维度：60
变量类型：二进制（Binary）
1.  MaxSat125
维度：125
变量类型：二进制（Binary）
1.  Ackley53
维度：53（50 二进制 + 3 连续）
变量类型：混合（二进制+连续，Binary + Continuous）
1.  Contamination
维度：默认 25
变量类型：二进制（Binary）
1.  Labs
维度：默认 50
变量类型：二进制（Binary）
1.  PestControl
维度：默认 25
变量类型：多分类（Categorical，one-hot 编码）

代理模型精度

======= Surrogate 模型最终评估 =======
初始点数       : 5                      50
平均绝对误差   : 47.620126              80.479859
均方根误差     : 50.413490              81.541262
======================================

======= Surrogate 模型最终评估 =======
初始点数       :  5                      50
平均绝对误差   : 731.514064             1121.114909
均方根误差     : 809.537162             1238.382887
======================================

初始点给多了反而效果不好，可能的原因就是初始点多了之后范围太广了，用于训练代理模型可能不适合优化过程的局部搜索。

这样测精度有用吗？不是只要知道大致曲线就可以了嘛？

局部，全局代理模型····· 
全局代理找到一个点作为TR中心，然后用于局部搜索；难点在于局部搜索的代理模型用哪些数据训练，数据量也不，直接暴力了，用每一个解取匹配当前TR，看看有没有超过当前范围，没超过就加入集合，然后结束之后用这些解来训练代理模型，如果数量不足，就是用全局代理模型替代
其次，(?问题是这些点我已经全部真实评估过了啊，我为什么还要用全局代理模型选一个点去局部优化？)，就是如何进行全局搜索呢，原来其实是基本上没有全局搜索这个点的，就在于一开始初始化5个点，这就算是全局搜索了。

就是说原来的算法中几乎是没有全局搜索的，我只能说一开始的初始化可能就是全局搜索了，之后算法就一直在局部搜索。所以可以解释为什么初始点一多，代理模型效果反而不好了。
如何加上全局搜索可能是一个点。很容易想到的就是，在分裂bin的时候再给一次随机点，就用此作为全局搜索的手段。为什么有这个想法呢。很简单嘛，假如问题有个60维，一开始只初始化5个bin，也就是5维，当当前预算花光之后并且假设分裂1一个bin，那么现在就有10维了，维度上升很快，但是就之前那些点，覆盖的范围很少，所以呢就直接在分裂的时候全局搜索，这时候用于局部和全局代理模型训练的数据都有了，但是，昂贵问题本身就没有多少预算，如果每次分裂还要用一些的话，emmm，反正就是不知道效果如何。还有目前来说我知道的全局搜索的手段比较少，可能需要多看一些论文了。

还有一个点，但是实现起来应该难度很大。就这么说，原来的算法只有一个TR，并且每次都是选择当前已经真实评估点中最好的作为TR中心（EI采集函数），也很好想到啊，原来算法不是会陷入局部最优嘛，现在直接使用两个TR不就好了，或者更多也行啊，第三个就随机一个点嘛。就是一个不变嘛，第二个TR的话就是用GP模型方差最大的点，但是也不能不考虑当前的预测值，那有涉及到一个权重的问题了，方差和预测值。这个点实现起来很复杂，这样的话会有很多问题，比如说是否需要每一个TR都需要代理模型呢？还是直接使用全局代理模型？

pfn

修改算法使用进化算法，进化算法一次迭代选择一个点
有了，不是找不到全局搜索嘛，就是用遗传算法来全局搜索，这里的代理模型使用tabPFN，选择的点就不动原来的算法了，用于局部优化，这时候速度也很快。

已经实现了，目前来说还有几点改进的地方。组会上说的tabpfn只能在区域内有较好的表现，但是区域外的预测趋向于均值，不太好作为全局代理模型，所以将局部代理模型改为tabpfn，然后全局代理模型可以换一个。