import logging
import torch
import numpy as np
import math
from typing import Dict, List, Tuple, Optional, Union
from functools import lru_cache
from bounce.surrogate_manager import SurrogateManager
from bounce.device_manager import DeviceManager


class WeightManager:
    """权重管理器 - 基于预测准确度的动态权重调整"""
    
    def __init__(self, 
                 model_names: List[str], 
                 window_size: int = 50,
                 min_weight_protection: float = 0.1,
                 performance_beta: float = 5.0):
        """
        初始化权重管理器
        
        Args:
            model_names: 模型名称列表
            window_size: 性能评估滑动窗口大小
            min_weight_protection: 最小权重保护值
            performance_beta: 性能评估温度参数
        """
        self.model_names = model_names
        self.window_size = window_size
        self.min_weight_protection = min_weight_protection
        self.performance_beta = performance_beta
        
        # 初始化权重为均分
        initial_weight = 1.0 / len(model_names)
        self.weights = {name: initial_weight for name in model_names}
        
        # 性能历史记录 {model_name: [(predicted, actual), ...]}
        self.performance_history = {name: [] for name in model_names}
        
        logging.info(f"权重管理器初始化完成，模型: {model_names}")
        logging.info(f"初始权重: {self.weights}")
    
    def record_prediction(self, model_name: str, predicted: float, actual: float) -> None:
        """
        记录模型预测与实际结果 - 增强滑动窗口监控版
        
        Args:
            model_name: 模型名称
            predicted: 预测值
            actual: 实际值
        """
        if model_name not in self.performance_history:
            logging.warning(f"⚠️ 未知模型名称: {model_name}，跳过性能记录")
            return
        
        # 📊 数据验证
        if not isinstance(predicted, (int, float)) or not isinstance(actual, (int, float)):
            logging.warning(f"⚠️ {model_name}性能记录数据类型错误: pred={type(predicted)}, actual={type(actual)}")
            return
            
        if math.isnan(predicted) or math.isnan(actual) or math.isinf(predicted) or math.isinf(actual):
            logging.warning(f"⚠️ {model_name}性能记录数据无效: pred={predicted}, actual={actual}")
            return
        
        # 记录当前数据量（添加前）
        before_count = len(self.performance_history[model_name])
        
        # 添加新数据
        self.performance_history[model_name].append((predicted, actual))
        
        # 🔧 关键修复：增强滑动窗口监控
        after_count = len(self.performance_history[model_name])
        
        # 🎯 滑动窗口机制：当数据超过窗口大小时移除最旧的数据
        if after_count > self.window_size:
            removed = self.performance_history[model_name].pop(0)
            final_count = len(self.performance_history[model_name])
            
            # 🔧 重要：将滑动窗口操作提升为INFO级别，确保可见
            logging.info(f"📈 {model_name.upper()}滑动窗口运作: 移除旧数据{removed}, {before_count}→{after_count}→{final_count} (窗口大小:{self.window_size})")
        else:
            # 记录窗口填充进度
            fill_progress = after_count / self.window_size * 100
            logging.debug(f"📊 {model_name}窗口填充: {after_count}/{self.window_size} ({fill_progress:.1f}%)")
        
        # 🔧 详细调试信息：记录性能数据
        error = abs(predicted - actual)
        logging.debug(f"📊 {model_name}性能记录成功: 预测={predicted:.4f}, 实际={actual:.4f}, 误差={error:.4f}")
        
        # 🔧 实时监控性能记录状态 - 改进版
        current_count = len(self.performance_history[model_name])
        
        # 在关键节点输出状态信息
        if current_count == self.window_size:
            logging.info(f"🎯 {model_name.upper()}窗口已满: {current_count}条记录，后续将启动滑动窗口机制")
        elif current_count % 10 == 0:  # 每10条记录输出一次状态
            logging.debug(f"📈 {model_name}性能历史: {current_count}条记录，最新误差: {error:.4f}")
        elif current_count % 5 == 0 and current_count <= 20:  # 前20条记录时每5条输出一次
            logging.debug(f"📈 {model_name}性能历史: {current_count}条记录，最新误差: {error:.4f}")
    
    def get_window_status(self) -> Dict[str, Dict[str, int]]:
        """
        获取所有模型的窗口状态信息
        
        Returns:
            各模型的窗口状态 {model_name: {current: int, max: int, progress: float}}
        """
        status = {}
        for model_name in self.model_names:
            current = len(self.performance_history[model_name])
            progress = current / self.window_size * 100
            status[model_name] = {
                'current': current,
                'max': self.window_size,
                'progress': progress,
                'is_full': current >= self.window_size
            }
        return status
    
    def log_window_status(self) -> None:
        """
        输出所有模型的窗口状态
        """
        status = self.get_window_status()
        logging.info(f"📊 滑动窗口状态汇总:")
        for model_name, info in status.items():
            status_icon = "🟢" if info['is_full'] else "🟡"
            logging.info(f"   {status_icon} {model_name.upper()}: {info['current']}/{info['max']} ({info['progress']:.1f}%)")
    
    @lru_cache(maxsize=128)
    def _calculate_mae_cached(self, model_name: str, history_hash: int) -> float:
        """
        缓存版本的MAE计算
        
        Args:
            model_name: 模型名称
            history_hash: 历史记录的哈希值，用于缓存失效
            
        Returns:
            平均绝对误差
        """
        history = self.performance_history[model_name]
        errors = [abs(pred - actual) for pred, actual in history]
        return sum(errors) / len(errors) if errors else 0.0
    
    def calculate_mae(self, model_name: str) -> float:
        """
        计算模型的平均绝对误差
        
        Args:
            model_name: 模型名称
            
        Returns:
            平均绝对误差，数据不足时返回0.5（中等误差）
        """
        history = self.performance_history[model_name]
        if len(history) < 2:  # 🔧 修复：降低阈值从3到2，使权重更新更敏感
            logging.debug(f"{model_name}性能数据不足({len(history)})，使用默认MAE")
            return 0.5
        
        # 使用缓存版本的计算
        history_hash = hash(str([(pred, actual) for pred, actual in history]))
        mae = self._calculate_mae_cached(model_name, history_hash)
        logging.debug(f"{model_name} MAE计算: {len(history)}个样本, MAE={mae:.6f}")
        return mae
    
    @lru_cache(maxsize=64)
    def _compute_weights_cached(self, model_data_hash: int, min_weight_protection: float) -> tuple:
        """
        缓存版本的权重计算
        
        Args:
            model_data_hash: 模型数据的哈希值
            min_weight_protection: 最小权重保护值
            
        Returns:
            计算后的权重字典和其他相关信息
        """
        # 由于这个函数需要访问实例变量，我们不能直接缓存整个函数
        # 但我们可以在update_weights中优化重复计算
        pass
    
    def update_weights(self) -> None:
        """基于性能历史更新权重 - 温和调整版本"""
        # 记录更新前的权重
        old_weights = self.weights.copy()
        
        # 🔧 新增：输出窗口状态
        self.log_window_status()
        
        # 计算各模型的MAE
        model_maes = {}
        valid_data_counts = {}
        for model_name in self.model_names:
            model_maes[model_name] = self.calculate_mae(model_name)
            valid_data_counts[model_name] = len(self.performance_history[model_name])
        
        # 🔧 调试信息：显示所有模型的MAE和数据量
        logging.debug(f"📈 模型MAE: {model_maes}")
        logging.debug(f"📊 数据量: {valid_data_counts}")
        
        # 🎯 温和权重调整算法
        # 检查是否有足够的性能数据进行调整
        models_with_data = [name for name in self.model_names 
                           if len(self.performance_history[name]) >= 2]  # 需要至少2个数据点
        
        if len(models_with_data) >= 2:  # 至少有两个模型有数据才进行调整
            logging.debug(f"📊 {len(models_with_data)}个模型有足够性能数据，进行温和权重调整")
            
            # 🎯 温和算法：基于相对性能的适度权重调整
            # 1. 获取有效的MAE值（排除默认值0.5）
            effective_maes = {name: model_maes[name] for name in models_with_data 
                            if model_maes[name] != 0.5}
            
            if len(effective_maes) >= 2:
                # 2. 计算性能差异
                mae_values = list(effective_maes.values())
                best_mae = min(mae_values)
                worst_mae = max(mae_values)
                mae_range = worst_mae - best_mae
                
                logging.debug(f"📈 性能排序: {sorted([(name, mae) for name, mae in effective_maes.items()], key=lambda x: x[1])}")
                logging.debug(f"📈 MAE范围: 最佳={best_mae:.4f}, 最差={worst_mae:.4f}, 差异={mae_range:.4f}")
                
                # 🎯 温和权重计算：只在显著差异时调整
                if mae_range > best_mae * 0.05:  # 5%的相对差异才调整
                    logging.info(f"🔄 检测到性能差异({mae_range:.4f})，启动温和权重调整")
                    
                    # 温和的权重调整算法
                    raw_weights = {}
                    for model_name in self.model_names:
                        mae = model_maes[model_name]
                        data_count = valid_data_counts[model_name]
                        
                        if data_count < 2:
                            # 数据不足的模型给予中等权重
                            raw_weights[model_name] = 1.0
                        elif mae == 0.5:  # 默认MAE
                            raw_weights[model_name] = 1.0
                        else:
                            # 🎯 温和权重函数：基于相对性能但不过度惩罚
                            relative_performance = mae / best_mae
                            
                            # 使用更温和的权重函数
                            if relative_performance <= 1.0:
                                # 最佳模型：适度提升权重
                                raw_weights[model_name] = math.exp(0.5 * (2.0 - relative_performance))
                            else:
                                # 较差模型：温和降低权重
                                raw_weights[model_name] = math.exp(-1.0 * (relative_performance - 1.0))
                            
                            # 数据量加权（更温和）
                            data_bonus = min(data_count / 20.0, 1.2)  # 最多1.2倍加成，更保守
                            raw_weights[model_name] *= data_bonus
                    
                    logging.debug(f"📊 温和权重: {raw_weights}")
                else:
                    logging.debug(f"📊 性能差异较小({mae_range:.4f})，保持当前权重")
                    # 差异不够大，保持现有权重
                    raw_weights = old_weights.copy()
            else:
                logging.debug(f"📊 有效MAE数据不足({len(effective_maes)})，使用标准权重")
                # 使用标准指数衰减
                raw_weights = {}
                for model_name in self.model_names:
                    mae = model_maes[model_name]
                    raw_weights[model_name] = math.exp(-2.0 * mae)  # 温和的指数衰减
        else:
            logging.debug(f"📊 有效数据不足({len(models_with_data)})，保持均匀权重")
            # 数据不足，保持均匀权重
            raw_weights = {name: 1.0 for name in self.model_names}
        
        # 🔧 调试信息：显示原始权重
        logging.debug(f"📉 原始权重: {raw_weights}")
        
        # 归一化权重
        total_weight = sum(raw_weights.values())
        if total_weight > 1e-8:
            normalized_weights = {name: w / total_weight for name, w in raw_weights.items()}
        else:
            # 备选：均分权重
            normalized_weights = {name: 1.0 / len(self.model_names) for name in self.model_names}
        
        # 🎯 恢复原始最小权重保护（0.1）
        for model_name in self.model_names:
            self.weights[model_name] = max(self.min_weight_protection, normalized_weights[model_name])
        
        # 重新归一化
        total_weight = sum(self.weights.values())
        for model_name in self.model_names:
            self.weights[model_name] /= total_weight
        
        # 📊 输出权重变化信息（降低敏感度）
        has_any_data = any(len(self.performance_history[name]) > 0 for name in self.model_names)
        
        if has_any_data:
            # 检测权重变化（提高阈值到0.01即1%）
            max_weight_change = max(abs(self.weights[model_name] - old_weights[model_name]) 
                                  for model_name in self.model_names)
            
            if max_weight_change > 0.01:  # 1%的变化才显示
                logging.info(f"🔄 权重适度调整 (最大变化: {max_weight_change:.5f}):")
            else:
                logging.info(f"🔄 权重保持稳定 (最大变化: {max_weight_change:.5f}):")
            
            # 🎯 详细权重变化信息
            for model_name in self.model_names:
                old_w = old_weights[model_name]
                new_w = self.weights[model_name]
                mae = model_maes[model_name]
                data_count = valid_data_counts[model_name]
                change = new_w - old_w
                
                # 更合理的变化符号
                if abs(change) < 0.005:  # 0.5%以下视为稳定
                    change_symbol = "→"
                elif change > 0.01:  # 1%以上视为显著上升
                    change_symbol = "↗"
                elif change > 0:
                    change_symbol = "↑"
                elif change < -0.01:  # 1%以上视为显著下降
                    change_symbol = "↘"
                else:
                    change_symbol = "↓"
                    
                logging.info(f"   {model_name.upper()}: {old_w:.4f} {change_symbol} {new_w:.4f} (MAE:{mae:.4f}, 数据:{data_count})")
        else:
            logging.debug(f"权重更新完成: {self.weights}")
    
    def get_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.weights.copy()


class CandidateSelector:
    """候选点选择器 - Min-Max归一化、加权评分、Top-K选择和Softmax采样"""
    
    def __init__(self, 
                 top_k: int = 5,
                 softmax_temperature: float = 5.0):
        """
        初始化候选点选择器
        
        Args:
            top_k: 最终候选点数量
            softmax_temperature: Softmax采样温度参数
        """
        self.top_k = top_k
        self.softmax_temperature = softmax_temperature
    
    def normalize_predictions(self, predictions: torch.Tensor) -> torch.Tensor:
        """
        Min-Max归一化预测值
        
        Args:
            predictions: 预测值 [n_samples]
            
        Returns:
            归一化后的预测值 [n_samples]
        """
        pred_min = predictions.min()
        pred_max = predictions.max()
        
        if pred_max == pred_min:
            # 如果所有预测值相同，返回0.5
            # 🔧 关键修复：保持输入的设备和数据类型
            return torch.full_like(predictions, 0.5)
        
        # 🔧 关键修复：确保返回的归一化结果保持原始设备和数据类型
        normalized = (predictions - pred_min) / (pred_max - pred_min)
        return normalized.to(device=predictions.device, dtype=predictions.dtype)
    
    def calculate_weighted_scores(self, 
                                  predictions_dict: Dict[str, torch.Tensor], 
                                  weights: Dict[str, float]) -> torch.Tensor:
        """
        计算加权评分
        
        Args:
            predictions_dict: 各模型的预测结果 {model_name: predictions}
            weights: 模型权重 {model_name: weight}
            
        Returns:
            加权评分 [n_samples]
        """
        # 优化：向量化加权评分计算，减少循环
        if not predictions_dict:
            raise ValueError("预测字典为空")
            
        # 🔧 关键修复：确定目标设备（使用第一个预测结果的设备）
        first_prediction = list(predictions_dict.values())[0]
        target_device = first_prediction.device
        n_samples = len(first_prediction)
        
        # 🔧 关键修复：在正确设备上创建weighted_scores
        weighted_scores = torch.zeros(n_samples, device=target_device, dtype=first_prediction.dtype)
        
        # 归一化各模型预测并计算加权和
        model_names = list(predictions_dict.keys())
        weights_list = [weights.get(name, 0.0) for name in model_names]
        
        # 批量处理预测结果
        normalized_preds = []
        for model_name in model_names:
            predictions = predictions_dict[model_name]
            normalized_pred = self.normalize_predictions(predictions)
            # 🔧 关键修复：确保norm_pred在正确设备上
            normalized_pred = normalized_pred.to(device=target_device, dtype=weighted_scores.dtype)
            normalized_preds.append(normalized_pred)
        
        # 向量化加权计算
        if normalized_preds:
            # 堆叠所有归一化预测结果
            stacked_preds = torch.stack(normalized_preds)  # [n_models, n_samples]
            weights_tensor = torch.tensor(weights_list, device=target_device, dtype=weighted_scores.dtype)  # [n_models]
            
            # 向量化加权求和
            weighted_scores = torch.sum(stacked_preds * weights_tensor.unsqueeze(1), dim=0)
        
        return weighted_scores
    
    def softmax_sampling(self, scores: torch.Tensor) -> int:
        """
        Softmax采样选择索引
        
        Args:
            scores: 评分数组
            
        Returns:
            选中的索引
        """
        # 应用温度参数
        scaled_scores = self.softmax_temperature * scores
        
        # 计算softmax概率
        exp_scores = torch.exp(scaled_scores - scaled_scores.max())  # 数值稳定性
        probabilities = exp_scores / exp_scores.sum()
        
        # 采样
        return torch.multinomial(probabilities, 1).item()
    
    def select_best_candidate(self, 
                             candidates: torch.Tensor,
                             predictions_dict: Dict[str, torch.Tensor], 
                             weights: Dict[str, float]) -> torch.Tensor:
        """
        选择最佳候选点
        
        Args:
            candidates: 候选点 [n_candidates, n_dims]
            predictions_dict: 各模型预测 {model_name: predictions}
            weights: 模型权重 {model_name: weight}
            
        Returns:
            选中的候选点 [n_dims]
        """
        logging.debug(f"🔍 开始候选点选择过程，候选点数: {len(candidates)}")
        
        # 🔍 设备调试信息
        # logging.info(f"🔍 [SELECTOR_DEBUG] candidates设备: {candidates.device}")
        # for model_name, predictions in predictions_dict.items():
        #     logging.info(f"🔍 [SELECTOR_DEBUG] {model_name}预测设备: {predictions.device}")
        
        # 计算加权评分
        weighted_scores = self.calculate_weighted_scores(predictions_dict, weights)
        
        # logging.info(f"🔍 [SELECTOR_DEBUG] weighted_scores设备: {weighted_scores.device}")
        
        # Top-K选择
        if len(weighted_scores) <= self.top_k:
            top_indices = torch.arange(len(weighted_scores))
            top_scores = weighted_scores
        else:
            top_scores, top_indices = torch.topk(weighted_scores, self.top_k, largest=True)
        
        logging.debug(f"📈 Top-{self.top_k}候选点评分: {top_scores.tolist()}")
        logging.debug(f"📈 Top-{self.top_k}候选点索引: {top_indices.tolist()}")
        
        # Softmax采样从Top-K中选择
        selected_idx_in_topk = self.softmax_sampling(top_scores)
        selected_idx = top_indices[selected_idx_in_topk]
        
        logging.debug(f"🎯 Softmax采样选中: Top-{self.top_k}中的第{selected_idx_in_topk}个，全局索引{selected_idx}")
        
        selected_candidate = candidates[selected_idx]
        
        # 🔧 关键修复：确保返回的候选点保持原始设备
        # logging.info(f"🔍 [SELECTOR_DEBUG] 选中候选点设备: {selected_candidate.device}")
        
        return selected_candidate


class EnsembleManager:
    """集成代理模型管理器 - 统一管理三个代理模型(GP/TabPFN/RBF)"""
    
    def __init__(self, 
                 benchmark, 
                 axus, 
                 device: str = 'cpu',
                 model_types: List[str] = None,
                 weight_window_size: int = 50,
                 min_weight_protection: float = 0.1,
                 performance_beta: float = 5.0,
                 top_k_candidates: int = 5,
                 softmax_temperature: float = 5.0):
        """
        初始化集成管理器
        
        Args:
            benchmark: 基准函数
            axus: AxUS投影对象
            device: 计算设备
            model_types: 参与集成的模型类型列表
            weight_window_size: 权重计算滑动窗口大小
            min_weight_protection: 最小权重保护值
            performance_beta: 性能评估温度参数
            top_k_candidates: 最终候选点数量
            softmax_temperature: Softmax采样温度参数
        """
        self.benchmark = benchmark
        self.axus = axus
        self.device_manager = DeviceManager(device)
        
        # 默认使用三个模型
        if model_types is None:
            model_types = ['gp', 'rbf', 'tabpfn']
        self.model_types = model_types
        
        # 初始化代理模型管理器（传递设备参数）
        self.surrogate_manager = SurrogateManager(benchmark, axus, str(self.device_manager.device))
        
        # 创建三个代理模型
        self.models = {}
        for model_type in self.model_types:
            if model_type == 'tabpfn':
                self.models[model_type] = self.surrogate_manager.create_model(model_type, n_bins=5, max_samples=200)
            else:
                self.models[model_type] = self.surrogate_manager.create_model(model_type)
            logging.info(f"✅ {model_type.upper()}模型初始化完成")
        
        # 初始化权重管理器
        self.weight_manager = WeightManager(
            model_names=self.model_types,
            window_size=weight_window_size,
            min_weight_protection=min_weight_protection,
            performance_beta=performance_beta
        )
        
        # 初始化候选点选择器
        self.candidate_selector = CandidateSelector(
            top_k=top_k_candidates,
            softmax_temperature=softmax_temperature
        )
        
        # 状态标志
        self.is_fitted = False
        
        # 🎆 记录上次训练的数据大小，避免重复训练
        self.last_training_size = 0
        
        logging.info("🌟 集成代理模型管理器初始化完成")
        logging.info(f"   设备: {self.device_manager.device}")
        logging.info(f"   支持模型: {self.model_types}")
        logging.info(f"   初始权重: {self.weight_manager.get_weights()}")
    
    def fit(self, X: torch.Tensor, y: torch.Tensor) -> None:
        """
        训练所有代理模型
        
        Args:
            X: 训练特征 [n_samples, n_features]
            y: 训练目标 [n_samples]
        """
        logging.debug(f"开始训练集成模型，设备: {self.device_manager.device}, 数据大小: {len(X)}")
        
        # 确保输入数据在正确设备上
        X = self.device_manager.ensure_tensor_device(X)
        y = self.device_manager.ensure_tensor_device(y)
        
        for model_type in self.model_types:
            try:
                model = self.models[model_type]
                model.fit(X, y)
                logging.info(f"{model_type.upper()}模型训练完成")
            except Exception as e:
                logging.error(f"{model_type.upper()}模型训练失败: {e}")
        
        self.is_fitted = True
        logging.debug("集成模型训练完成")
    
    def update_models(self, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        更新所有代理模型
        
        Args:
            X_new: 新训练数据 [n_samples, n_features]
            y_new: 新训练目标 [n_samples]
        """
        # 确保输入数据在正确设备上
        X_new = self.device_manager.ensure_tensor_device(X_new)
        y_new = self.device_manager.ensure_tensor_device(y_new)
        
        for model_type in self.model_types:
            try:
                model = self.models[model_type]
                model.update(X_new, y_new)
                logging.debug(f"{model_type.upper()}模型更新完成")
            except Exception as e:
                logging.warning(f"{model_type.upper()}模型更新失败: {e}")
    
    def predict_all_models(self, X: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        使用所有模型进行预测
        
        Args:
            X: 输入特征 [n_samples, n_features]
            
        Returns:
            各模型预测结果 {model_name: predictions}
        """
        predictions = {}
        
        # 确保输入在正确设备上
        X = self.device_manager.ensure_tensor_device(X)
        
        # 关键调试：检查输入数据
        logging.debug(f"🔍 predict_all_models 输入: 设备={X.device}, 形状={X.shape}, 范围=[{X.min():.6f}, {X.max():.6f}]")
        if X.shape[0] > 1:
            input_variance = torch.var(X, dim=0)
            logging.debug(f"🔍 输入数据方差: {input_variance.mean():.8f}")
            if input_variance.mean() < 1e-10:
                logging.warning(f"⚠️ predict_all_models: 输入数据方差过小，所有候选点几乎相同！")
        
        for model_type in self.model_types:
            try:
                model = self.models[model_type]
                if model.is_fitted:
                    # logging.debug(f"🤖 使用{model_type.upper()}模型进行预测...")
                    # logging.info(f"🔍 [ENSEMBLE_DEBUG] 即将调用 {model_type}.predict(X), X设备: {X.device}")
                    
                    pred = model.predict(X)
                    
                    # logging.info(f"🔍 [ENSEMBLE_DEBUG] {model_type}.predict 返回: 设备={pred.device}, 形状={pred.shape}, 值={pred[:3] if len(pred) > 0 else 'empty'}...")
                    
                    # 确保预测结果在正确设备上
                    pred = self.device_manager.ensure_tensor_device(pred)
                    predictions[model_type] = pred
                    
                    # logging.info(f"🔍 [ENSEMBLE_DEBUG] {model_type} 转换后: 设备={pred.device}")
                    
                    # 🔧 调试信息：检查每个模型的预测结果
                    logging.debug(f"📉 {model_type.upper()}预测结果: {pred}")
                    if len(pred) > 1:
                        pred_variance = torch.var(pred)
                        pred_range = pred.max() - pred.min()
                        logging.debug(f"📉 {model_type.upper()}预测统计: 方差={pred_variance:.8f}, 范围={pred_range:.6f}")
                        if pred_variance < 1e-8:
                            logging.warning(f"🚨 {model_type.upper()}模型预测值方差过小，所有预测几乎相同：{pred[0]:.6f}")
                else:
                    logging.warning(f"{model_type.upper()}模型未训练，跳过预测")
            except Exception as e:
                logging.warning(f"{model_type.upper()}模型预测失败: {e}")
                
                logging.error(f"🛑 [ENSEMBLE_DEBUG] {model_type} 预测异常: {e}")
                import traceback
                logging.error(f"🛑 [ENSEMBLE_DEBUG] {model_type} 详细错误:\n{traceback.format_exc()}")
                
                # 🔧 关键修复：为预测失败的模型提供备选值，确保所有模型都有预测结果
                if model_type in ['gp', 'rbf']:
                    # 对于GP和RBF，使用基于历史数据的备选值
                    backup_value = self._get_backup_prediction_value(model_type, X.shape[0])
                    predictions[model_type] = backup_value
                    logging.info(f"🔄 {model_type.upper()}使用备选预测值: {backup_value}")
                elif model_type == 'tabpfn':
                    # 🔧 关键修复：对于TabPFN，也使用转换后的预测值作为备选
                    backup_value = self._get_backup_prediction_value(model_type, X.shape[0])
                    predictions[model_type] = backup_value
                    logging.info(f"🔄 TabPFN使用备选预测值: {backup_value}")
                
                logging.debug(f"{model_type.upper()}模型预测详细错误: {traceback.format_exc()}")
        
        # 🔧 最终调试信息
        logging.debug(f"🎯 predict_all_models 完成，返回{len(predictions)}个模型的预测结果")
        
        # 🔧 内存管理优化：确保输入张量在使用后可以被释放
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return predictions
    
    def predict_best_center(self, 
                           candidates: torch.Tensor,
                           existing_X: torch.Tensor = None, 
                           existing_y: torch.Tensor = None) -> torch.Tensor:
        """
        从候选点中预测最佳TR中心点
        
        Args:
            candidates: 候选点集合 [n_candidates, n_dims] (低维空间)
            existing_X: 现有训练数据 (高维空间)  
            existing_y: 现有训练目标
            
        Returns:
            预测的最佳中心点 [n_dims] (低维空间)
        """
        logging.info(f"🎯 集成模型开始预测最佳中心点，候选点数: {len(candidates)}")
        
        # 🔍 设备调试信息 - 输入检查
        # logging.info(f"🔍 [ENSEMBLE_DEBUG] candidates设备: {candidates.device}, 形状: {candidates.shape}")
        # logging.info(f"🔍 [ENSEMBLE_DEBUG] existing_X设备: {existing_X.device if existing_X is not None else 'None'}, 形状: {existing_X.shape if existing_X is not None else 'None'}")
        # logging.info(f"🔍 [ENSEMBLE_DEBUG] existing_y设备: {existing_y.device if existing_y is not None else 'None'}, 形状: {existing_y.shape if existing_y is not None else 'None'}")
        # logging.info(f"🔍 [ENSEMBLE_DEBUG] device_manager.device: {self.device_manager.device}")
        
        # 🎆 检查是否需要更新模型
        needs_training = False
        if existing_X is not None and existing_y is not None:
            current_size = len(existing_X)
            logging.info(f"📊 输入训练数据: {current_size}个样本 (上次: {self.last_training_size})")
            
            # 💫 每次迭代都更新代理模型（昂贵优化问题的关键）
            if not self.is_fitted:
                needs_training = True
                logging.info("🎆 集成模型未训练，需要初始化训练")
            elif current_size > self.last_training_size:  # 有任何新数据就更新
                needs_training = True
                new_samples = current_size - self.last_training_size
                logging.info(f"🔄 新增{new_samples}个珍贵样本，立即更新模型")
            else:
                logging.debug(f"🔄 数据量未变化 ({current_size})，跳过模型更新")
        
        # 执行模型更新
        if needs_training:
            if not self.is_fitted:
                self.fit(existing_X, existing_y)
            else:
                self.update_models(existing_X, existing_y)
            
            self.last_training_size = len(existing_X)
        
        if not self.is_fitted:
            logging.warning("集成模型未训练，返回第一个候选点")
            return candidates[0]
        
        # 优化：向量化候选点转换为高维空间，减少循环
        try:
            # 将所有候选点批量转换为高维空间
            if candidates.dim() == 2:
                # candidates已经是二维张量 [n_candidates, n_dims]
                candidates_for_projection = candidates
            else:
                # 如果是三维或更高维度，需要调整
                candidates_for_projection = candidates.view(-1, candidates.shape[-1])
            
            # 批量投影到高维空间
            with torch.no_grad():
                candidates_high_dim = self.axus.project_up(candidates_for_projection.T).T
                
            # 确保结果在正确设备上
            candidates_high_dim = self.device_manager.ensure_tensor_device(candidates_high_dim)
            
        except Exception as e:
            logging.warning(f"批量投影失败，回退到逐个投影: {e}")
            # 回退到原来的逐个投影方法
            candidates_high_dim = []
            for i in range(len(candidates)):
                candidate = candidates[i]
                if candidate.dim() == 1:
                    candidate = candidate.unsqueeze(0)
                
                try:
                    # 投影到高维空间
                    high_dim = self.axus.project_up(candidate.T).T
                    candidates_high_dim.append(high_dim.squeeze(0))
                except Exception as proj_e:
                    logging.warning(f"候选点{i}投影失败: {proj_e}")
                    continue
            
            if not candidates_high_dim:
                logging.warning("所有候选点投影失败，返回第一个原始候选点")
                return candidates[0]
            
            candidates_high_dim = torch.stack(candidates_high_dim)
        
        # 获取所有模型的预测
        predictions_dict = self.predict_all_models(candidates_high_dim)
        
        if not predictions_dict:
            logging.warning("所有模型预测失败，返回第一个候选点")
            return candidates[0]
        
        # 🎯 归一化预测值用于公平比较
        normalized_predictions_dict = {}
        for model_name, predictions in predictions_dict.items():
            normalized_predictions_dict[model_name] = self.candidate_selector.normalize_predictions(predictions)
        
        # 获取当前权重
        current_weights = self.weight_manager.get_weights()
        
        # 🌟 详细日志：显示各模型预测和权重
        logging.info("💡 集成模型预测详情:")
        logging.info(f"   📊 各模型权重: {current_weights}")
        
        for model_name, predictions in predictions_dict.items():
            if len(predictions) > 0:
                min_pred = predictions.min().item()
                max_pred = predictions.max().item()
                mean_pred = predictions.mean().item()
                # 归一化后的预测
                norm_pred = normalized_predictions_dict.get(model_name)
                norm_min = norm_pred.min().item() if norm_pred is not None else 0
                norm_max = norm_pred.max().item() if norm_pred is not None else 0
                norm_mean = norm_pred.mean().item() if norm_pred is not None else 0
                logging.info(f"   📈 {model_name.upper()}: 原始范围[{min_pred:.4f}, {max_pred:.4f}] → 归一化[{norm_min:.4f}, {norm_max:.4f}], 均值{mean_pred:.4f}→{norm_mean:.4f}")
        
        # 选择最佳候选点（使用归一化后的预测值）
        # 🔧 关键修复：确保候选点在正确设备上传递给选择器
        candidates_on_device = self.device_manager.ensure_tensor_device(candidates)
        
        best_candidate = self.candidate_selector.select_best_candidate(
            candidates_on_device, normalized_predictions_dict, current_weights
        )
        
        # 🌟 详细日志：显示选择结果
        best_idx = None
        
        # 🔍 设备调试信息
        # logging.info(f"🔍 [PREDICT_DEBUG] 查找最佳候选点索引...")
        # logging.info(f"🔍 [PREDICT_DEBUG] 原始candidates设备: {candidates.device}")
        # logging.info(f"🔍 [PREDICT_DEBUG] best_candidate设备: {best_candidate.device}")
        
        for i, candidate in enumerate(candidates):
            # 🔧 关键修复：确保两个张量在同一设备上进行比较
            candidate_on_device = self.device_manager.ensure_tensor_device(candidate)
            if torch.allclose(candidate_on_device, best_candidate, atol=1e-6):
                best_idx = i
                # logging.info(f"🔍 [PREDICT_DEBUG] 找到匹配的候选点索引: {i}")
                break
        
        if best_idx is not None:
            logging.info(f"🎯 集成选择结果: 第{best_idx}个候选点 {best_candidate[:3]}...")
            # 显示这个候选点在各模型的得分（原始分数和归一化分数）
            for model_name, predictions in predictions_dict.items():
                if best_idx < len(predictions):
                    raw_score = predictions[best_idx].item()
                    norm_score = normalized_predictions_dict[model_name][best_idx].item()
                    weight = current_weights.get(model_name, 0.0)
                    weighted_score = weight * norm_score  # 使用归一化后的分数计算加权分
                    logging.info(f"   📊 {model_name.upper()}: 原始分{raw_score:.4f} → 归一化{norm_score:.4f}, 权重{weight:.4f}, 加权分{weighted_score:.4f}")
        else:
            logging.info(f"🎯 集成选择结果: {best_candidate[:3]}...")
        
        # 🔧 内存管理优化：清理临时张量
        del candidates_high_dim, predictions_dict, normalized_predictions_dict
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return best_candidate
    
    def record_performance(self, 
                          predictions_dict: Dict[str, torch.Tensor], 
                          actual_values: torch.Tensor) -> None:
        """
        记录模型性能用于权重更新 - 强化版本
        
        Args:
            predictions_dict: 各模型预测 {model_name: predictions}
            actual_values: 实际评估值
        """
        logging.debug(f"📊 开始性能记录: {len(predictions_dict)}个模型，{len(actual_values)}个真实值")
        
        # 🔧 数据验证
        if not predictions_dict:
            logging.warning(f"⚠️ 性能记录失败: 预测结果为空")
            return
            
        if len(actual_values) == 0:
            logging.warning(f"⚠️ 性能记录失败: 实际值为空")
            return
        
        # 📊 记录各模型性能
        recorded_count = 0
        for model_name, predictions in predictions_dict.items():
            if len(predictions) == len(actual_values):
                # 🔧 关键修复：现在TabPFN也输出预测值，可以直接与真实值比较
                try:
                    for i, (pred, actual) in enumerate(zip(predictions, actual_values)):
                        pred_value = pred.item() if hasattr(pred, 'item') else float(pred)
                        actual_value = actual.item() if hasattr(actual, 'item') else float(actual)
                        
                        self.weight_manager.record_prediction(model_name, pred_value, actual_value)
                        logging.debug(f"📊 {model_name.upper()}[{i}]性能记录: 预测={pred_value:.4f}, 实际={actual_value:.4f}, 误差={abs(pred_value - actual_value):.4f}")
                        recorded_count += 1
                except Exception as e:
                    logging.warning(f"⚠️ {model_name.upper()}性能记录失败: {e}")
                    continue
            else:
                logging.warning(f"⚠️ {model_name.upper()}预测数量({len(predictions)})与真实值数量({len(actual_values)})不匹配")
        
        if recorded_count > 0:
            logging.info(f"📊 性能记录完成: {recorded_count}条记录已添加到权重管理系统")
            
            # 🔧 强制记录当前各模型的性能状态
            for model_name in predictions_dict.keys():
                history_count = len(self.weight_manager.performance_history[model_name])
                mae = self.weight_manager.calculate_mae(model_name)
                logging.debug(f"📈 {model_name.upper()}性能状态: MAE={mae:.4f}, 历史数据={history_count}条")
            
            # 更新权重
            logging.debug(f"🎯 触发权重更新...")
            self.weight_manager.update_weights()
        else:
            logging.warning(f"⚠️ 性能记录失败: 没有成功记录任何性能数据")
    
    def _get_backup_prediction_value(self, model_type: str, n_samples: int) -> torch.Tensor:
        """
        为预测失败的模型生成备选预测值
        
        Args:
            model_type: 模型类型
            n_samples: 样本数量
            
        Returns:
            备选预测值
        """
        # 基于历史性能记录生成合理的备选值
        history = self.weight_manager.performance_history.get(model_type, [])
        
        if len(history) > 0:
            # 使用历史预测值的均值和标准差
            historical_predictions = [pred for pred, actual in history]
            mean_pred = sum(historical_predictions) / len(historical_predictions)
            
            # 添加一些随机性
            import random
            noise = random.gauss(0, abs(mean_pred) * 0.1)  # 10%的噪声
            backup_values = [mean_pred + noise for _ in range(n_samples)]
        else:
            # 没有历史数据时，使用保守的默认值
            if model_type == 'gp':
                backup_values = [-50.0] * n_samples  # GP通常预测负值（最小化问题）
            elif model_type == 'rbf':
                backup_values = [-45.0] * n_samples  # RBF类似GP
            elif model_type == 'tabpfn':
                # 🔧 关键修复：TabPFN也使用与其他模型相似的函数值范围
                backup_values = [-60.0] * n_samples  # TabPFN转换后的预测值
            else:
                backup_values = [0.0] * n_samples
        
        # 使用设备管理器确保返回值在正确设备上
        return self.device_manager.safe_to_device(torch.tensor(backup_values))
    
    def get_ensemble_info(self) -> Dict:
        """获取集成模型信息"""
        return {
            "model_types": self.model_types,
            "current_weights": self.weight_manager.get_weights(),
            "is_fitted": self.is_fitted,
            "model_status": {
                model_type: self.models[model_type].is_fitted 
                for model_type in self.model_types
            }
        }
