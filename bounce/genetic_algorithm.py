import logging
import random
from typing import List, Tu<PERSON>, Optional, Union
import numpy as np
import torch
import pandas as pd
from dataclasses import dataclass

from bounce.util.benchmark import Parameter, ParameterType
from bounce.projection import AxUS


@dataclass
class GAConfig:
    """遗传算法配置参数"""
    population_size: int = 100
    max_generations: int = 200
    crossover_rate: float = 0.8
    mutation_rate: float = 0.1
    tournament_size: int = 3
    elitism_rate: float = 0.1  # 精英保留比例

    # 🚀 新增：动态种群大小配置
    use_dynamic_population: bool = True  # 是否使用动态种群大小
    base_population_size: int = 100  # 基础种群大小
    population_scale_factor: float = 2.0  # 种群缩放因子
    min_population_size: int = 50  # 最小种群大小
    max_population_size: int = 500  # 最大种群大小

    def get_population_size(self, target_dim: int) -> int:
        """根据目标维度计算动态种群大小"""
        if not self.use_dynamic_population:
            return self.population_size

        # 动态计算：base_size + scale_factor * target_dim
        dynamic_size = int(self.base_population_size + self.population_scale_factor * target_dim)

        # 限制在合理范围内
        return max(self.min_population_size, min(self.max_population_size, dynamic_size))


class Individual:
    """个体类，表示一个候选解"""
    
    def __init__(self, genes: torch.Tensor, axus: AxUS):
        """
        初始化个体
        
        Args:
            genes: 基因编码，在[-1, 1]范围内
            axus: AxUS投影对象，用于处理不同变量类型
        """
        self.genes = genes.clone()  # 在低维空间中的编码
        self.axus = axus
        self.fitness = None
        self.high_dim_genes = None  # 高维空间中的编码
        
    def to_high_dim(self, lb: torch.Tensor, ub: torch.Tensor) -> torch.Tensor:
        """将低维基因转换为高维表示"""
        if self.high_dim_genes is None:
            # 🔧 修复类别变量转换问题：project_up期望类别变量是{-1,1}格式，不是{0,1}格式
            # 对于类别变量，保持{-1,1}格式；对于其他变量，转换为[0,1]
            genes_for_projection = self.genes.clone()

            # 检查是否有类别变量
            has_categorical = False
            if hasattr(self.axus, 'bins_and_indices_of_type'):
                from bounce.util.benchmark import ParameterType
                for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                    if len(indices) > 0:
                        has_categorical = True
                        break

            if not has_categorical:
                # 没有类别变量，正常转换为[0,1]
                genes_for_projection = (self.genes + 1) / 2
            # 如果有类别变量，保持原始的{-1,1}格式

            # 投影到高维空间，确保输入是2D张量
            if genes_for_projection.dim() == 1:
                genes_for_projection = genes_for_projection.unsqueeze(0)
            high_dim = self.axus.project_up(genes_for_projection.T).T

            # 转换到实际范围，确保输入是2D张量
            if high_dim.dim() == 1:
                high_dim = high_dim.unsqueeze(0)

            # 🎯 关键修复：对于纯二分问题，避免from_1_around_origin产生0.5值
            # 检查是否为纯二分问题
            is_pure_binary = True
            if hasattr(self.axus, 'bins_and_indices_of_type'):
                from bounce.util.benchmark import ParameterType
                # 检查是否有非二分变量
                for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break
                for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break

            if is_pure_binary:
                # 纯二分问题：直接将{-1,1}映射到{0,1}，避免0.5值
                self.high_dim_genes = (high_dim + 1) / 2
                # 确保严格的{0,1}值
                self.high_dim_genes = torch.round(self.high_dim_genes)
                self.high_dim_genes = torch.clamp(self.high_dim_genes, 0.0, 1.0)
                if high_dim.dim() > 1:
                    self.high_dim_genes = self.high_dim_genes.squeeze(0)
            else:
                # 混合变量问题：使用from_1_around_origin
                from bounce.util.data_handling import from_1_around_origin
                self.high_dim_genes = from_1_around_origin(
                    x=high_dim,
                    lb=lb,
                    ub=ub
                ).squeeze(0)
        return self.high_dim_genes
    
    def copy(self):
        """创建个体的深拷贝"""
        new_individual = Individual(self.genes.clone(), self.axus)
        new_individual.fitness = self.fitness
        if self.high_dim_genes is not None:
            new_individual.high_dim_genes = self.high_dim_genes.clone()
        return new_individual


class MixedVariableGA:
    """混合变量类型的遗传算法"""

    def __init__(self, config: GAConfig, axus: AxUS, benchmark, global_surrogate=None, ensemble_manager=None):
        """
        初始化遗传算法

        Args:
            config: 遗传算法配置
            axus: AxUS投影对象
            benchmark: 基准函数
            global_surrogate: TabPFN全局代理模型（用于预测适应度）
            ensemble_manager: 集成代理模型管理器（用于集成模式）
        """
        self.config = config
        self.axus = axus
        self.benchmark = benchmark
        self.global_surrogate = global_surrogate  # TabPFN代理模型
        self.ensemble_manager = ensemble_manager  # 集成代理模型管理器
        self.population: List[Individual] = []
        self.generation = 0
        self.best_individual = None
        self.fitness_history = []

        # 获取不同类型变量的索引
        self._get_variable_indices()
        
    def _get_variable_indices(self):
        """获取不同类型变量在低维空间中的索引"""
        self.binary_indices = []
        self.continuous_indices = []
        self.categorical_indices = []
        
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.BINARY):
            self.binary_indices.extend(indices.tolist())
            
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
            self.continuous_indices.extend(indices.tolist())
            
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            self.categorical_indices.extend(indices.tolist())
            
        self.binary_indices = torch.tensor(self.binary_indices, dtype=torch.long)
        self.continuous_indices = torch.tensor(self.continuous_indices, dtype=torch.long)
        self.categorical_indices = torch.tensor(self.categorical_indices, dtype=torch.long)
        
        # logging.info(f"GA变量索引 - 二分: {len(self.binary_indices)}, "
        #             f"连续: {len(self.continuous_indices)}, "
        #             f"类别: {len(self.categorical_indices)}")

    def initialize_population(self) -> None:
        """初始化种群"""
        self.population = []

        # 🚀 动态计算种群大小
        dynamic_population_size = self.config.get_population_size(self.axus.target_dim)
        logging.info(f"动态种群大小: {dynamic_population_size} (目标维度: {self.axus.target_dim})")

        for _ in range(dynamic_population_size):
            # 🎯 关键修复：对于纯二分问题，生成严格的{-1,1}值
            # 检查是否为纯二分问题
            is_pure_binary = True
            if hasattr(self.axus, 'bins_and_indices_of_type'):
                from bounce.util.benchmark import ParameterType
                # 检查是否有非二分变量
                for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break
                for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break

            if is_pure_binary:
                # 纯二分问题：生成严格的{-1,1}值
                genes = torch.randint(0, 2, (self.axus.target_dim,), dtype=torch.float64) * 2 - 1
            else:
                # 🎯 混合变量问题：分别处理不同类型的变量
                genes = torch.zeros(self.axus.target_dim, dtype=torch.float64)
                
                # 二分变量：生成严格的{-1,1}值
                if len(self.binary_indices) > 0:
                    binary_values = torch.randint(0, 2, (len(self.binary_indices),), dtype=torch.float64)
                    genes[self.binary_indices] = binary_values * 2 - 1  # 转换为{-1, 1}
                
                # 连续变量：在[-1, 1]范围内随机生成
                if len(self.continuous_indices) > 0:
                    genes[self.continuous_indices] = torch.rand(len(self.continuous_indices), dtype=torch.float64) * 2 - 1
                
                # 类别变量：确保one-hot编码的正确性
                if len(self.categorical_indices) > 0:
                    genes = self._fix_categorical_genes(genes)

            individual = Individual(genes, self.axus)
            self.population.append(individual)
            
        logging.info(f"初始化种群完成，种群大小: {len(self.population)}")

    def inject_elite_individual(self, elite_genes: torch.Tensor):
        """
        将精英个体注入到种群中，替换最差的个体

        Args:
            elite_genes: 精英个体的基因
        """
        if len(self.population) == 0:
            return

        # 创建精英个体
        elite_individual = Individual(elite_genes.clone(), self.axus)

        # 找到种群中最差的个体（fitness最大的，因为我们要最小化）
        worst_idx = 0
        worst_fitness = float('-inf')

        for i, individual in enumerate(self.population):
            if individual.fitness is not None and individual.fitness > worst_fitness:
                worst_fitness = individual.fitness
                worst_idx = i

        # 替换最差的个体
        self.population[worst_idx] = elite_individual
        logging.debug(f"🏆 注入精英个体到GA种群，替换第{worst_idx}个个体")

    def _fix_categorical_genes(self, genes: torch.Tensor) -> torch.Tensor:
        """修正类别变量的基因编码，确保one-hot编码的正确性"""
        genes = genes.clone()

        # 获取类别变量的bins信息
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            if len(indices) > 0:
                # 对于每个类别变量的one-hot编码段，随机选择一个位置设为1，其他设为-1
                # 注意：indices可能不是连续的，需要按bin处理
                n_categories = bin_obj.dims_required

                # 将该bin的所有维度设为-1
                genes[indices] = -1

                # 随机选择一个位置设为1
                selected_pos = torch.randint(0, len(indices), (1,)).item()
                genes[indices[selected_pos]] = 1

        return genes

    def evaluate_population(self) -> None:
        """评估种群中所有个体的适应度"""
        # 收集需要评估的个体
        individuals_to_evaluate = [ind for ind in self.population if ind.fitness is None]

        if not individuals_to_evaluate:
            return

        # 🔧 修复：支持集成模式和单一模型模式
        if self.ensemble_manager is not None and self.ensemble_manager.is_fitted:
            # 集成模式：使用集成代理模型预测适应度
            logging.debug(f"🤖 使用集成代理模型评估{len(individuals_to_evaluate)}个GA个体")
            self._evaluate_with_ensemble(individuals_to_evaluate)
        elif self.global_surrogate is not None and self.global_surrogate.is_fitted:
            # 单一模型模式：使用单一代理模型预测适应度
            logging.debug(f"🤖 使用单一代理模型评估{len(individuals_to_evaluate)}个GA个体")
            self._evaluate_with_surrogate(individuals_to_evaluate)
        else:
            raise ValueError("代理模型未初始化或未训练，无法进行评估")

        # 更新最佳个体
        self._update_best_individual()

    def _evaluate_with_surrogate(self, individuals: List[Individual]) -> None:
        """使用单一代理模型评估个体适应度"""
        if not individuals:
            return

        # 转换所有个体到高维空间
        high_dim_candidates = []
        for individual in individuals:
            high_dim_genes = individual.to_high_dim(
                self.benchmark.lb_vec,
                self.benchmark.ub_vec
            )
            high_dim_candidates.append(high_dim_genes)

        # 批量预测适应度
        candidates_tensor = torch.stack(high_dim_candidates)
        try:
            # 使用代理模型预测质量分数（越小越好）
            quality_scores = self.global_surrogate.predict_quality(candidates_tensor)

            # 将质量分数作为适应度
            for i, individual in enumerate(individuals):
                individual.fitness = quality_scores[i].item()

            logging.info(f"单一代理模型评估了{len(individuals)}个GA个体")

        except Exception as e:
            logging.error(f"单一代理模型评估GA个体失败: {e}")
            raise e

    def _evaluate_with_ensemble(self, individuals: List[Individual]) -> None:
        """使用集成代理模型评估个体适应度"""
        if not individuals:
            return

        # 转换所有个体到高维空间
        high_dim_candidates = []
        for individual in individuals:
            high_dim_genes = individual.to_high_dim(
                self.benchmark.lb_vec,
                self.benchmark.ub_vec
            )
            high_dim_candidates.append(high_dim_genes)

        # 批量预测适应度
        candidates_tensor = torch.stack(high_dim_candidates)
        try:
            # 使用集成代理模型预测
            predictions_dict = self.ensemble_manager.predict_all_models(candidates_tensor)

            if not predictions_dict:
                raise ValueError("集成代理模型预测失败，没有返回任何预测结果")

            # 获取当前权重
            current_weights = self.ensemble_manager.weight_manager.get_weights()

            # 计算加权平均预测值作为适应度
            n_individuals = len(individuals)
            weighted_predictions = torch.zeros(n_individuals)

            for model_name, predictions in predictions_dict.items():
                weight = current_weights.get(model_name, 0.0)
                if weight > 0:
                    weighted_predictions += weight * predictions.cpu()

            # 将加权预测值作为适应度
            for i, individual in enumerate(individuals):
                individual.fitness = weighted_predictions[i].item()

            logging.info(f"集成代理模型评估了{len(individuals)}个GA个体，使用权重: {current_weights}")

        except Exception as e:
            logging.error(f"集成代理模型评估GA个体失败: {e}")
            raise e

    def _update_best_individual(self) -> None:
        """更新最佳个体"""
        current_best = min(self.population, key=lambda x: x.fitness)
        
        if self.best_individual is None or current_best.fitness < self.best_individual.fitness:
            self.best_individual = current_best.copy()
            
        # 记录适应度历史
        self.fitness_history.append(self.best_individual.fitness)

    def tournament_selection(self) -> Individual:
        """锦标赛选择"""
        tournament = random.sample(self.population, self.config.tournament_size)
        return min(tournament, key=lambda x: x.fitness)

    def crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """交叉操作，针对不同变量类型采用不同策略"""
        if random.random() > self.config.crossover_rate:
            return parent1.copy(), parent2.copy()
            
        child1_genes = parent1.genes.clone()
        child2_genes = parent2.genes.clone()
        
        # 对连续变量使用算术交叉
        if len(self.continuous_indices) > 0:
            alpha = random.random()
            child1_genes[self.continuous_indices] = (
                alpha * parent1.genes[self.continuous_indices] + 
                (1 - alpha) * parent2.genes[self.continuous_indices]
            )
            child2_genes[self.continuous_indices] = (
                (1 - alpha) * parent1.genes[self.continuous_indices] + 
                alpha * parent2.genes[self.continuous_indices]
            )
        
        # 对二分变量使用单点交叉
        if len(self.binary_indices) > 0:
            crossover_point = random.randint(0, len(self.binary_indices) - 1)
            child1_genes[self.binary_indices[:crossover_point]] = parent2.genes[self.binary_indices[:crossover_point]]
            child2_genes[self.binary_indices[:crossover_point]] = parent1.genes[self.binary_indices[:crossover_point]]
            
            # 🎯 关键修复：确保交叉后二分变量仍为{-1,1}值
            child1_genes[self.binary_indices] = torch.sign(child1_genes[self.binary_indices])
            child2_genes[self.binary_indices] = torch.sign(child2_genes[self.binary_indices])
            # 处理可能的0值
            zero_mask1 = child1_genes[self.binary_indices] == 0
            zero_mask2 = child2_genes[self.binary_indices] == 0
            if zero_mask1.any():
                child1_genes[self.binary_indices[zero_mask1]] = torch.randint(0, 2, (zero_mask1.sum(),), dtype=torch.float64) * 2 - 1
            if zero_mask2.any():
                child2_genes[self.binary_indices[zero_mask2]] = torch.randint(0, 2, (zero_mask2.sum(),), dtype=torch.float64) * 2 - 1
        
        # 对类别变量使用特殊交叉
        if len(self.categorical_indices) > 0:
            child1_genes = self._crossover_categorical(parent1.genes, parent2.genes, child1_genes)
            child2_genes = self._crossover_categorical(parent2.genes, parent1.genes, child2_genes)
        
        return Individual(child1_genes, self.axus), Individual(child2_genes, self.axus)

    def _crossover_categorical(self, parent1_genes: torch.Tensor, parent2_genes: torch.Tensor, 
                             child_genes: torch.Tensor) -> torch.Tensor:
        """类别变量的交叉操作"""
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            if len(indices) > 0 and random.random() < 0.5:
                # 50%概率从另一个父代继承类别变量
                child_genes[indices] = parent2_genes[indices]
        return child_genes

    def mutate(self, individual: Individual) -> Individual:
        """变异操作"""
        if random.random() > self.config.mutation_rate:
            return individual
            
        mutated_genes = individual.genes.clone()
        
        # 连续变量：高斯变异
        if len(self.continuous_indices) > 0:
            for idx in self.continuous_indices:
                if random.random() < 0.1:  # 10%的变异概率
                    noise = torch.normal(0, 0.1, (1,)).item()
                    mutated_genes[idx] = torch.clamp(mutated_genes[idx] + noise, -1, 1)
        
        # 二分变量：位翻转
        if len(self.binary_indices) > 0:
            for idx in self.binary_indices:
                if random.random() < 0.05:  # 5%的变异概率
                    # 🎯 关键修复：确保位翻转后仍为{-1,1}值
                    current_value = mutated_genes[idx]
                    if current_value > 0:
                        mutated_genes[idx] = -1.0
                    else:
                        mutated_genes[idx] = 1.0
        
        # 类别变量：重新随机选择
        if len(self.categorical_indices) > 0:
            mutated_genes = self._mutate_categorical(mutated_genes)
        
        return Individual(mutated_genes, self.axus)

    def _mutate_categorical(self, genes: torch.Tensor) -> torch.Tensor:
        """类别变量的变异操作"""
        genes = genes.clone()
        
        for bin_obj, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            if len(indices) > 0 and random.random() < 0.1:  # 10%的变异概率
                start_idx = indices[0].item()
                end_idx = indices[-1].item() + 1
                
                # 重新随机选择类别
                genes[start_idx:end_idx] = -1
                selected_idx = start_idx + torch.randint(0, end_idx - start_idx, (1,)).item()
                genes[selected_idx] = 1
                
        return genes

    def evolve_generation(self) -> None:
        """进化一代"""
        # 评估当前种群
        self.evaluate_population()

        # 🚀 使用动态种群大小
        current_population_size = self.config.get_population_size(self.axus.target_dim)

        # 精英保留
        elite_count = int(current_population_size * self.config.elitism_rate)
        elites = sorted(self.population, key=lambda x: x.fitness)[:elite_count]

        # 生成新种群
        new_population = [elite.copy() for elite in elites]

        # 生成剩余个体
        while len(new_population) < current_population_size:
            # 选择父代
            parent1 = self.tournament_selection()
            parent2 = self.tournament_selection()

            # 交叉
            child1, child2 = self.crossover(parent1, parent2)

            # 变异
            child1 = self.mutate(child1)
            child2 = self.mutate(child2)

            new_population.extend([child1, child2])

        # 确保种群大小正确
        self.population = new_population[:current_population_size]
        self.generation += 1

        logging.debug(f"第{self.generation}代进化完成，最佳适应度: {self.best_individual.fitness:.6f}")

    def run(self, max_generations: Optional[int] = None) -> Individual:
        """运行遗传算法（完整进化过程）"""
        if max_generations is None:
            max_generations = self.config.max_generations

        # 初始化种群（如果还没有初始化）
        if not self.population:
            self.initialize_population()

        # 进化过程
        for generation in range(max_generations):
            self.evolve_generation()

            # 早停条件（可选）
            if generation > 10:
                recent_improvements = [
                    self.fitness_history[i-1] - self.fitness_history[i]
                    for i in range(max(1, len(self.fitness_history)-10), len(self.fitness_history))
                ]
                if all(imp < 1e-6 for imp in recent_improvements):
                    logging.info(f"GA早停于第{generation}代，连续10代无显著改进")
                    break

        logging.info(f"GA完成，共进化{self.generation}代，最佳适应度: {self.best_individual.fitness:.6f}")
        return self.best_individual

    def reset_for_continuous_evolution(self):
        """重置GA状态以支持持续进化"""
        # 保留种群，但重置其他状态
        self.generation = 0
        self.fitness_history = []
        # 不重置种群和最佳个体，让它们继续进化

    def get_population_diversity(self) -> float:
        """计算种群多样性"""
        if len(self.population) < 2:
            return 0.0

        total_distance = 0.0
        count = 0

        for i in range(len(self.population)):
            for j in range(i + 1, len(self.population)):
                distance = torch.norm(self.population[i].genes - self.population[j].genes).item()
                total_distance += distance
                count += 1

        return total_distance / count if count > 0 else 0.0

    def get_best_individual_for_tabpfn(self) -> torch.Tensor:
        """获取最佳个体用于TabPFN预测，返回高维表示"""
        if self.best_individual is None:
            raise ValueError("尚未运行遗传算法或种群为空")

        return self.best_individual.to_high_dim(
            self.benchmark.lb_vec,
            self.benchmark.ub_vec
        )

    def get_population_for_tabpfn(self, top_k: int = 10) -> torch.Tensor:
        """获取种群中前k个个体用于TabPFN训练，返回高维表示"""
        if not self.population:
            raise ValueError("种群为空")

        # 按适应度排序
        sorted_population = sorted(self.population, key=lambda x: x.fitness)
        top_individuals = sorted_population[:min(top_k, len(sorted_population))]

        # 转换为高维表示
        high_dim_population = []
        for individual in top_individuals:
            high_dim_genes = individual.to_high_dim(
                self.benchmark.lb_vec,
                self.benchmark.ub_vec
            )
            high_dim_population.append(high_dim_genes)

        return torch.stack(high_dim_population)

    def update_with_new_data(self, x_new: torch.Tensor, fx_new: torch.Tensor) -> None:
        """使用新的评估数据更新种群（可选功能）"""
        # 将新数据转换为低维表示并添加到种群中
        # 这个功能可以用于在线学习，暂时保留接口
        pass
