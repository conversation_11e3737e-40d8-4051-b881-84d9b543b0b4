import logging
import lzma
import math
import os.path
import zlib
from datetime import datetime
from typing import Optional, Union

import gin
import numpy as np
import torch
import pandas as pd
from botorch.acquisition import ExpectedImprovement
from botorch.sampling import SobolQMCNormalSampler
from torch import Size
from tqdm import tqdm

from bounce import settings
from bounce.benchmarks import Benchmark
from bounce.candidates import create_candidates_continuous, create_candidates_discrete
from bounce.gaussian_process import fit_mll, get_gp
from bounce.projection import AxUS, Bin
from bounce.trust_region import TrustRegion, update_tr_state
from bounce.util.benchmark import ParameterType
from bounce.util.data_handling import (
    construct_mixed_point,
    from_1_around_origin,
    join_data,
    sample_binary,
    sample_categorical,
    sample_continuous,
)
from bounce.util.printing import BColors
from bounce.ga_tabpfn_integration import GATabPFNIntegration
from bounce.genetic_algorithm import GAConfig


@gin.configurable
class Bounce:
    """
    Bounce class: implements the Bounce algorithm.

    The main method is `run()` which runs the algorithm.
    """

    def __init__(
            self,
            benchmark: Benchmark,
            number_initial_points: int,
            initial_target_dimensionality: int,
            number_new_bins_on_split: int,
            maximum_number_evaluations: int,
            batch_size: int,
            results_dir: str,
            desired_final_dimensionality: Optional[int] = None,
            maximum_number_evaluations_until_input_dim: Optional[int] = None,
            max_cholesky_size: int = 1000,
            device: str = "cuda" if torch.cuda.is_available() else "cpu",
            dtype: Optional[str] = None,
            use_scipy_lbfgs: bool = True,
            use_surrogate_local: bool = True,  # 新增：是否使用代理模型作为局部代理模型
            local_model_type: str = 'tabpfn',  # 新增：局部代理模型类型
            max_lbfgs_iters: Optional[int] = None,
            min_cuda: int = 10,
            n_interleaved: int = 5,
    ):
        """
        Init

        Args:
            benchmark: the benchmark to be used
            number_initial_points: the number of initial points to be sampled
            initial_target_dimensionality: the dimensionality in which `Bounce` starts the optimization
            number_new_bins_on_split: the number of new bins to be created on each split (if applicable)
            maximum_number_evaluations: the maximum number of function evaluations
            batch_size: the batch size to be used
            results_dir: the directory where the results will be stored
            desired_final_dimensionality: the dimensionality in which `Bounce` terminates the optimization
            maximum_number_evaluations_until_input_dim: the maximum number of function evaluations until the input
            max_cholesky_size: the maximum size of the Cholesky decomposition
            device: the device to be used (cpu or cuda)
            dtype: the dtype to be used (float32 or float64)
            use_scipy_lbfgs: whether to use scipy's LBFGS implementation or the backup Adam optimizer for the GP fitting
            max_lbfgs_iters: maximum iterations until we run LBFGS, after that use Adam
            min_cuda: the minimum number of data points to use cuda
            n_interleaved: the number of interleaved steps when optimizing mixed benchmarks

        """
        self.benchmark = benchmark
        """
        The benchmark to be used
        """
        self.number_initial_points = number_initial_points
        """
        the number of initial points to be sampled
        """
        self.initial_target_dimensionality = initial_target_dimensionality
        """
        the dimensionality in which `Bounce` starts the optimization
        """
        self.number_new_bins_on_split = number_new_bins_on_split
        """
        the number of new bins to be created on each split (if applicable)
        """
        self.maximum_number_evaluations = maximum_number_evaluations
        """
        the maximum number of function evaluations
        """
        self.batch_size = batch_size
        """
        the batch size to be used
        """
        self.max_cholesky_size = max_cholesky_size
        """
        the maximum size of the Cholesky decomposition
        """
        self.use_scipy_lbfgs = use_scipy_lbfgs
        """
        whether to use scipy's LBFGS implementation or the backup Adam optimizer for the GP fitting
        """
        self.device = device
        print(self.device)
        """
        the device to be used (cpu or cuda)
        """
        self.min_cuda = min_cuda
        
        self.use_surrogate_local = use_surrogate_local
        self.local_model_type = local_model_type
        """
        the minimum number of data points to use cuda
        """
        self.max_lbfgs_iters = max_lbfgs_iters  # maximum iterations until we run LBFGS, after that use Adam
        """
        maximum iterations until we run LBFGS, after that use Adam
        """
        self.n_interleaved = n_interleaved
        """
        the number of interleaved steps when optimizing mixed benchmarks
        """
        self.dtype = None
        """
        the dtype to be used (float32 or float64)
        """
        if dtype is None:
            self.dtype = torch.float64
        else:
            match dtype:
                case "float32":
                    self.dtype = torch.float32
                case "float64":
                    self.dtype = torch.float64
                case _:
                    raise ValueError(f"Unknown dtype {dtype}")

        # defining results directory
        # 获取当前系统时间，用于生成唯一实验ID
        now = datetime.now()
        # 将gin配置转换为字符串，用于后续的哈希计算
        gin_config_str = gin.config_str()

        # 计算配置字符串的Adler-32哈希值（轻量级校验和）
        # 作用：相同配置的实验会产生相同的哈希值，方便归类
        adler = zlib.adler32(gin_config_str.encode("utf-8"))

        # 生成带日期的哈希值文件夹名，格式：YYYY-MM-DD_hash值
        date_str = now.strftime("%Y-%m-%d")
        hash_folder = f"{date_str}_{adler}"
        benchmark_name = self.benchmark.fun_name

        # 生成时间戳格式的文件名，精确到微秒保证唯一性
        # 格式示例：14:30:45:123456
        fname = now.strftime("%H:%M:%S:%f")

        # 构建结果目录路径，层级结构：
        # results_dir / 日期_哈希值文件夹 / 时间戳文件夹
        self.results_dir = os.path.join(results_dir, benchmark_name, hash_folder, fname)
       
        """
        the directory where the results will be stored
        """
        # 递归创建目录（exist_ok=True避免重复创建报错）
        os.makedirs(self.results_dir, exist_ok=True)    
        # save gin config to file
        with open(os.path.join(self.results_dir, "gin_config.txt"), "w") as f:
            f.write(gin.config_str())

        self.desired_final_dimensionality = None
        """
        the dimensionality in which `Bounce` terminates the optimization
        """
        if desired_final_dimensionality is None:
            self.desired_final_dimensionality = self.benchmark.dim
        else:
            self.desired_final_dimensionality = desired_final_dimensionality

        self.maximum_number_evaluations_until_input_dim = None
        """
        the maximum number of function evaluations until the input dimensionality is reached
        """
        if maximum_number_evaluations_until_input_dim is None:
            self.maximum_number_evaluations_until_input_dim = (
                self.maximum_number_evaluations
            )
        else:
            assert (
                    maximum_number_evaluations_until_input_dim
                    <= self.maximum_number_evaluations
            )
            self.maximum_number_evaluations_until_input_dim = (
                maximum_number_evaluations_until_input_dim
            )

        self.tr_splits = 0
        """
        the number of splits that have been performed (the target dimensionality has been increased)
        """

        # PRIVATE ATTRIBUTES

        self._n_evals = 0
        self._n_splits = math.ceil(
            math.log(
                self.desired_final_dimensionality / self.initial_target_dimensionality,
                self.number_new_bins_on_split + 1,
            )
        )
        
        if settings.ADJUST_NUMBER_OF_NEW_BINS:
            self._adjust_number_bins_on_split()
        logging.info(f"初始维度{self.initial_target_dimensionality}维，算法目标维度{self.desired_final_dimensionality}维")
        logging.info(f"🤖 {settings.NAME} will split at most {self._n_splits} times.  每次分裂{self.number_new_bins_on_split}个箱子")

        self.split_budget = self._split_budget(self.initial_target_dimensionality)
        logging.info(f"🤖 初始{self.initial_target_dimensionality}维，有{self.split_budget} 次评估预算 ")
        """
        the budget for the current target dimensionality
        """

        self.random_embedding = AxUS(
            parameters=self.benchmark.parameters,
            n_bins=self.initial_target_dimensionality,
        )
        """
        the random embedding used for the low-dimensional target space (only `AxUS` used)
        """

        self.trust_region = TrustRegion(dimensionality=self.random_embedding.target_dim)
        """
        a `TrustRegion` instance to model the trust region
        """

        # saving global data
        self.x_global = torch.empty(
            0, self.random_embedding.target_dim, dtype=self.dtype
        )
        """
        the input points in the low-dimensional target space
        """
        self.x_up_global = torch.empty(
            0, self.benchmark.representation_dim, dtype=self.dtype
        )
        """
        the input points in the high-dimensional representation space
        """
        self.fx_global = torch.empty(0, dtype=self.dtype)
        """
        the function values at the input points
        """

        # tr local data
        self._reset_local_data()

        all_target_dims = self.initial_target_dimensionality * torch.pow(
            1 + self.number_new_bins_on_split, torch.arange(0, self._n_splits + 1)
        )
        self._all_split_budgets = {
            i.item(): self._split_budget(i.item()) for i in all_target_dims
        }
        logging.info(f"每一个可能维度以及评估预算：{self._all_split_budgets}；以及总预算为{self.maximum_number_evaluations_until_input_dim}次")
        self.eval_history = []  # 新增：记录评估次数和当前最优值

        # 🚀 新增：策略切换机制
        self.stagnation_counter = 0  # 连续无改进次数
        self.strategy_switch_threshold = 3  # 策略切换阈值（可通过gin配置修改）
        self.last_best_fx = float('inf')  # 上次最佳函数值

        # 策略类型：0=全局模型策略, 1=历史最优策略
        self.current_strategy = 0  # 默认使用全局模型策略
        self.strategy_stagnation_counter = 0  # 当前策略的停滞计数
        self.strategy_names = ["全局模型策略", "历史最优策略"]

        # 🚀 新增：局部搜索代理模型配置
        logging.info(f"🔄 策略切换机制已启用，切换阈值: {self.strategy_switch_threshold}步")
        if self.use_surrogate_local:
            logging.info(f"🎯 局部搜索将使用{self.local_model_type.upper()}代理模型")

        # 🔧 新增：低维TabPFN训练标志
        self.low_dim_tabpfn_trained = False

        # 🚀 初始化GA-TabPFN集成系统，启用动态种群大小
        ga_config = GAConfig(
            population_size=100,  # 基础种群大小
            max_generations=200,
            crossover_rate=0.8,
            mutation_rate=0.15,
            tournament_size=3,
            elitism_rate=0.2,
            # 动态种群大小配置
            use_dynamic_population=True,
            base_population_size=50,  # 基础种群大小
            population_scale_factor=3.0,  # 种群缩放因子：每增加1维，种群增加3个个体
            min_population_size=50,  # 最小种群大小
            max_population_size=800  # 最大种群大小
        )
        self.ga_tabpfn_integration = GATabPFNIntegration(
            axus=self.random_embedding,
            benchmark=self.benchmark,
            ga_config=ga_config,
            tabpfn_n_bins=5,
            ga_generations=15,
            device=self.device.type if hasattr(self.device, 'type') else str(self.device),
            # 🔧 关键修复：使用gin配置中的global_model_type，默认为'gp'
        )

        # 控制全局搜索的频率 - 修改为每次迭代都进行
        self.enable_ga_every_iteration = True  # 每次迭代都进行GA+TabPFN
        self.min_data_for_tabpfn = 5  # TabPFN需要的最少数据点数

    def _reset_local_data(
            self
    ):
        # saving tr local data
        self.x_tr = torch.empty(0, self.random_embedding.target_dim, dtype=self.dtype)
        self.x_up_tr = torch.empty(
            0, self.benchmark.representation_dim, dtype=self.dtype
        )
        self.fx_tr = torch.empty(0, dtype=self.dtype)

    def _adjust_number_bins_on_split(
            self
    ):
        """
        Adjusts the number of new bins on split to the number of new bins that minimizes the difference between the
        true final target dimensionality and the desired final dimensionality.

        Returns:
            None

        """
        possible_bin_sizes = torch.arange(
            1,
            torch.floor(
                torch.log2(torch.tensor(self.desired_final_dimensionality))
            ).item(),
        )

        if possible_bin_sizes.numel() == 0:
            possible_bin_sizes = torch.tensor([1])

        best_bin_size = (
                torch.argmin(
                    torch.abs(
                        self.initial_target_dimensionality
                        * (1 + possible_bin_sizes) ** self._n_splits
                        - self.desired_final_dimensionality
                    )
                )
                + 1
        )
        if best_bin_size != self.number_new_bins_on_split:
            logging.debug(
                f"Updating number of new bins from {self.number_new_bins_on_split} to {best_bin_size}"
            )
            self.number_new_bins_on_split = best_bin_size.item()
            self._n_splits = math.ceil(
                math.log(
                    self.desired_final_dimensionality
                    / self.initial_target_dimensionality,
                    self.number_new_bins_on_split + 1,
                )
            )

    def _split_budget(
            self,
            target_dimensionality: int
    ) -> int:
        """
        Calculates the number of evaluations to be used for the split with target_dimensionality.

        Args:
            target_dimensionality: the target dimensionality of the split

        Returns:
            the number of evaluations to be used for the split with target_dimensionality

        """
        total_budget = (
                self.maximum_number_evaluations_until_input_dim - self.number_initial_points
        )

        if target_dimensionality >= self.benchmark.dim:
            return min(
                10 * target_dimensionality,
                self.maximum_number_evaluations - self._n_evals,
            )
        split_budget = round(
            -(self.number_new_bins_on_split * total_budget * target_dimensionality)
            / (
                    self.initial_target_dimensionality
                    * (1 - (self.number_new_bins_on_split + 1) ** (self._n_splits + 1))
            )
        )
        return min(2 ** target_dimensionality, split_budget)

    def sample_init(
            self
    ):
        """
        Samples the initial points, evaluates them, and adds them to the observations.
        Increases the number of evaluations by the number of initial points.

        Returns:
            None

        """
        types_points_and_indices = {pt: (None, None) for pt in ParameterType}
        # sample initial points for each parameter type present in the benchmark
        for parameter_type in self.benchmark.unique_parameter_types:
            # find number of parameters of type parameter_type
            bins_of_type: list[Bin] = self.random_embedding.bins_of_type(parameter_type)
            indices_of_type = torch.concat(
                [
                    self.random_embedding.bins_and_indices_of_type(parameter_type)[i][1]
                    for i in range(len(bins_of_type))
                ]
            )
            match parameter_type:
                case ParameterType.BINARY:
                    _x_init = sample_binary(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.CONTINUOUS:
                    _x_init = sample_continuous(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.CATEGORICAL:
                    _x_init = sample_categorical(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.ORDINAL:
                    raise NotImplementedError(
                        "Ordinal parameters are not supported yet."
                    )
                case _:
                    raise ValueError(f"Unknown parameter type {parameter_type}.")
            types_points_and_indices[parameter_type] = (_x_init, indices_of_type)

        # logging.info(f"查看每一种类型变量在那几个维度{types_points_and_indices}")

        x_init = construct_mixed_point(
            size=self.number_initial_points,
            binary_indices=types_points_and_indices[ParameterType.BINARY][1],
            continuous_indices=types_points_and_indices[ParameterType.CONTINUOUS][1],
            categorical_indices=types_points_and_indices[ParameterType.CATEGORICAL][1],
            ordinal_indices=types_points_and_indices[ParameterType.ORDINAL][1],
            x_binary=types_points_and_indices[ParameterType.BINARY][0],
            x_continuous=types_points_and_indices[ParameterType.CONTINUOUS][0],
            x_categorical=types_points_and_indices[ParameterType.CATEGORICAL][0],
            x_ordinal=types_points_and_indices[ParameterType.ORDINAL][0],
        )

        # 🎯 关键修复：对于纯二分问题，避免from_1_around_origin产生0.5值
        projected = self.random_embedding.project_up(x_init.T).T

        # 检查是否为纯二分问题
        is_pure_binary = True
        if hasattr(self.random_embedding, 'bins_and_indices_of_type'):
            for _, indices in self.random_embedding.bins_and_indices_of_type(ParameterType.CONTINUOUS):
                if len(indices) > 0:
                    is_pure_binary = False
                    break
            for _, indices in self.random_embedding.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                if len(indices) > 0:
                    is_pure_binary = False
                    break

        if is_pure_binary:
            # 纯二分问题：直接将{-1,1}映射到{0,1}，避免0.5值
            x_init_up = (projected + 1) / 2
            # 确保严格的{0,1}值
            x_init_up = torch.round(x_init_up)
            x_init_up = torch.clamp(x_init_up, 0.0, 1.0)
        else:
            # 混合变量问题：使用from_1_around_origin
            x_init_up = from_1_around_origin(
                x=projected,
                lb=self.benchmark.lb_vec,
                ub=self.benchmark.ub_vec,
            )


        fx_init = self.benchmark(x_init_up)
        # logging.info(f"完整点{x_init}")
        # logging.info(f"映射回高维的完整点{x_init_up}")
        # logging.info(f"点的目标值{fx_init}")


        self._add_data_to_tr_observations(
            xs_down=x_init,
            xs_up=x_init_up,
            fxs=fx_init,
        )

        self._n_evals += self.number_initial_points

    def run(
            self
    ):
        """
        Runs the algorithm.

        Returns:
            None

        """

        self.sample_init()

        while self._n_evals < self.maximum_number_evaluations:
            axus = self.random_embedding
            x = self.x_tr
            fx = self.fx_tr

            # 全局搜索：每次迭代都使用GA+TabPFN预测最佳TR中心
            predicted_center = None
            if (self.enable_ga_every_iteration and
                len(self.x_up_global) >= self.min_data_for_tabpfn):  # 确保有足够的数据

                try:
                    # logging.info(f"🧬 执行全局搜索 (评估次数: {self._n_evals})")

                    # 运行遗传算法进行全局搜索
                    best_low_dim, best_high_dim = self.ga_tabpfn_integration.run_global_search(
                        existing_X=self.x_up_global,
                        existing_y=self.fx_global
                    )

                    # 🚀 策略切换机制：TabPFN策略 vs 历史最优策略
                    if self.current_strategy == 1:  # 历史最优策略
                        if len(self.x_global) > 0:
                            # 使用历史最优值作为TR中心
                            best_idx = torch.argmin(self.fx_global)
                            predicted_center = self.x_global[best_idx]
                            logging.info(f"🏆 [{self.strategy_names[self.current_strategy]}] 使用历史最优值作为TR中心: {predicted_center[:5]}... (fx={self.fx_global[best_idx].item():.3f})")
                        else:
                            # 备选方案：随机中心
                            predicted_center = torch.randint(0, 2, (self.axus.target_dim,), dtype=torch.float64) * 2 - 1
                            logging.info(f"🏆 [{self.strategy_names[self.current_strategy]}] 无历史数据，使用随机中心")
                    else:  # TabPFN策略
                        # 🚀 准备历史最优点用于增加多样性
                        historical_points = None
                        current_best_low = None
                        if len(self.x_up_global) > 0:
                            # 获取前5个最优的历史点
                            sorted_indices = torch.argsort(self.fx_global)
                            top_k = min(5, len(self.x_up_global))
                            historical_points = self.x_up_global[sorted_indices[:top_k]]

                            # 🎯 关键修复：获取当前最优解的低维表示
                            best_idx = sorted_indices[0]
                            current_best_low = self.x_global[best_idx]

                        # 使用TabPFN预测最佳TR中心点
                        try:
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 开始全局搜索")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 全局搜索输入 - X设备: {self.x_up_global.device}, 形状: {self.x_up_global.shape}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 全局搜索输入 - y设备: {self.fx_global.device}, 形状: {self.fx_global.shape}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] self.device: {self.device}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] historical_points设备: {historical_points.device if historical_points is not None and hasattr(historical_points, 'device') else 'N/A'}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] current_best_low设备: {current_best_low.device if current_best_low is not None and hasattr(current_best_low, 'device') else 'N/A'}")
                            
                            # 远程调用GA集成的全局搜索
                            predicted_center = self.ga_tabpfn_integration.predict_best_center_with_tabpfn(
                                existing_X=self.x_up_global,
                                existing_y=self.fx_global,
                                historical_points=historical_points,
                                current_best_low=current_best_low
                            )
                            
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 全局搜索输出 - predicted_center设备: {predicted_center.device if hasattr(predicted_center, 'device') else 'N/A'}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 全局搜索输出 - predicted_center形状: {predicted_center.shape if hasattr(predicted_center, 'shape') else 'N/A'}")
                            # logging.info(f"🔍 [BOUNCE_DEBUG] 全局搜索输出 - predicted_center值: {predicted_center[:5] if hasattr(predicted_center, '__getitem__') else predicted_center}...")
                            
                            logging.info(f"🎯 [{self.ga_tabpfn_integration.global_model_type}] 全局模型预测的TR中心: {predicted_center[:5]}...")
                            
                        except Exception as e:
                            logging.error(f"🛑 [BOUNCE_DEBUG] 全局搜索异常: {e}")
                            import traceback
                            logging.error(f"🛑 [BOUNCE_DEBUG] 全局搜索详细错误:\n{traceback.format_exc()}")
                            predicted_center = None
                            raise  # 重新抛出异常
                except Exception as e:
                    logging.warning(f"全局搜索失败，使用默认策略: {e}")
                    predicted_center = None

            # normalize data
            mean = torch.mean(fx)
            std = torch.std(fx)
            if std == 0:
                std += 1
            fx_scaled = (fx - mean) / std
            x_scaled = (x + 1) / 2

            if self.device == "cuda":
                fx_scaled = fx_scaled.to(self.device)
                x_scaled = x_scaled.to(self.device)

            # Select the kernel
            model, train_x, train_fx = get_gp(
                axus=axus,
                x=x_scaled,
                fx=-fx_scaled,
            )

            use_scipy_lbfgs = self.use_scipy_lbfgs and (
                    self.max_lbfgs_iters is None or len(train_x) <= self.max_lbfgs_iters
            )
            fit_mll(
                model=model,
                train_x=train_x,
                train_fx=-train_fx,
                max_cholesky_size=self.max_cholesky_size,
                use_scipy_lbfgs=use_scipy_lbfgs,
            )
            acquisition_function = None
            sampler = None

            if self.batch_size > 1:
                # we don't set the acquisition function here, because it needs to be redefined
                # for each batch item to be able to condition on the earlier batch items
                # note that this is the only place where we don't use the acquisition function
                sampler = SobolQMCNormalSampler(Size([1024]), seed=self._n_evals)
            else:
                # use analytical EI for batch size 1
                acquisition_function = ExpectedImprovement(
                    model=model, best_f=(-fx_scaled).max().item()
                )

            if self.benchmark.is_discrete:
                # 准备TR中心点：优先使用GA+TabPFN预测的中心，否则使用默认策略
                x_bests_for_tr = None
                if predicted_center is not None:
                    # 确保预测中心点在正确设备上
                    predicted_center = predicted_center.to(device=self.device)
                    # 将预测的中心点转换为[0,1]范围并扩展为batch
                    center_01 = (predicted_center + 1) / 2
                    x_bests_for_tr = center_01.unsqueeze(0).repeat(self.batch_size, 1).to(dtype=x_scaled.dtype, device=x_scaled.device)
                    logging.debug(f"使用GA+全局模型预测的TR中心点")

                x_best, fx_best, tr_state = create_candidates_discrete(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    model=model,
                    axus=axus,
                    trust_region=self.trust_region,
                    device=self.device,
                    batch_size=self.batch_size,
                    x_bests=x_bests_for_tr,
                    acquisition_function=acquisition_function,
                    sampler=sampler,
                    use_surrogate_local=self.use_surrogate_local,  # 🚀 传递代理模型局部搜索标志
                    local_model_type=self.local_model_type,  # 🚀 传递局部模型类型
                    surrogate_manager=self.ga_tabpfn_integration.surrogate_manager,  # 🚀 传递代理模型管理器
                )
                fx_best = fx_best * std + mean
            elif self.benchmark.is_continuous:
                x_best, fx_best, tr_state = create_candidates_continuous(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    acquisition_function=acquisition_function,
                    model=model,
                    axus=axus,
                    trust_region=self.trust_region,
                    device=self.device,
                    batch_size=self.batch_size,
                    sampler=sampler,
                )
                fx_best = fx_best * std + mean
            # TODO don't use elif True here but check for the exact type
            elif True:
                # Scale the function values

                continuous_indices = torch.tensor(
                    [
                        i
                        for b, i in axus.bins_and_indices_of_type(
                        ParameterType.CONTINUOUS
                    )
                    ],
                    device=self.device  # 确保索引在正确设备上
                )
                x_best = None
                for interleaved_step in tqdm(range(self.n_interleaved), desc="☯ Interleaved steps"):
                    # 在第一步使用GA+TabPFN预测的中心点
                    x_bests_for_interleaved = x_best
                    if interleaved_step == 0 and predicted_center is not None:
                        # 确保预测中心点在正确设备上
                        predicted_center = predicted_center.to(device=x_scaled.device)
                        x_bests_for_interleaved = predicted_center.unsqueeze(0).repeat(self.batch_size, 1).to(dtype=x_scaled.dtype, device=x_scaled.device)
                        logging.debug(f"混合变量优化第一步使用GA+TabPFN预测的中心点")

                    x_best, fx_best, tr_state = create_candidates_discrete(
                        x_scaled=x_scaled,
                        fx_scaled=fx_scaled,
                        axus=axus,
                        model=model,
                        trust_region=self.trust_region,
                        device=self.device,
                        batch_size=self.batch_size,
                        x_bests=x_bests_for_interleaved,  # expects [-1, 1],
                        acquisition_function=acquisition_function,
                        sampler=sampler,
                        use_surrogate_local=self.use_surrogate_local,  # 🚀 传递代理模型局部搜索标志
                        local_model_type=self.local_model_type,  # 🚀 传递局部模型类型
                        surrogate_manager=self.ga_tabpfn_integration.surrogate_manager,  # 🚀 传递代理模型管理器
                    )
                    x_best = x_best.reshape(-1, axus.target_dim)

                    # 对于连续变量，使用当前最佳点或预测中心点
                    if interleaved_step == 0 and predicted_center is not None:
                        # 第一步：连续变量部分使用预测中心点
                        # 确保预测中心点在正确设备上
                        true_center = predicted_center.to(device=x_best.device)
                    else:
                        # 后续步骤：使用当前最佳点
                        true_center = x[fx.argmin()].to(device=x_best.device)
                    x_best[:, continuous_indices] = true_center[continuous_indices]
                    x_best, fx_best, tr_state = create_candidates_continuous(
                        x_scaled=x_scaled,
                        fx_scaled=fx_scaled,
                        axus=axus,
                        trust_region=self.trust_region,
                        device=self.device,
                        indices_to_optimize=continuous_indices,
                        x_bests=x_best,  # expects [-1, 1]
                        acquisition_function=acquisition_function,
                        model=model,
                        batch_size=self.batch_size,
                        sampler=sampler,
                    )
                    fx_best = fx_best * std + mean
                    x_best = x_best.reshape(-1, axus.target_dim)
                x_best = x_best
            else:
                raise NotImplementedError(
                    "Only binary and continuous benchmarks are supported."
                )
            # get the GP hyperparameters as a dictionary
            self.save_tr_state(tr_state)
            minimum_xs = x_best.detach().cpu()
            minimum_fxs = fx_best.detach().cpu()

            fx_batches = minimum_fxs

            cand_batch = torch.empty((self.batch_size, self.benchmark.representation_dim), dtype=self.dtype)
            xs_low_dim = x_best.detach().cpu()
            # 🎯 关键修复：对于纯二分问题，避免from_1_around_origin产生0.5值
            projected = self.random_embedding.project_up(xs_low_dim.T).T

            # 检查是否为纯二分问题
            is_pure_binary = True
            if hasattr(self.random_embedding, 'bins_and_indices_of_type'):
                for _, indices in self.random_embedding.bins_and_indices_of_type(ParameterType.CONTINUOUS):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break
                for _, indices in self.random_embedding.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                    if len(indices) > 0:
                        is_pure_binary = False
                        break

            if is_pure_binary:
                # 纯二分问题：直接将{-1,1}映射到{0,1}，避免0.5值
                xs_high_dim = (projected + 1) / 2
                # 确保严格的{0,1}值
                xs_high_dim = torch.round(xs_high_dim)
                xs_high_dim = torch.clamp(xs_high_dim, 0.0, 1.0)
            else:
                # 混合变量问题：使用from_1_around_origin
                xs_high_dim = from_1_around_origin(
                    projected,
                    lb=self.benchmark.lb_vec, ub=self.benchmark.ub_vec
                )

            cand_batch[:, :] = xs_high_dim

            # Sample on the candidate points
            y_next = self.benchmark(cand_batch)

            # 🎆 增加性能跟踪：记录集成模型性能用于权重更新
            if (hasattr(self.ga_tabpfn_integration, 'enable_ensemble') and 
                self.ga_tabpfn_integration.enable_ensemble and 
                hasattr(self.ga_tabpfn_integration, 'ensemble_manager') and 
                self.ga_tabpfn_integration.ensemble_manager is not None):
                
                try:
                    # 获取集成模型对候选点的预测
                    ensemble_manager = self.ga_tabpfn_integration.ensemble_manager
                    if ensemble_manager.is_fitted:
                        # 将低维点投影到高维空间进行预测
                        candidates_high_dim = []
                        for i in range(len(xs_low_dim)):
                            candidate = xs_low_dim[i]
                            if candidate.dim() == 1:
                                candidate = candidate.unsqueeze(0)
                            high_dim = self.random_embedding.project_up(candidate.T).T
                            candidates_high_dim.append(high_dim.squeeze(0))
                        
                        if candidates_high_dim:
                            candidates_high_dim = torch.stack(candidates_high_dim)
                            
                            # 获取各模型的预测
                            predictions_dict = ensemble_manager.predict_all_models(candidates_high_dim)
                            
                            # 🔧 关键修复：检查预测结果并记录性能
                            if predictions_dict:
                                logging.debug(f"📊 获取到{len(predictions_dict)}个模型的预测结果")
                                for model_name, predictions in predictions_dict.items():
                                    logging.debug(f"   {model_name.upper()}: {len(predictions)}个预测值")
                                
                                # 记录性能：比较预测值与实际评估结果
                                if len(y_next) == len(candidates_high_dim):
                                    ensemble_manager.record_performance(predictions_dict, y_next)
                                    logging.debug(f"📊 已记录{len(y_next)}个点的性能数据用于权重更新")
                                    
                                    # 🔧 强制输出权重更新信息
                                    current_weights = ensemble_manager.weight_manager.get_weights()
                                    logging.debug(f"🎯 权重更新后: {current_weights}")
                                else:
                                    logging.warning(f"⚠️ 预测点数({len(candidates_high_dim)})与评估点数({len(y_next)})不匹配，跳过性能记录")
                            else:
                                logging.warning(f"⚠️ 未获取到任何模型预测结果")
                except Exception as e:
                    logging.debug(f"性能记录失败: {e}")
                    import traceback
                    logging.debug(f"详细错误: {traceback.format_exc()}")

            best_fx = self.fx_tr.min()
            current_best_fx = y_next.min().item()

            # 🚀 策略切换机制的停滞检测逻辑
            if torch.min(y_next) < best_fx:
                logging.info(
                    f"✨ Iteration {self._n_evals}: {BColors.OKGREEN}New incumbent function value {current_best_fx:.3f}{BColors.ENDC} [{self.strategy_names[self.current_strategy]}有效]"
                )
                # 有改进，重置停滞计数器
                self.strategy_stagnation_counter = 0
                self.last_best_fx = current_best_fx
            else:
                logging.info(
                    f"🚀 Iteration {self._n_evals}: No improvement. Best function value {best_fx.item():.3f} [{self.strategy_names[self.current_strategy]}]"
                )
                # 无改进，增加当前策略的停滞计数器
                self.strategy_stagnation_counter += 1

                # 🚀 策略切换逻辑
                if self.strategy_stagnation_counter >= self.strategy_switch_threshold:
                    old_strategy = self.current_strategy
                    self.current_strategy = 1 - self.current_strategy  # 切换策略 (0<->1)
                    self.strategy_stagnation_counter = 0  # 重置计数器

                    logging.warning(f"🔄 策略切换: {self.strategy_names[old_strategy]} -> {self.strategy_names[self.current_strategy]} (停滞{self.strategy_switch_threshold}步)")
                elif self.strategy_stagnation_counter >= self.strategy_switch_threshold // 2:
                    logging.debug(f"🔍 [{self.strategy_names[self.current_strategy]}] 停滞{self.strategy_stagnation_counter}步")

            # Calculate the estimated trust region dimensionality
            tr_dim = self._forecasted_tr_dim
            # Number of times this trust region has been selected
            # Remaining budget for this trust region
            remaining_budget = self._all_split_budgets[tr_dim]
            remaining_budget = min(
                remaining_budget, self.maximum_number_evaluations - self._n_evals
            )
            remaining_budget = max(remaining_budget, 1)
            tr = self.trust_region
            factor = (tr.length_min_discrete / tr.length_discrete_continuous) ** (
                    1 / remaining_budget
            )
            factor **= self.batch_size
            factor = np.clip(factor, a_min=1e-10, a_max=None)
            logging.debug(
                f"🔎 Adjusting trust region by factor {factor.item():.3f}. Remaining budget: {remaining_budget}"
            )
            update_tr_state(
                trust_region=self.trust_region,
                fx_next=y_next.min(),
                fx_incumbent=self.fx_tr.min(),
                adjustment_factor=factor,
            )

            logging.debug(
                f"📏 Trust region has length {tr.length_discrete_continuous:.3f} and minium l {tr.length_min_discrete:.3f}"
            )

            self._all_split_budgets[tr_dim] = (
                    self._all_split_budgets[tr_dim] - self.batch_size
            )


            self._add_data_to_tr_observations(
                xs_down=xs_low_dim,
                xs_up=xs_high_dim,
                fxs=y_next.reshape(-1),
            )
            # 新增：记录当前评估次数和当前最优值
            self.eval_history.append([self._n_evals, self.fx_tr.min().item()])
            
            self._n_evals += self.batch_size

            # Splitting trust regions that terminated
            if self.trust_region.terminated:
                if self.random_embedding.target_dim < self.benchmark.representation_dim:
                    # Full dim is not reached yet
                    logging.info(f"✂️ Splitting trust region")

                    index_mapping = self.random_embedding.split(
                        self.number_new_bins_on_split
                    )

                    # move data to higher-dimensional space
                    self.x_tr = join_data(self.x_tr, index_mapping)
                    self.x_global = join_data(self.x_global, index_mapping)

                    self.trust_region = TrustRegion(
                        dimensionality=self.random_embedding.target_dim
                    )
                    if self.tr_splits < self._n_splits:
                        self.tr_splits += 1

                    self.split_budget = self._split_budget(
                        self.initial_target_dimensionality
                        * (self.number_new_bins_on_split + 1) ** self.tr_splits
                    )
                else:
                    # Full dim is reached
                    logging.info(
                        f"🏁 Reached full dimensionality. Restarting with new random samples."
                    )
                    self.split_budget = self._split_budget(
                        self.random_embedding.input_dim
                    )
                    # Reset the last split budget
                    self._all_split_budgets[self._forecasted_tr_dim] = self.split_budget

                    # empty tr data, does not delete the global data
                    self._reset_local_data()

                    # reset the trust region
                    self.trust_region.reset()

                    self.sample_init()
        # 保存全局结果为csv，列名为x1,x2,...,fx，且不使用科学计数法
        df = pd.DataFrame(
            np.hstack((
                self.x_up_global.detach().cpu().numpy(),
                self.fx_global.detach().cpu().numpy().reshape(-1, 1),
            )),
            columns=[f"x{i+1}" for i in range(self.x_up_global.shape[1])] + ["fx"]
        )
        df.to_csv(os.path.join(self.results_dir, "results.csv"), index=False, float_format="%.8f", encoding="utf-8-sig")

        # 保存评估历史，列名为中文，且不使用科学计数法
        eval_history_df = pd.DataFrame(self.eval_history, columns=["评估次数", "当前最优值"])
        eval_history_df.to_csv(os.path.join(self.results_dir, "eval_history.csv"), index=False, float_format="%.8f", encoding="utf-8-sig")


    @property
    def _forecasted_tr_dim(
            self
    ) -> int:
        """
        Calculate the estimated trust region dimensionality.

        Returns:
            the estimated trust region dimensionality

        """

        return self.initial_target_dimensionality * (
                1 + self.number_new_bins_on_split
        ) ** (self.tr_splits)

    def _add_data_to_tr_observations(
            self,
            xs_down: torch.Tensor,
            xs_up: torch.Tensor,
            fxs: torch.Tensor,
    ):
        """
        Add data to the tr local observations and save the selected trust regions to disk.

        Args:
            xs_down: the low-dimensional points that were evaluated in the trust regions
            xs_up:  the high-dimensional points that were evaluated in the trust regions
            fxs:  the function values of the high-dimensional points that were evaluated in the trust regions

        Returns:
            None

        """

        self.fx_tr = torch.cat(
            (
                self.fx_tr,
                fxs.reshape(-1).detach().cpu(),
            )
        )
        self.x_tr = torch.vstack(
            (
                self.x_tr,
                xs_down.detach().cpu(),
            )
        )
        self.x_up_tr = torch.vstack(
            (
                self.x_up_tr,
                xs_up.detach().cpu(),
            )
        )

        self._add_data_to_global_observations(
            xs_down=xs_down,
            xs_up=xs_up,
            fxs=fxs,
        )

    def _add_data_to_global_observations(
            self,
            xs_down: torch.Tensor,
            xs_up: torch.Tensor,
            fxs: torch.Tensor,
    ):
        """
        Add data to the global observations and save the selected trust regions to disk.

        Args:
            xs_down: the low-dimensional points that were evaluated in the trust regions
            xs_up:  the high-dimensional points that were evaluated in the trust regions
            fxs:  the function values of the high-dimensional points that were evaluated in the trust regions

        Returns:
            None

        """

        self.fx_global = torch.cat(
            (
                self.fx_global,
                fxs.reshape(-1).detach().cpu(),
            )
        )
        self.x_global = torch.vstack(
            (
                self.x_global,
                xs_down.detach().cpu(),
            )
        )
        self.x_up_global = torch.vstack(
            (
                self.x_up_global,
                xs_up.detach().cpu(),
            )
        )

    def save_tr_state(
            self,
            tr_state: dict[str, Union[float, np.ndarray]],
    ):
        """
        Save the trust region state to disk.

        Args:
            tr_state: the trust region state

        Returns:
            None

        """
        for key, value in tr_state.items():
            with lzma.open(os.path.join(self.results_dir, f"{key}.csv.xz"), "a") as f:
                np.savetxt(f, value, delimiter=",")
