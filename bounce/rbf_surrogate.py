import logging
import torch
import numpy as np
from typing import Optional, Tuple
from scipy.spatial.distance import cdist
from scipy.linalg import solve
from sklearn.preprocessing import StandardScaler

from bounce.device_manager import DeviceManager


class RBFGlobalSurrogate:
    """RBF全局代理模型，用于替代TabPFN进行全局搜索"""
    
    def __init__(self, benchmark, axus, device: str = 'cpu', kernel: str = 'gaussian', epsilon: float = 1.0):
        """
        初始化RBF代理模型
        
        Args:
            benchmark: 基准函数
            axus: AxUS投影对象
            device: 计算设备
            kernel: RBF核函数类型 ('gaussian', 'multiquadric', 'inverse_multiquadric', 'linear', 'cubic', 'quintic', 'thin_plate')
            epsilon: RBF形状参数
        """
        self.benchmark = benchmark
        self.axus = axus
        self.device_manager = DeviceManager(device)
        self.kernel = kernel
        self.epsilon = epsilon
        self.is_fitted = False
        
        # 存储训练数据
        self.X_train = None
        self.y_train = None
        self.weights = None
        
        # 数据标准化器
        self.X_scaler = StandardScaler()
        self.y_scaler = StandardScaler()
        
        # 存储数据供设备管理器使用
        self.X_train_scaled = None
        self.y_scaler_mean = None
        self.y_scaler_std = None
        
        # logging.info(f"RBF全局代理模型初始化完成，设备: {self.device_manager.device}, 核函数: {kernel}, epsilon: {epsilon}")
    
    def _rbf_kernel(self, X1: np.ndarray, X2: np.ndarray) -> np.ndarray:
        """
        计算RBF核矩阵
        
        Args:
            X1: 输入点1，形状为 (n1, d)
            X2: 输入点2，形状为 (n2, d)
            
        Returns:
            核矩阵，形状为 (n1, n2)
        """
        # 计算欧氏距离
        distances = cdist(X1, X2, metric='euclidean')
        
        if self.kernel == 'gaussian':
            return np.exp(-(self.epsilon * distances) ** 2)
        elif self.kernel == 'multiquadric':
            return np.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'inverse_multiquadric':
            return 1.0 / np.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'linear':
            return distances
        elif self.kernel == 'cubic':
            return distances ** 3
        elif self.kernel == 'quintic':
            return distances ** 5
        elif self.kernel == 'thin_plate':
            # 避免log(0)
            distances = np.maximum(distances, 1e-12)
            return distances ** 2 * np.log(distances)
        else:
            raise ValueError(f"不支持的RBF核函数: {self.kernel}")
    
    def fit(self, X: torch.Tensor, y: torch.Tensor) -> None:
        """
        训练RBF模型
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            y: 目标值，形状为 (n_samples,)
        """
        try:
            # 转换数据类型（暂时使用NumPy进行计算）
            if isinstance(X, torch.Tensor):
                X_np = self.device_manager.tensor_to_numpy(X)
            else:
                X_np = np.array(X)
                
            if isinstance(y, torch.Tensor):
                y_np = self.device_manager.tensor_to_numpy(y)
            else:
                y_np = np.array(y)
            
            # 确保数据形状正确
            if X_np.ndim == 1:
                X_np = X_np.reshape(1, -1)
            if y_np.ndim > 1:
                y_np = y_np.flatten()
            
            # 数据标准化
            X_scaled = self.X_scaler.fit_transform(X_np)
            y_scaled = self.y_scaler.fit_transform(y_np.reshape(-1, 1)).flatten()
            
            # 存储训练数据
            self.X_train = X_scaled
            self.y_train = y_scaled
            
            # 存储数据供设备管理器使用
            self.X_train_scaled = X_scaled
            self.y_scaler_mean = self.y_scaler.mean_[0]
            self.y_scaler_std = np.sqrt(self.y_scaler.var_[0])
            
            # 构建RBF核矩阵
            K = self._rbf_kernel(X_scaled, X_scaled)
            
            # 添加正则化项以提高数值稳定性
            regularization = 1e-8
            K += regularization * np.eye(K.shape[0])
            
            # 求解线性系统 K * weights = y
            try:
                self.weights = solve(K, y_scaled, assume_a='pos')
            except np.linalg.LinAlgError:
                # 如果矩阵不是正定的，使用更大的正则化
                regularization = 1e-6
                K += regularization * np.eye(K.shape[0])
                self.weights = solve(K, y_scaled)
            
            self.is_fitted = True
            
            # logging.info(f"RBF模型训练完成，设备: {self.device_manager.device}, 训练样本数: {len(X_np)}")
            
        except Exception as e:
            logging.error(f"RBF模型训练失败: {e}")
            self.is_fitted = False
            raise e
    
    def predict(self, X: torch.Tensor) -> torch.Tensor:
        """
        使用RBF模型进行预测
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            
        Returns:
            预测值，形状为 (n_samples,)
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        # 确保输入在正确设备
        X_test = self.device_manager.ensure_tensor_device(X)
        
        try:
            # 选择计算策略
            if self.device_manager.is_cuda and X_test.shape[0] > 100:
                return self._predict_cuda(X_test)
            else:
                return self._predict_cpu_numpy(X_test)
                
        except Exception as e:
            logging.warning(f"RBF CUDA预测失败: {e}, 回退到CPU")
            return self._predict_cpu_numpy(X_test)
            
    def _predict_cuda(self, X: torch.Tensor) -> torch.Tensor:
        """
        CUDA张量计算RBF预测
        
        Args:
            X: 输入张量，已在正确设备上
            
        Returns:
            预测值
        """
        # 确保训练数据在CUDA上
        X_train_cuda = self.device_manager.safe_to_device(self.X_train_scaled)
        weights_cuda = self.device_manager.safe_to_device(self.weights)
        
        # 简单的数据标准化（使用存储的参数）
        X_mean = self.device_manager.safe_to_device(self.X_scaler.mean_)
        X_scale = self.device_manager.safe_to_device(self.X_scaler.scale_)
        
        # 标准化输入
        X_scaled = (X - X_mean) / X_scale
        
        # CUDA RBF计算
        distances = torch.cdist(X_scaled, X_train_cuda)
        kernel_matrix = self._rbf_kernel_cuda(distances)
        predictions = torch.matmul(kernel_matrix, weights_cuda)
        
        # 反标准化
        predictions = predictions * self.y_scaler_std + self.y_scaler_mean
        
        return predictions
    
    def _rbf_kernel_cuda(self, distances: torch.Tensor) -> torch.Tensor:
        """
        CUDA上的RBF核计算
        
        Args:
            distances: 距离矩阵
            
        Returns:
            核矩阵
        """
        if self.kernel == 'gaussian':
            return torch.exp(-(self.epsilon * distances) ** 2)
        elif self.kernel == 'multiquadric':
            return torch.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'inverse_multiquadric':
            return 1.0 / torch.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'linear':
            return distances
        elif self.kernel == 'cubic':
            return distances ** 3
        elif self.kernel == 'quintic':
            return distances ** 5
        elif self.kernel == 'thin_plate':
            # 避免log(0)
            distances = torch.clamp(distances, min=1e-12)
            return distances ** 2 * torch.log(distances)
        else:
            # 默认使用高斯核
            return torch.exp(-(self.epsilon * distances) ** 2)
    
    def _predict_cpu_numpy(self, X: torch.Tensor) -> torch.Tensor:
        """
        CPU NumPy计算RBF预测
        
        Args:
            X: 输入张量
            
        Returns:
            预测值
        """
        # 转换为NumPy进行计算
        X_np = self.device_manager.tensor_to_numpy(X)
        
        if X_np.ndim == 1:
            X_np = X_np.reshape(1, -1)
        
        # 调试信息
        logging.debug(f"RBF预测输入: 形状={X_np.shape}, 范围=[{X_np.min():.6f}, {X_np.max():.6f}]")
        
        if X_np.shape[0] > 1:
            input_variance = np.var(X_np, axis=0)
            logging.debug(f"RBF输入方差: {input_variance.mean():.8f}")
            if input_variance.mean() < 1e-10:
                logging.warning(f"⚠️ RBF输入数据方差过小，所有候选点几乎相同！")
        
        # 数据标准化
        X_scaled = self.X_scaler.transform(X_np)
        
        # 检查标准化后的数据
        logging.debug(f"RBF标准化后: 形状={X_scaled.shape}, 范围=[{X_scaled.min():.6f}, {X_scaled.max():.6f}]")
        if X_scaled.shape[0] > 1:
            norm_variance = np.var(X_scaled, axis=0)
            logging.debug(f"RBF标准化后方差: {norm_variance.mean():.8f}")
            if norm_variance.mean() < 1e-10:
                logging.warning(f"⚠️ RBF标准化后数据方差过小，可能导致预测相同！")
        
        # RBF计算
        distances = cdist(X_scaled, self.X_train_scaled)
        kernel_matrix = self._rbf_kernel_numpy(distances)
        predictions_np = np.dot(kernel_matrix, self.weights)
        
        # 反标准化
        predictions_np = predictions_np * self.y_scaler.scale_ + self.y_scaler.mean_
        
        # 调试信息
        # logging.debug(f"RBF预测结果: {predictions_np}")
        # if len(predictions_np) > 1:
        #     pred_variance = np.var(predictions_np)
        #     logging.debug(f"RBF预测方差: {pred_variance:.8f}")
        #     if pred_variance < 1e-8:
        #         logging.warning(f"⚠️ RBF预测值方差过小，所有预测几乎相同：{predictions_np[0]:.6f}")
        
        # 转换回张量并移到正确设备
        return self.device_manager.safe_to_device(predictions_np)
    
    def _rbf_kernel_numpy(self, distances: np.ndarray) -> np.ndarray:
        """
        NumPy版本的RBF核计算
        
        Args:
            distances: 距离矩阵
            
        Returns:
            核矩阵
        """
        if self.kernel == 'gaussian':
            return np.exp(-(self.epsilon * distances) ** 2)
        elif self.kernel == 'multiquadric':
            return np.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'inverse_multiquadric':
            return 1.0 / np.sqrt(1 + (self.epsilon * distances) ** 2)
        elif self.kernel == 'linear':
            return distances
        elif self.kernel == 'cubic':
            return distances ** 3
        elif self.kernel == 'quintic':
            return distances ** 5
        elif self.kernel == 'thin_plate':
            # 避免log(0)
            distances = np.maximum(distances, 1e-12)
            return distances ** 2 * np.log(distances)
        else:
            raise ValueError(f"不支持的RBF核函数: {self.kernel}")
    
    def predict_quality(self, X: torch.Tensor) -> torch.Tensor:
        """
        预测质量分数（用于与TabPFN接口兼容）
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            
        Returns:
            质量分数，形状为 (n_samples,)，值越小表示质量越好
        """
        # 对于最小化问题，直接返回预测值
        predictions = self.predict(X)
        return predictions
    
    def update(self, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        增量更新模型（重新训练）
        
        Args:
            X_new: 新的输入特征 - 可能是完整数据集或新增数据
            y_new: 新的目标值 - 可能是完整数据集或新增数据
        """
        if not self.is_fitted:
            # 如果模型未训练，直接训练
            self.fit(X_new, y_new)
            return
        
        try:
            # 使用设备管理器转换新数据
            X_new_np = self.device_manager.tensor_to_numpy(X_new) if isinstance(X_new, torch.Tensor) else np.array(X_new)
            y_new_np = self.device_manager.tensor_to_numpy(y_new) if isinstance(y_new, torch.Tensor) else np.array(y_new)
            
            # 确保数据形状正确
            if X_new_np.ndim == 1:
                X_new_np = X_new_np.reshape(1, -1)
            if y_new_np.ndim > 1:
                y_new_np = y_new_np.flatten()
            
            # 🎯 关键修复：检测是否为完整数据集更新，避免重复累积
            if self.X_train is not None and self.y_train is not None:
                # 反标准化旧数据以进行比较
                X_old_original = self.X_scaler.inverse_transform(self.X_train)
                y_old_original = self.y_scaler.inverse_transform(self.y_train.reshape(-1, 1)).flatten()
                
                current_samples = len(X_old_original)
                new_samples = len(X_new_np)
                
                # 📊 检测数据更新类型
                if new_samples >= current_samples:
                    # 传入的可能是完整数据集（包含历史数据）
                    logging.debug(f"RBF检测到完整数据集更新: 当前{current_samples}样本 -> 输入{new_samples}样本")
                    
                    # 检查前N个样本是否与历史数据相同（允许小误差）
                    if new_samples >= current_samples:
                        # 取前current_samples个样本进行比较
                        X_prefix = X_new_np[:current_samples]
                        y_prefix = y_new_np[:current_samples]
                        
                        # 计算差异
                        X_diff = np.linalg.norm(X_prefix - X_old_original, axis=1).mean()
                        y_diff = np.linalg.norm(y_prefix - y_old_original)
                        
                        if X_diff < 1e-6 and y_diff < 1e-6:
                            # 确认是完整数据集更新，直接用新数据重新训练
                            # logging.info(f"RBF确认完整数据集更新，直接重新训练，样本数: {new_samples}")
                            X_new_tensor = self.device_manager.safe_to_device(X_new_np)
                            y_new_tensor = self.device_manager.safe_to_device(y_new_np)
                            self.fit(X_new_tensor, y_new_tensor)
                            return
                
                # 否则作为新增数据处理（原有逻辑）
                logging.debug(f"RBF作为增量数据处理: {current_samples} + {new_samples} = {current_samples + new_samples}")
                X_combined = np.vstack([X_old_original, X_new_np])
                y_combined = np.concatenate([y_old_original, y_new_np])
            else:
                # 首次训练
                X_combined = X_new_np
                y_combined = y_new_np
            
            # 重新训练模型
            X_combined_tensor = self.device_manager.safe_to_device(X_combined)
            y_combined_tensor = self.device_manager.safe_to_device(y_combined)
            self.fit(X_combined_tensor, y_combined_tensor)
            
            # logging.info(f"RBF模型更新完成，总样本数: {len(X_combined)}")
            
        except Exception as e:
            logging.error(f"RBF模型更新失败: {e}")
            raise e
    
    def get_feature_importance(self) -> np.ndarray:
        """
        获取特征重要性（基于权重的绝对值）
        
        Returns:
            特征重要性数组
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        
        try:
            # 计算每个特征的平均权重绝对值作为重要性
            if self.X_train is not None and self.weights is not None:
                # 简单的特征重要性：基于训练数据的方差
                feature_importance = np.var(self.X_train, axis=0)
                feature_importance = feature_importance / feature_importance.sum()  # 归一化
                return feature_importance
            else:
                return np.ones(self.axus.target_dim) / self.axus.target_dim
        except Exception as e:
            logging.warning(f"无法获取RBF特征重要性: {e}")
            return np.ones(self.axus.target_dim) / self.axus.target_dim
