import pytest
import torch
import numpy as np
import logging
from unittest.mock import Mock, patch

from bounce.genetic_algorithm import MixedVariableGA, GAConfig, Individual
from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
from bounce.ga_tabpfn_integration import GATabPFNIntegration
from bounce.projection import AxUS
from bounce.util.benchmark import Parameter, ParameterType
from bounce.benchmarks import MaxSat60


# 设置日志
logging.basicConfig(level=logging.INFO)


class MockBenchmark:
    """模拟基准函数用于测试"""
    
    def __init__(self, dim=10):
        self.dim = dim
        self.representation_dim = dim
        self.parameters = [
            Parameter(
                name=f"x{i}",
                type=ParameterType.BINARY if i < dim//2 else ParameterType.CONTINUOUS,
                lower_bound=0.0,
                upper_bound=1.0,
            )
            for i in range(dim)
        ]
        self.lb_vec = torch.zeros(dim, dtype=torch.float64)
        self.ub_vec = torch.ones(dim, dtype=torch.float64)
        self.is_discrete = True
        self.is_continuous = False
        self.is_mixed = True
        
    def __call__(self, x):
        """简单的测试函数：最小化所有变量的平方和"""
        if x.dim() == 1:
            x = x.unsqueeze(0)
        return torch.sum(x**2, dim=1)


def test_individual_creation():
    """测试个体创建和基因转换"""
    # 创建简单的AxUS投影
    benchmark = MockBenchmark(dim=6)
    axus = AxUS(parameters=benchmark.parameters, n_bins=6)
    
    # 创建个体
    genes = torch.rand(6, dtype=torch.float64) * 2 - 1  # [-1, 1]范围
    individual = Individual(genes, axus)
    
    # 测试高维转换
    high_dim = individual.to_high_dim(benchmark.lb_vec, benchmark.ub_vec)
    
    assert high_dim.shape[0] == benchmark.representation_dim
    assert torch.all(high_dim >= benchmark.lb_vec)
    assert torch.all(high_dim <= benchmark.ub_vec)
    
    # 测试拷贝
    individual_copy = individual.copy()
    assert torch.allclose(individual.genes, individual_copy.genes)
    assert individual.fitness == individual_copy.fitness


def test_ga_initialization():
    """测试遗传算法初始化"""
    benchmark = MockBenchmark(dim=8)
    axus = AxUS(parameters=benchmark.parameters, n_bins=8)
    
    config = GAConfig(population_size=10, max_generations=5)
    ga = MixedVariableGA(config, axus, benchmark)
    
    # 测试变量索引识别
    assert len(ga.binary_indices) == 4  # 前一半是二分变量
    assert len(ga.continuous_indices) == 4  # 后一半是连续变量
    
    # 测试种群初始化
    ga.initialize_population()
    assert len(ga.population) == config.population_size
    
    for individual in ga.population:
        assert individual.genes.shape[0] == axus.target_dim
        assert torch.all(individual.genes >= -1)
        assert torch.all(individual.genes <= 1)


def test_ga_evolution():
    """测试遗传算法进化过程"""
    benchmark = MockBenchmark(dim=6)
    axus = AxUS(parameters=benchmark.parameters, n_bins=6)
    
    config = GAConfig(population_size=8, max_generations=3)
    ga = MixedVariableGA(config, axus, benchmark)
    
    # 运行遗传算法
    best_individual = ga.run()
    
    assert best_individual is not None
    assert best_individual.fitness is not None
    assert ga.generation > 0
    assert len(ga.fitness_history) > 0


@patch('bounce.tabpfn_surrogate.TabPFNClassifier')
def test_tabpfn_surrogate(mock_classifier):
    """测试TabPFN代理模型"""
    # 模拟TabPFN分类器
    mock_instance = Mock()
    mock_instance.fit.return_value = None
    mock_instance.predict_proba.return_value = np.array([[0.8, 0.2], [0.3, 0.7]])
    mock_classifier.return_value = mock_instance
    
    benchmark = MockBenchmark(dim=4)
    surrogate = TabPFNGlobalSurrogate(benchmark, n_bins=2)
    
    # 测试训练
    X_train = torch.rand(10, 4, dtype=torch.float64)
    y_train = torch.rand(10, dtype=torch.float64)
    
    surrogate.fit(X_train, y_train)
    assert surrogate.is_fitted
    
    # 测试预测
    X_test = torch.rand(2, 4, dtype=torch.float64)
    quality_scores = surrogate.predict_quality(X_test)
    
    assert quality_scores.shape[0] == 2
    assert torch.all(quality_scores >= 0)


@patch('bounce.tabpfn_surrogate.TabPFNClassifier')
def test_ga_tabpfn_integration(mock_classifier):
    """测试GA-TabPFN集成"""
    # 模拟TabPFN分类器
    mock_instance = Mock()
    mock_instance.fit.return_value = None
    mock_instance.predict_proba.return_value = np.array([[0.9, 0.1], [0.2, 0.8]])
    mock_classifier.return_value = mock_instance
    
    benchmark = MockBenchmark(dim=6)
    axus = AxUS(parameters=benchmark.parameters, n_bins=6)
    
    config = GAConfig(population_size=8, max_generations=3)
    integration = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        ga_config=config,
        ga_generations=3
    )
    
    # 测试全局搜索
    best_low_dim, best_high_dim = integration.run_global_search()
    
    assert best_low_dim.shape[0] == axus.target_dim
    assert best_high_dim.shape[0] == benchmark.representation_dim
    assert torch.all(best_low_dim >= -1)
    assert torch.all(best_low_dim <= 1)
    
    # 测试TabPFN预测中心点
    existing_X = torch.rand(15, benchmark.representation_dim, dtype=torch.float64)
    existing_y = torch.rand(15, dtype=torch.float64)
    
    predicted_center = integration.predict_best_center_with_tabpfn(
        existing_X, existing_y, n_candidates=10
    )
    
    assert predicted_center.shape[0] == axus.target_dim
    assert torch.all(predicted_center >= -1)
    assert torch.all(predicted_center <= 1)


def test_mixed_variable_handling():
    """测试混合变量类型的处理"""
    # 创建包含不同类型变量的基准函数
    parameters = [
        Parameter(name="binary1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="binary2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="continuous1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="continuous2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="categorical1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=2),
    ]
    
    class MixedBenchmark:
        def __init__(self):
            self.parameters = parameters
            self.dim = len(parameters)
            self.representation_dim = 7  # 类别变量需要3维one-hot编码
            self.lb_vec = torch.tensor([0, 0, 0, 0, 0, 0, 0], dtype=torch.float64)
            self.ub_vec = torch.tensor([1, 1, 1, 1, 1, 1, 1], dtype=torch.float64)
            self.is_mixed = True
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = MixedBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    config = GAConfig(population_size=6, max_generations=2)
    ga = MixedVariableGA(config, axus, benchmark)
    
    # 测试变量索引识别
    assert len(ga.binary_indices) == 2
    assert len(ga.continuous_indices) == 2
    assert len(ga.categorical_indices) == 3  # one-hot编码
    
    # 测试种群初始化
    ga.initialize_population()
    
    # 验证类别变量的one-hot编码正确性
    for individual in ga.population:
        categorical_genes = individual.genes[ga.categorical_indices]
        # 应该有且仅有一个位置为1，其他为-1
        assert torch.sum(categorical_genes == 1) == 1
        assert torch.sum(categorical_genes == -1) == len(ga.categorical_indices) - 1


def test_error_handling():
    """测试错误处理"""
    benchmark = MockBenchmark(dim=4)
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 测试未训练的TabPFN模型
    surrogate = TabPFNGlobalSurrogate(benchmark)
    
    with pytest.raises(ValueError, match="模型尚未训练"):
        X_test = torch.rand(2, 4, dtype=torch.float64)
        surrogate.predict_quality(X_test)
    
    # 测试空种群的GA
    config = GAConfig(population_size=0, max_generations=1)
    ga = MixedVariableGA(config, axus, benchmark)
    
    with pytest.raises(Exception):  # 应该抛出某种异常
        ga.run()


if __name__ == "__main__":
    # 运行基本测试
    test_individual_creation()
    print("✅ Individual creation test passed")
    
    test_ga_initialization()
    print("✅ GA initialization test passed")
    
    test_ga_evolution()
    print("✅ GA evolution test passed")
    
    test_mixed_variable_handling()
    print("✅ Mixed variable handling test passed")
    
    print("🎉 所有基本测试通过！")
    
    # 注意：TabPFN相关测试需要mock，在实际环境中可能需要网络连接
