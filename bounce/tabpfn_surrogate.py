import logging
import numpy as np
import pandas as pd
import torch
import os
from typing import List, Tuple, Optional, Union
from tabpfn import TabPFNClassifier

from bounce.util.benchmark import Parameter, ParameterType
from bounce.device_manager import DeviceManager


class TabPFNGlobalSurrogate:
    """TabPFN全局代理模型，用于预测候选解的质量"""
    
    def __init__(self, benchmark, n_bins: int = 5, device: str = 'cpu', axus=None):
        """
        初始化TabPFN全局代理模型

        Args:
            benchmark: 基准函数对象
            n_bins: 将连续目标值离散化的箱数
            device: 计算设备 ('cpu' 或 'cuda')
            axus: AxUS投影对象，用于处理高维表示
            max_samples: 最大训练样本数，超过时会采样
        """
        self.benchmark = benchmark
        self.n_bins = n_bins
        self.device_manager = DeviceManager(device)
        self.axus = axus  # 🔧 添加AxUS引用

        # 设置环境变量以允许CPU处理大数据集
        if self.device_manager.device.type == 'cpu':
            os.environ["TABPFN_ALLOW_CPU_LARGE_DATASET"] = "1"

        # 初始化本地TabPFN分类器（使用设备管理器的设备）
        try:
            self.classifier = TabPFNClassifier(device=str(self.device_manager.device))
            logging.info(f"TabPFN初始化成功，设备: {self.device_manager.device}")
        except Exception as e:
            logging.warning(f"TabPFN CUDA初始化失败: {e}，使用CPU")
            self.device_manager._downgrade_to_cpu()
            self.classifier = TabPFNClassifier(device='cpu')
            
        self.is_fitted = False

        # 存储训练数据
        self.X_train = None
        self.y_train = None
        self.y_bins = None  # 离散化的边界

        # 🔧 新增：低维空间支持
        self.current_target_dim = None  # 当前适配的目标维度
        self.low_dim_classifier = None  # 低维空间的分类器
        self.low_dim_fitted = False  # 低维模型是否已训练
        self.low_dim_X_train = None  # 低维训练数据
        self.low_dim_y_train = None  # 低维训练标签
        
        # 变量类型信息
        self._analyze_variable_types()
        


    def _analyze_variable_types(self):
        """分析基准函数的变量类型"""
        self.variable_types = []
        self.variable_bounds = []
        
        for param in self.benchmark.parameters:
            self.variable_types.append(param.type)
            self.variable_bounds.append((param.lower_bound, param.upper_bound))
            
        logging.info(f"TabPFN代理模型变量类型分析完成: "
                    f"连续变量: {sum(1 for t in self.variable_types if t == ParameterType.CONTINUOUS)}, "
                    f"二分变量: {sum(1 for t in self.variable_types if t == ParameterType.BINARY)}, "
                    f"类别变量: {sum(1 for t in self.variable_types if t == ParameterType.CATEGORICAL)}")

    def _discretize_targets(self, y_values: np.ndarray) -> np.ndarray:
        """将连续的目标值离散化为分类标签"""
        if len(y_values) < self.n_bins:
            # 如果数据点太少，使用简单的排序分类
            sorted_indices = np.argsort(y_values)
            labels = np.zeros(len(y_values), dtype=int)
            
            # 将数据分为几个等大小的组
            group_size = len(y_values) // min(self.n_bins, len(y_values))
            if group_size == 0:
                group_size = 1
                
            for i, idx in enumerate(sorted_indices):
                labels[idx] = min(i // group_size, self.n_bins - 1)
                
            self.y_bins = None
            return labels
        else:
            # 使用分位数进行离散化
            self.y_bins = np.quantile(y_values, np.linspace(0, 1, self.n_bins + 1))
            self.y_bins[0] = y_values.min() - 1e-6  # 确保最小值被包含
            self.y_bins[-1] = y_values.max() + 1e-6  # 确保最大值被包含
            
            # 将连续值转换为离散标签
            labels = np.digitize(y_values, self.y_bins) - 1
            labels = np.clip(labels, 0, self.n_bins - 1)
            
            return labels

    def _prepare_features(self, X: torch.Tensor) -> pd.DataFrame:
        """准备特征数据，处理不同变量类型"""
        if X.dim() == 1:
            X = X.unsqueeze(0)

        X_np = X.detach().cpu().numpy()

        # 🔧 关键修复：正确处理高维空间中的one-hot编码
        # 检查是否需要从高维空间解码到原始参数空间
        if hasattr(self.benchmark, 'parameters') and hasattr(self, '_decode_from_high_dim'):
            # 如果输入是高维表示，需要解码到原始参数空间
            try:
                X_decoded = self._decode_from_high_dim(X)
                X_np = X_decoded.detach().cpu().numpy()
            except Exception as e:
                logging.warning(f"高维解码失败，尝试直接处理: {e}")
                # 如果解码失败，尝试直接处理
                pass

        # 创建特征DataFrame
        feature_dict = {}

        # 🔧 修复：根据实际输入维度和变量类型进行处理
        if X_np.shape[1] == len(self.variable_types):
            # 输入维度等于原始参数数量，直接按变量类型处理
            for i, (var_type, bounds) in enumerate(zip(self.variable_types, self.variable_bounds)):
                col_name = f"x{i}"

                if var_type == ParameterType.CONTINUOUS:
                    # 连续变量：假设已经在[0,1]范围内
                    feature_dict[col_name] = X_np[:, i]

                elif var_type == ParameterType.BINARY:
                    # 二分变量：转换为0/1
                    feature_dict[col_name] = (X_np[:, i] > 0).astype(int)

                elif var_type == ParameterType.CATEGORICAL:
                    # 类别变量：假设是整数索引
                    feature_dict[col_name] = X_np[:, i].astype(int)
        else:
            # 输入维度不等于原始参数数量，可能是高维表示
            # 尝试使用AxUS解码
            try:
                if hasattr(self.benchmark, 'axus') or hasattr(self, 'axus'):
                    axus = getattr(self.benchmark, 'axus', getattr(self, 'axus', None))
                    if axus is not None:
                        # 使用AxUS的bins_and_indices_of_type方法解码
                        feature_dict = self._decode_with_axus(X_np, axus)
                    else:
                        raise ValueError("无法找到AxUS对象进行解码")
                else:
                    # 没有AxUS，尝试简单的维度映射
                    logging.warning(f"维度不匹配且无AxUS，使用简单映射: 输入{X_np.shape[1]}维，参数{len(self.variable_types)}个")
                    # 简单截断或填充
                    if X_np.shape[1] >= len(self.variable_types):
                        for i, (var_type, bounds) in enumerate(zip(self.variable_types, self.variable_bounds)):
                            col_name = f"x{i}"
                            if var_type == ParameterType.CONTINUOUS:
                                feature_dict[col_name] = X_np[:, i]
                            elif var_type == ParameterType.BINARY:
                                feature_dict[col_name] = (X_np[:, i] > 0).astype(int)
                            elif var_type == ParameterType.CATEGORICAL:
                                feature_dict[col_name] = X_np[:, i].astype(int)
                    else:
                        # 输入维度不足，填充默认值
                        for i, (var_type, bounds) in enumerate(zip(self.variable_types, self.variable_bounds)):
                            col_name = f"x{i}"
                            if i < X_np.shape[1]:
                                if var_type == ParameterType.CONTINUOUS:
                                    feature_dict[col_name] = X_np[:, i]
                                elif var_type == ParameterType.BINARY:
                                    feature_dict[col_name] = (X_np[:, i] > 0).astype(int)
                                elif var_type == ParameterType.CATEGORICAL:
                                    feature_dict[col_name] = X_np[:, i].astype(int)
                            else:
                                # 使用默认值
                                if var_type == ParameterType.CATEGORICAL:
                                    feature_dict[col_name] = np.zeros(X_np.shape[0], dtype=int)
                                else:
                                    feature_dict[col_name] = np.zeros(X_np.shape[0])
            except Exception as e:
                logging.error(f"特征解码失败: {e}")
                # 最后的备选方案：使用前几个维度
                for i, (var_type, bounds) in enumerate(zip(self.variable_types, self.variable_bounds)):
                    col_name = f"x{i}"
                    if i < X_np.shape[1]:
                        feature_dict[col_name] = X_np[:, i]
                    else:
                        feature_dict[col_name] = np.zeros(X_np.shape[0])

        return pd.DataFrame(feature_dict)

    def _decode_with_axus(self, X_np: np.ndarray, axus) -> dict:
        """使用AxUS解码高维表示到原始参数空间"""
        feature_dict = {}

        try:
            # 转换回torch张量进行AxUS操作
            X_tensor = torch.tensor(X_np, dtype=torch.float64)

            # 🔧 关键修复：使用AxUS的bins_and_indices_of_type方法解码类别变量
            param_idx = 0

            for var_type, bounds in zip(self.variable_types, self.variable_bounds):
                col_name = f"x{param_idx}"

                if var_type == ParameterType.CATEGORICAL:
                    # 对于类别变量，需要从one-hot编码中找到激活的类别
                    # 获取该类别变量在高维空间中的索引范围
                    categorical_indices = None
                    for _, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                        if len(indices) > 0:
                            # 找到对应的类别变量索引
                            # 这里简化处理，假设类别变量按顺序排列
                            categorical_indices = indices
                            break

                    if categorical_indices is not None and len(categorical_indices) <= X_np.shape[1]:
                        # 从one-hot编码中提取类别索引
                        categorical_values = []
                        for sample_idx in range(X_np.shape[0]):
                            # 在类别变量的索引范围内找到值为1的位置
                            sample_categorical = X_np[sample_idx, categorical_indices]
                            # 找到最大值的位置（应该是1）
                            max_idx = np.argmax(sample_categorical)
                            categorical_values.append(max_idx)
                        feature_dict[col_name] = np.array(categorical_values, dtype=int)
                    else:
                        # 如果无法解码，使用默认值
                        feature_dict[col_name] = np.zeros(X_np.shape[0], dtype=int)

                elif var_type == ParameterType.BINARY:
                    # 二分变量：取对应位置的值并转换为0/1
                    if param_idx < X_np.shape[1]:
                        feature_dict[col_name] = (X_np[:, param_idx] > 0).astype(int)
                    else:
                        feature_dict[col_name] = np.zeros(X_np.shape[0], dtype=int)

                elif var_type == ParameterType.CONTINUOUS:
                    # 连续变量：直接使用对应位置的值
                    if param_idx < X_np.shape[1]:
                        feature_dict[col_name] = X_np[:, param_idx]
                    else:
                        feature_dict[col_name] = np.zeros(X_np.shape[0])

                param_idx += 1

        except Exception as e:
            logging.error(f"AxUS解码失败: {e}")
            # 备选方案：简单映射
            for i, (var_type, bounds) in enumerate(zip(self.variable_types, self.variable_bounds)):
                col_name = f"x{i}"
                if i < X_np.shape[1]:
                    if var_type == ParameterType.CATEGORICAL:
                        feature_dict[col_name] = X_np[:, i].astype(int)
                    elif var_type == ParameterType.BINARY:
                        feature_dict[col_name] = (X_np[:, i] > 0).astype(int)
                    else:
                        feature_dict[col_name] = X_np[:, i]
                else:
                    if var_type == ParameterType.CATEGORICAL:
                        feature_dict[col_name] = np.zeros(X_np.shape[0], dtype=int)
                    else:
                        feature_dict[col_name] = np.zeros(X_np.shape[0])

        return feature_dict

    def fit(self, X: torch.Tensor, y: torch.Tensor) -> None:
        """
        训练TabPFN模型
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            y: 目标值，形状为 (n_samples,)
        """
        if X.dim() == 1:
            X = X.unsqueeze(0)
        if y.dim() == 1:
            y = y.unsqueeze(0) if y.numel() == 1 else y
        
        # 转换为numpy
        X_np = X.detach().cpu().numpy()
        y_np = y.detach().cpu().numpy().flatten()
        
        # 使用设备管理器保存原始函数值
        self._original_y_values = self.device_manager.safe_to_device(y_np)
        
        # 准备特征
        self.X_train = self._prepare_features(X)
        
        # 离散化目标值
        y_discrete = self._discretize_targets(y_np)
        self.y_train = pd.Series(y_discrete)
        
        # 训练TabPFN模型
        try:
            self.classifier.fit(self.X_train, self.y_train)
            self.is_fitted = True
            logging.info(f"TabPFN模型训练完成，训练样本数: {len(self.X_train)}, "
                        f"类别数: {len(np.unique(y_discrete))}")
            
            # 检查是否超过警告阈值
            if len(self.X_train) > 200:
                logging.warning(f"📊 TabPFN训练样本数较多 ({len(self.X_train)})，可能影响性能")
        except Exception as e:
            logging.error(f"TabPFN模型训练失败: {e}")
            self.is_fitted = False
            raise

    def predict(self, X: torch.Tensor) -> torch.Tensor:
        """
        与GP、RBF模型接口兼容的预测方法
        
        Args:
            X: 输入特征，形状为 (n_samples, n_features)
            
        Returns:
            预测值，形状为 (n_samples,)，转换为与真实函数值相似的尺度
        """
        # 获取质量分数
        quality_scores = self.predict_quality(X)
        
        # 🔧 关键修复：将质量分数转换为与真实函数值相似的预测值
        # 使用历史数据的统计信息进行转换
        if hasattr(self, 'y_train') and self.y_train is not None and len(self.y_train) > 0:
            # 获取训练时的真实函数值统计
            if hasattr(self, '_original_y_values'):
                y_mean = self._original_y_values.mean()
                y_std = self._original_y_values.std()
            else:
                # 如果没有保存原始值，使用默认的函数值范围
                y_mean = -100.0  # MaxSat问题的典型函数值
                y_std = 50.0
        else:
            # 默认函数值范围
            y_mean = -100.0
            y_std = 50.0
        
        # 将质量分数（0-n_bins，越小越好）转换为函数值（越小越好）
        # 质量分数0对应最好的函数值，质量分数n_bins对应最差的函数值
        normalized_scores = quality_scores / self.n_bins  # 归一化到[0,1]
        
        # 转换为函数值：好的质量分数(0)对应好的函数值(y_mean - y_std)
        # 差的质量分数(n_bins)对应差的函数值(y_mean + y_std)
        predicted_values = y_mean - y_std + normalized_scores * (2 * y_std)
        
        # 使用设备管理器返回预测值
        predicted_values = self.device_manager.safe_to_device(predicted_values)
        
        logging.debug(f"TabPFN质量分数转换: 质量分数范围[{quality_scores.min():.4f}, {quality_scores.max():.4f}] → 预测值范围[{predicted_values.min():.4f}, {predicted_values.max():.4f}]")
        
        return predicted_values

    def predict_quality(self, X: torch.Tensor) -> torch.Tensor:
        """
        预测候选解的质量（概率分布）
        
        Args:
            X: 候选解，形状为 (n_candidates, n_features)
            
        Returns:
            质量分数，形状为 (n_candidates,)，值越小表示质量越好
        """
        if not self.is_fitted:
            logging.warning("TabPFN模型未训练，返回随机质量分数")
            return self.device_manager.safe_to_device(torch.rand(X.shape[0]))
            
        # 确保输入在正确设备上
        X = self.device_manager.ensure_tensor_device(X)
        
        if X.dim() == 1:
            X = X.unsqueeze(0)

        # 添加输入验证
        if X.shape[0] == 0:
            return self.device_manager.safe_to_device(torch.tensor([]))

        # 准备特征
        X_test = self._prepare_features(X)

        # 验证特征数据
        if X_test.empty or X_test.isnull().any().any():
            logging.warning("特征数据包含空值或无效值，返回随机分数")
            return self.device_manager.safe_to_device(torch.rand(X.shape[0]))
        
        try:
            # 获取预测概率
            probabilities = self.classifier.predict_proba(X_test)
            
            # 计算期望质量分数（越小越好）
            # 使用概率加权的类别索引作为质量分数
            quality_scores = []
            for prob_dist in probabilities:
                # 期望类别索引（0是最好的，n_bins-1是最差的）
                expected_class = np.sum(prob_dist * np.arange(len(prob_dist)))
                quality_scores.append(expected_class)
                
            return self.device_manager.safe_to_device(torch.tensor(quality_scores))
            
        except Exception as e:
            logging.error(f"TabPFN预测失败: {e}")
            # 返回随机分数作为备选
            return self.device_manager.safe_to_device(torch.rand(X.shape[0]) * self.n_bins)

    def predict_best_candidate(self, candidates: torch.Tensor) -> Tuple[torch.Tensor, float]:
        """
        从候选解中预测最佳的一个
        
        Args:
            candidates: 候选解集合，形状为 (n_candidates, n_features)
            
        Returns:
            最佳候选解和其质量分数
        """
        quality_scores = self.predict_quality(candidates)
        best_idx = torch.argmin(quality_scores)
        
        return candidates[best_idx], quality_scores[best_idx].item()

    def _sample_training_data(self, X: torch.Tensor, y: torch.Tensor, max_samples: int = 1000) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对训练数据进行采样以限制训练样本数量
        
        Args:
            X: 输入特征
            y: 目标值
            max_samples: 最大样本数
            
        Returns:
            采样后的X和y
        """
        if len(X) <= max_samples:
            return X, y
        
        # 使用分层采样保持数据分布
        try:
            # 简单随机采样
            indices = torch.randperm(len(X))[:max_samples]
            X_sampled = X[indices]
            y_sampled = y[indices]
            
            logging.info(f"TabPFN训练数据采样: {len(X)} → {len(X_sampled)}")
            return X_sampled, y_sampled
            
        except Exception as e:
            logging.warning(f"数据采样失败，使用前{max_samples}个样本: {e}")
            return X[:max_samples], y[:max_samples]
    
    def update_model(self, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        使用新数据更新模型

        Args:
            X_new: 新的输入特征
            y_new: 新的目标值
        """
        if not self.is_fitted:
            # 如果模型未训练，直接训练
            self.fit(X_new, y_new)
        else:
            # 检查新数据是否只是重复的完整数据集
            if len(X_new) == len(self.X_train) + len(y_new):
                logging.debug("检测到完整数据集更新，跳过重复训练")
                return
                
            # 💫 每次迭代都更新TabPFN模型（昂贵优化问题的关键）
            if hasattr(self, '_last_update_size'):
                if len(X_new) > self._last_update_size:  # 有新数据就更新
                    new_samples = len(X_new) - self._last_update_size
                    logging.debug(f"TabPFN新增{new_samples}个珍贵样本，立即更新")
                elif len(X_new) == self._last_update_size:
                    logging.debug("TabPFN数据量未变化，跳过更新")
                    return
            
            self._last_update_size = len(X_new)
            
            # 🎯 使用采样策略重新训练
            X_sampled, y_sampled = self._sample_training_data(X_new, y_new)
            
            # 🔧 关键修复：保存原始函数值
            self._original_y_values = y_sampled.clone()
            
            # 重新准备特征
            X_new_df = self._prepare_features(X_sampled)
            y_new_np = y_sampled.detach().cpu().numpy().flatten()
            
            # 重新离散化所有目标值
            y_new_discrete = self._discretize_targets(y_new_np)
            
            # 更新训练数据
            self.X_train = X_new_df
            self.y_train = pd.Series(y_new_discrete)
            
            try:
                self.classifier.fit(self.X_train, self.y_train)
                logging.info(f"TabPFN模型更新完成，总样本数: {len(self.X_train)}")
                
                # 检查是否超过警告阈值
                if len(self.X_train) > 200:
                    logging.warning(f"📊 TabPFN训练样本数较多 ({len(self.X_train)})，可能影响性能")
            except Exception as e:
                logging.error(f"TabPFN模型更新失败: {e}")

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "is_fitted": self.is_fitted,
            "n_bins": self.n_bins,
            "n_train_samples": len(self.X_train) if self.X_train is not None else 0,
            "n_features": len(self.variable_types),
            "variable_types": [t.value for t in self.variable_types],
            "low_dim_fitted": getattr(self, 'low_dim_fitted', False),
            "current_target_dim": getattr(self, 'current_target_dim', None)
        }

    def predict_quality_low_dim(self, X: torch.Tensor) -> torch.Tensor:
        """
        在低维空间预测候选点的质量分数

        Args:
            X: 低维候选点张量 [n_candidates, target_dim]

        Returns:
            质量分数张量 [n_candidates] (越小越好)
        """
        if not getattr(self, 'low_dim_fitted', False):
            logging.warning("低维TabPFN模型未训练，返回随机质量分数")
            return self.device_manager.safe_to_device(torch.rand(X.shape[0]))

        # 添加输入验证
        if X.shape[0] == 0:
            return self.device_manager.safe_to_device(torch.tensor([]))

        if X.shape[0] > 1000:  # TabPFN有样本数量限制
            logging.warning(f"候选点数量过多({X.shape[0]})，截断到1000个")
            X = X[:1000]

        try:
            # 准备低维特征（简化处理，直接使用原始值）
            X_np = X.detach().cpu().numpy()

            # 创建特征DataFrame
            feature_dict = {f"x{i}": X_np[:, i] for i in range(X.shape[1])}
            X_test = pd.DataFrame(feature_dict)

            # 验证特征数据
            if X_test.empty or X_test.isnull().any().any():
                logging.warning("低维特征数据包含空值或无效值，返回随机分数")
                return self.device_manager.safe_to_device(torch.rand(X.shape[0]))

            # 获取预测概率
            probabilities = self.low_dim_classifier.predict_proba(X_test)

            # 计算期望质量分数（越小越好）
            quality_scores = []
            for prob_dist in probabilities:
                # 期望值：sum(class_index * probability)
                expected_class = sum(i * prob for i, prob in enumerate(prob_dist))
                quality_scores.append(expected_class)

            return self.device_manager.safe_to_device(torch.tensor(quality_scores))

        except Exception as e:
            logging.error(f"低维TabPFN预测失败: {e}")
            logging.debug(f"输入形状: {X.shape}")
            # 返回随机分数作为备选
            return self.device_manager.safe_to_device(torch.rand(X.shape[0]))

    def fit_low_dim(self, X_low: torch.Tensor, y: torch.Tensor, target_dim: int):
        """
        在低维空间训练TabPFN模型

        Args:
            X_low: 低维训练数据 [n_samples, target_dim]
            y: 目标值 [n_samples]
            target_dim: 目标维度
        """
        try:
            # 更新当前目标维度
            self.current_target_dim = target_dim

            # 准备训练数据
            X_np = X_low.detach().cpu().numpy()
            y_np = y.detach().cpu().numpy()

            # 离散化目标值
            y_discrete = self._discretize_targets(y_np)

            # 创建特征DataFrame
            feature_dict = {f"x{i}": X_np[:, i] for i in range(X_low.shape[1])}
            X_train_df = pd.DataFrame(feature_dict)

            # 初始化低维分类器（使用设备管理器的设备）
            self.low_dim_classifier = TabPFNClassifier(device=str(self.device_manager.device))

            # 训练模型
            self.low_dim_classifier.fit(X_train_df, y_discrete)

            # 保存训练数据（使用设备管理器）
            self.low_dim_X_train = self.device_manager.ensure_tensor_device(X_low)
            self.low_dim_y_train = self.device_manager.ensure_tensor_device(y)
            self.low_dim_fitted = True

            logging.info(f"低维TabPFN模型训练完成，维度: {target_dim}, 样本数: {len(X_low)}")

        except Exception as e:
            logging.error(f"低维TabPFN训练失败: {e}")
            self.low_dim_fitted = False

    def update(self, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        使用新数据更新模型（与XGBoost接口兼容的别名）

        Args:
            X_new: 新的输入特征
            y_new: 新的目标值
        """
        # 确保输入在正确设备上
        X_new = self.device_manager.ensure_tensor_device(X_new)
        y_new = self.device_manager.ensure_tensor_device(y_new)
        
        self.update_model(X_new, y_new)
