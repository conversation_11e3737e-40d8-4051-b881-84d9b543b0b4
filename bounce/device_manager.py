"""
统一设备管理器 - 解决多代理模型集成中的CUDA设备不一致问题

作者: yjf
日期: 2025-09-03

该模块提供统一的设备管理功能，确保所有张量运算在同一设备上进行，
解决在集成多个代理模型（GP、RBF、TabPFN）时出现的设备不匹配错误。
"""

import logging
import torch
import numpy as np
from contextlib import contextmanager
from typing import Union, Optional, Any


class DeviceManager:
    """统一设备管理器
    
    提供设备验证、张量转换、安全降级等功能，确保多代理模型集成时的设备一致性。
    
    主要功能：
    1. 设备验证和自动降级
    2. 张量设备转换和类型统一
    3. 安全的设备上下文管理
    4. 错误处理和备选策略
    
    Examples:
        >>> device_manager = DeviceManager('cuda:0')
        >>> tensor = device_manager.ensure_tensor_device(input_tensor)
        >>> with device_manager.device_context():
        ...     # 所有操作都在指定设备上进行
        ...     result = model(tensor)
    """
    
    def __init__(self, device: Union[str, torch.device]):
        """初始化设备管理器
        
        Args:
            device: 目标设备，如 'cpu', 'cuda', 'cuda:0' 等
        """
        self.device = self._validate_device(device)
        self.is_cuda = self.device.type == 'cuda'
        self.dtype = torch.float64  # 统一数据类型
        
        # logging.info(f"设备管理器初始化: {self.device}, CUDA可用: {self.is_cuda}")
    
    def _validate_device(self, device: Union[str, torch.device]) -> torch.device:
        """验证并返回有效设备
        
        Args:
            device: 设备规格
            
        Returns:
            验证后的torch.device对象
        """
        try:
            torch_device = torch.device(device)
            
            # 检查CUDA可用性
            if torch_device.type == 'cuda' and not torch.cuda.is_available():
                logging.warning("CUDA不可用，自动回退到CPU")
                return torch.device('cpu')
            
            # 检查CUDA设备索引
            if torch_device.type == 'cuda' and torch_device.index is not None:
                if torch_device.index >= torch.cuda.device_count():
                    logging.warning(f"CUDA设备索引{torch_device.index}不存在，使用默认CUDA设备")
                    return torch.device('cuda:0')
            
            return torch_device
            
        except Exception as e:
            logging.warning(f"无效设备'{device}': {e}，使用CPU")
            return torch.device('cpu')
    
    def ensure_tensor_device(self, tensor: torch.Tensor) -> torch.Tensor:
        """确保张量在正确设备上
        
        Args:
            tensor: 输入张量
            
        Returns:
            在目标设备上的张量
            
        Raises:
            TypeError: 如果输入不是torch.Tensor
        """
        if not isinstance(tensor, torch.Tensor):
            raise TypeError(f"输入必须是torch.Tensor，得到: {type(tensor)}")
        
        try:
            # 检查设备是否匹配
            if tensor.device != self.device:
                # 转换到目标设备和统一数据类型
                tensor = tensor.to(self.device, dtype=self.dtype)
                logging.debug(f"张量转换到设备: {self.device}")
            
            # 确保数据类型一致
            elif tensor.dtype != self.dtype:
                tensor = tensor.to(dtype=self.dtype)
                logging.debug(f"张量类型转换为: {self.dtype}")
            
            return tensor
            
        except RuntimeError as e:
            logging.warning(f"设备转换失败: {e}，回退到CPU")
            # 降级处理：转换到CPU
            cpu_tensor = tensor.cpu().to(dtype=self.dtype)
            self._downgrade_to_cpu()
            return cpu_tensor
    
    def safe_to_device(self, data: Union[torch.Tensor, np.ndarray, list, float, int], 
                       dtype: Optional[torch.dtype] = None) -> torch.Tensor:
        """安全创建指定设备上的张量
        
        Args:
            data: 输入数据（张量、NumPy数组、列表或标量）
            dtype: 目标数据类型，默认使用self.dtype
            
        Returns:
            在目标设备上的张量
        """
        if dtype is None:
            dtype = self.dtype
        
        try:
            if isinstance(data, torch.Tensor):
                return data.to(self.device, dtype=dtype)
            else:
                # 转换其他类型数据为张量
                return torch.tensor(data, device=self.device, dtype=dtype)
                
        except (RuntimeError, TypeError, ValueError) as e:
            logging.warning(f"设备张量创建失败: {e}，使用CPU")
            # 降级处理
            if isinstance(data, torch.Tensor):
                cpu_tensor = data.cpu().to(dtype=dtype)
            else:
                cpu_tensor = torch.tensor(data, device='cpu', dtype=dtype)
            
            self._downgrade_to_cpu()
            return cpu_tensor
    
    def _downgrade_to_cpu(self) -> None:
        """降级到CPU设备"""
        if self.is_cuda:
            logging.info("设备管理器降级到CPU模式")
            self.device = torch.device('cpu')
            self.is_cuda = False
    
    def numpy_to_tensor(self, np_array: np.ndarray, 
                       dtype: Optional[torch.dtype] = None) -> torch.Tensor:
        """NumPy数组转换为张量
        
        Args:
            np_array: NumPy数组
            dtype: 目标数据类型
            
        Returns:
            在目标设备上的张量
        """
        if dtype is None:
            dtype = self.dtype
        
        try:
            # 直接从NumPy创建张量并移到目标设备
            tensor = torch.from_numpy(np_array).to(self.device, dtype=dtype)
            return tensor
            
        except (RuntimeError, TypeError) as e:
            logging.warning(f"NumPy转张量失败: {e}，使用CPU")
            # 备选方案：先创建CPU张量
            cpu_tensor = torch.from_numpy(np_array).to(dtype=dtype)
            self._downgrade_to_cpu()
            return cpu_tensor
    
    def tensor_to_numpy(self, tensor: torch.Tensor) -> np.ndarray:
        """张量转换为NumPy数组
        
        Args:
            tensor: 输入张量
            
        Returns:
            NumPy数组
        """
        try:
            # 如果在CUDA上，先移到CPU
            if tensor.is_cuda:
                return tensor.detach().cpu().numpy()
            else:
                return tensor.detach().numpy()
                
        except Exception as e:
            logging.error(f"张量转NumPy失败: {e}")
            raise
    
    @contextmanager
    def device_context(self):
        """设备上下文管理器
        
        确保在上下文内的操作都在指定设备上进行
        
        Examples:
            >>> with device_manager.device_context():
            ...     # 所有操作在目标设备上
            ...     result = model(input_tensor)
        """
        old_default_device = None
        
        try:
            if self.is_cuda:
                # 保存当前CUDA设备
                old_default_device = torch.cuda.current_device()
                # 设置目标CUDA设备
                if self.device.index is not None:
                    torch.cuda.set_device(self.device.index)
                else:
                    torch.cuda.set_device(0)
            
            yield self
            
        except Exception as e:
            logging.warning(f"设备上下文错误: {e}")
            # 降级到CPU模式
            self._downgrade_to_cpu()
            yield self
            
        finally:
            # 恢复原设备设置
            if old_default_device is not None:
                try:
                    torch.cuda.set_device(old_default_device)
                except:
                    pass  # 忽略恢复失败
    
    def get_device_info(self) -> dict:
        """获取设备信息
        
        Returns:
            设备信息字典
        """
        info = {
            'device': str(self.device),
            'is_cuda': self.is_cuda,
            'dtype': str(self.dtype),
            'cuda_available': torch.cuda.is_available(),
        }
        
        if torch.cuda.is_available():
            info.update({
                'cuda_device_count': torch.cuda.device_count(),
                'current_cuda_device': torch.cuda.current_device(),
            })
            
            if self.is_cuda and self.device.index is not None:
                try:
                    info['device_name'] = torch.cuda.get_device_name(self.device.index)
                    info['memory_allocated'] = torch.cuda.memory_allocated(self.device.index)
                    info['memory_reserved'] = torch.cuda.memory_reserved(self.device.index)
                except:
                    pass
        
        return info
    
    def clear_cache(self) -> None:
        """清理CUDA缓存"""
        if self.is_cuda and torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
                logging.debug("CUDA缓存已清理")
            except Exception as e:
                logging.warning(f"清理CUDA缓存失败: {e}")
    
    def __str__(self) -> str:
        return f"DeviceManager(device={self.device}, dtype={self.dtype})"
    
    def __repr__(self) -> str:
        return self.__str__()


def create_device_manager(device: Union[str, torch.device]) -> DeviceManager:
    """创建设备管理器的便捷函数
    
    Args:
        device: 目标设备
        
    Returns:
        DeviceManager实例
    """
    return DeviceManager(device)


# 设备管理器单例模式（可选）
_global_device_manager: Optional[DeviceManager] = None


def get_global_device_manager() -> Optional[DeviceManager]:
    """获取全局设备管理器"""
    return _global_device_manager


def set_global_device_manager(device: Union[str, torch.device]) -> DeviceManager:
    """设置全局设备管理器
    
    Args:
        device: 目标设备
        
    Returns:
        DeviceManager实例
    """
    global _global_device_manager
    _global_device_manager = DeviceManager(device)
    return _global_device_manager