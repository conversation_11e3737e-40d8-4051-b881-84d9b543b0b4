import logging
from typing import Optional, Union
import torch

from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
from bounce.gp_surrogate import GPGlobalSurrogate
from bounce.rbf_surrogate import RBFGlobalSurrogate


class SurrogateManager:
    """统一的代理模型管理器，支持全局和局部模型的统一接口"""
    
    SUPPORTED_MODELS = ['tabpfn', 'gp', 'rbf']
    
    def __init__(self, benchmark, axus, device: str = 'cpu'):
        """
        初始化代理模型管理器
        
        Args:
            benchmark: 基准函数
            axus: AxUS投影对象
            device: 计算设备
        """
        self.benchmark = benchmark
        self.axus = axus
        self.device = device
        
        # 存储不同类型的模型实例
        self._models = {}
        
    def create_model(self, model_type: str, **kwargs) -> Union[TabPFNGlobalSurrogate, GPGlobalSurrogate, RBFGlobalSurrogate]:
        """
        创建指定类型的代理模型
        
        Args:
            model_type: 模型类型 ('tabpfn', 'gp', 'rbf')
            **kwargs: 模型特定的参数
            
        Returns:
            代理模型实例
        """
        model_type = model_type.lower()
        
        if model_type not in self.SUPPORTED_MODELS:
            raise ValueError(f"不支持的模型类型: {model_type}，支持的类型: {self.SUPPORTED_MODELS}")
        
        # 如果模型已存在，直接返回
        if model_type in self._models:
            return self._models[model_type]
        
        # 创建新模型
        if model_type == 'tabpfn':
            n_bins = kwargs.get('n_bins', 5)
            model = TabPFNGlobalSurrogate(self.benchmark, n_bins=n_bins, device=self.device, axus=self.axus)
            # logging.info(f"创建TabPFN代理模型，n_bins: {n_bins}")
            
        elif model_type == 'gp':
            gp_params = kwargs.get('gp_params', {})
            model = GPGlobalSurrogate(self.benchmark, self.axus, device=self.device, **gp_params)
            # logging.info(f"创建GP代理模型，参数: {gp_params}")
            
        elif model_type == 'rbf':
            kernel = kwargs.get('kernel', 'gaussian')
            epsilon = kwargs.get('epsilon', 1.0)
            model = RBFGlobalSurrogate(self.benchmark, self.axus, device=self.device, kernel=kernel, epsilon=epsilon)
            # logging.info(f"创建RBF代理模型，核函数: {kernel}, epsilon: {epsilon}")
        
        # 缓存模型
        self._models[model_type] = model
        return model
    
    def get_model(self, model_type: str) -> Optional[Union[TabPFNGlobalSurrogate, GPGlobalSurrogate, RBFGlobalSurrogate]]:
        """
        获取已创建的模型实例
        
        Args:
            model_type: 模型类型
            
        Returns:
            模型实例或None
        """
        return self._models.get(model_type.lower())
    
    def fit_model(self, model_type: str, X: torch.Tensor, y: torch.Tensor, **kwargs) -> None:
        """
        训练指定类型的模型
        
        Args:
            model_type: 模型类型
            X: 输入特征
            y: 目标值
            **kwargs: 模型特定的参数
        """
        model = self.create_model(model_type, **kwargs)
        model.fit(X, y)
        # logging.info(f"{model_type.upper()}模型训练完成")
    
    def predict_with_model(self, model_type: str, X: torch.Tensor) -> torch.Tensor:
        """
        使用指定模型进行预测
        
        Args:
            model_type: 模型类型
            X: 输入特征
            
        Returns:
            预测结果
        """
        model = self.get_model(model_type)
        if model is None:
            raise ValueError(f"模型 {model_type} 尚未创建")
        
        if not model.is_fitted:
            raise ValueError(f"模型 {model_type} 尚未训练")
        
        return model.predict(X)
    
    def predict_quality_with_model(self, model_type: str, X: torch.Tensor) -> torch.Tensor:
        """
        使用指定模型预测质量分数
        
        Args:
            model_type: 模型类型
            X: 输入特征
            
        Returns:
            质量分数
        """
        model = self.get_model(model_type)
        if model is None:
            raise ValueError(f"模型 {model_type} 尚未创建")
        
        if not model.is_fitted:
            raise ValueError(f"模型 {model_type} 尚未训练")
        
        return model.predict_quality(X)
    
    def update_model(self, model_type: str, X_new: torch.Tensor, y_new: torch.Tensor) -> None:
        """
        更新指定模型
        
        Args:
            model_type: 模型类型
            X_new: 新的输入特征
            y_new: 新的目标值
        """
        model = self.get_model(model_type)
        if model is None:
            raise ValueError(f"模型 {model_type} 尚未创建")
        
        model.update(X_new, y_new)
        # logging.info(f"{model_type.upper()}模型更新完成")
    
    def is_model_fitted(self, model_type: str) -> bool:
        """
        检查模型是否已训练
        
        Args:
            model_type: 模型类型
            
        Returns:
            是否已训练
        """
        model = self.get_model(model_type)
        return model is not None and model.is_fitted
    
    def get_available_models(self) -> list:
        """
        获取所有可用的模型类型
        
        Returns:
            模型类型列表
        """
        return self.SUPPORTED_MODELS.copy()
    
    def get_created_models(self) -> list:
        """
        获取已创建的模型类型
        
        Returns:
            已创建的模型类型列表
        """
        return list(self._models.keys())
    
    def clear_models(self) -> None:
        """清除所有模型"""
        self._models.clear()
        logging.info("所有代理模型已清除")
    
    def get_model_info(self, model_type: str) -> dict:
        """
        获取模型信息
        
        Args:
            model_type: 模型类型
            
        Returns:
            模型信息字典
        """
        model = self.get_model(model_type)
        if model is None:
            return {"exists": False, "fitted": False}
        
        info = {
            "exists": True,
            "fitted": model.is_fitted,
            "type": model_type,
            "device": self.device
        }
        
        # 添加模型特定信息
        if model_type == 'tabpfn' and hasattr(model, 'n_bins'):
            info["n_bins"] = model.n_bins
        elif model_type == 'rbf' and hasattr(model, 'kernel'):
            info["kernel"] = model.kernel
            info["epsilon"] = model.epsilon
        
        return info