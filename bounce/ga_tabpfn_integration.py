import logging
import torch
import numpy as np
import gin
from typing import Tuple, Optional

from bounce.genetic_algorithm import MixedVariableGA, GAConfig, Individual
from bounce.surrogate_manager import SurrogateManager
from bounce.ensemble_manager import EnsembleManager
from bounce.projection import AxUS
from bounce.util.benchmark import ParameterType


@gin.configurable
class GATabPFNIntegration:
    """遗传算法与代理模型的集成类（支持多种代理模型）"""

    def __init__(self,
                 axus: AxUS,
                 benchmark,
                 ga_config: Optional[GAConfig] = None,
                 tabpfn_n_bins: int = 5,
                 ga_generations: int = 20,
                 device: str = 'cpu',
                 global_model_type: str = 'tabpfn',
                 enable_ensemble: bool = True):
        """
        初始化GA-代理模型集成系统

        Args:
            axus: AxUS投影对象
            benchmark: 基准函数
            ga_config: 遗传算法配置
            tabpfn_n_bins: TabPFN离散化箱数
            ga_generations: 遗传算法运行代数
            device: 计算设备 ('cpu' 或 'cuda')
            global_model_type: 全局模型类型 ('tabpfn', 'xgboost', 'gp', 'rbf')
            enable_ensemble: 是否启用集成代理模型模式
        """
        self.axus = axus
        self.benchmark = benchmark
        self.ga_generations = ga_generations
        self.device = device
        self.global_model_type = global_model_type
        self.enable_ensemble = enable_ensemble

        # 🚀 初始化代理模型管理器（传递设备参数）
        self.surrogate_manager = SurrogateManager(benchmark, axus, device)
        
        # 🌟 根据模式初始化代理模型
        if self.enable_ensemble:
            # 集成模式：初始化集成管理器（确保设备一致性）
            self.ensemble_manager = EnsembleManager(
                benchmark=benchmark,
                axus=axus,
                device=device,  # 传递设备参数
                model_types=['gp', 'rbf', 'tabpfn'],
                weight_window_size=50,
                min_weight_protection=0.1,
                performance_beta=5.0,
                top_k_candidates=5,
                softmax_temperature=5.0
            )
            self.global_surrogate = None  # 集成模式下不使用单一全局模型
            logging.info("🌟 集成代理模型系统已启用")
        else:
            # 单一模型模式：创建单一全局代理模型（确保设备一致性）
            model_kwargs = {}
            if global_model_type.lower() == 'tabpfn':
                model_kwargs['n_bins'] = tabpfn_n_bins
            # 所有模型都会自动从 surrogate_manager 传递的 device 参数获取设备配置
            self.global_surrogate = self.surrogate_manager.create_model(global_model_type, **model_kwargs)
            self.ensemble_manager = None
            logging.info(f"使用{global_model_type.upper()}作为单一全局代理模型，设备: {device}")

        # 初始化遗传算法
        if ga_config is None:
            ga_config = GAConfig(
                population_size=100,
                max_generations=ga_generations,
                crossover_rate=0.8,
                mutation_rate=0.15,
                tournament_size=3,
                elitism_rate=0.2
            )
        self.ga_config = ga_config
        # 在集成模式下，GA不使用特定的全局代理模型
        surrogate_for_ga = None if self.enable_ensemble else self.global_surrogate
        self.ga = MixedVariableGA(ga_config, axus, benchmark, surrogate_for_ga)
        
        # 存储历史数据用于训练全局代理模型
        self.global_X_history = []
        self.global_y_history = []
        
        mode_info = "集成代理模型" if self.enable_ensemble else "单一代理模型"
        logging.info(f"GA-{mode_info}集成系统初始化完成")

    def _is_pure_binary_problem(self) -> bool:
        """检查是否为纯二分问题"""
        if not hasattr(self.axus, 'bins_and_indices_of_type'):
            return True

        from bounce.util.benchmark import ParameterType
        # 检查是否有非二分变量
        for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
            if len(indices) > 0:
                return False
        for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            if len(indices) > 0:
                return False
        return True

    def run_global_search(self,
                         existing_X: Optional[torch.Tensor] = None,
                         existing_y: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        运行全局搜索（遗传算法单代进化）

        Args:
            existing_X: 现有的评估点（高维）
            existing_y: 现有的函数值

        Returns:
            最佳候选点（低维）和其在高维的表示
        """
        logging.debug("开始GA全局搜索（单代进化）...")

        # 注意：全局代理模型的更新在predict_best_center_with_tabpfn中进行，避免重复更新

        # 检查维度是否发生变化，如果变化则重新初始化GA
        current_target_dim = self.axus.target_dim
        if (not self.ga.population or
            (self.ga.population and self.ga.population[0].genes.shape[0] != current_target_dim)):

            logging.info(f"GA维度变化或未初始化，重新初始化GA (目标维度: {current_target_dim})")

            # 重新创建GA以适应新维度
            from bounce.genetic_algorithm import MixedVariableGA
            self.ga = MixedVariableGA(self.ga_config, self.axus, self.benchmark, self.global_surrogate)
            self.ga.initialize_population()
            logging.debug(f"GA种群重新初始化完成，种群大小: {len(self.ga.population)}, 维度: {current_target_dim}")

        # 运行一代进化
        self.ga.evolve_generation()

        # 获取当前最佳个体
        best_individual = self.ga.best_individual
        if best_individual is None:
            # 如果没有最佳个体，从种群中选择适应度最好的
            valid_population = [ind for ind in self.ga.population if ind.fitness is not None]
            if valid_population:
                best_individual = min(valid_population, key=lambda x: x.fitness)
            else:
                # 备选方案：使用第一个个体
                best_individual = self.ga.population[0]
                if best_individual.fitness is None:
                    # 强制评估
                    high_dim = best_individual.to_high_dim(self.benchmark.lb_vec, self.benchmark.ub_vec)
                    best_individual.fitness = self.benchmark(high_dim.unsqueeze(0)).item()

        # 获取最佳个体的低维和高维表示
        best_low_dim = best_individual.genes  # [-1, 1]范围
        best_high_dim = best_individual.to_high_dim(
            self.benchmark.lb_vec,
            self.benchmark.ub_vec
        )

        logging.debug(f"GA单代进化完成，当前代数: {self.ga.generation}, 最佳适应度: {best_individual.fitness:.6f}")

        return best_low_dim, best_high_dim

    def predict_best_center_with_tabpfn(self,
                                       existing_X: torch.Tensor,
                                       existing_y: torch.Tensor,
                                       n_candidates: int = 50,
                                       historical_points: torch.Tensor = None,
                                       current_best_low: torch.Tensor = None) -> torch.Tensor:
        """
        使用全局模型预测最佳的TR中心点

        Args:
            existing_X: 现有的评估点（高维）
            existing_y: 现有的函数值
            n_candidates: 候选点数量

        Returns:
            预测的最佳中心点（低维，[-1,1]范围）
        """
        # 🌟 集成模式：使用集成代理模型管理器
        if self.enable_ensemble:
            logging.debug("使用集成代理模型预测最佳TR中心点...")
            
            # 🔍 设备调试信息
            # logging.info(f"🔍 [DEVICE_DEBUG] existing_X设备: {existing_X.device if hasattr(existing_X, 'device') else 'N/A'}, 形状: {existing_X.shape if hasattr(existing_X, 'shape') else 'N/A'}")
            # logging.info(f"🔍 [DEVICE_DEBUG] existing_y设备: {existing_y.device if hasattr(existing_y, 'device') else 'N/A'}, 形状: {existing_y.shape if hasattr(existing_y, 'shape') else 'N/A'}")
            # logging.info(f"🔍 [DEVICE_DEBUG] self.device: {self.device}")
            
            # 生成候选点（在低维空间）
            candidates_low_dim = self._generate_candidate_centers(n_candidates, historical_points, current_best_low)
            # logging.info(f"🔍 [DEVICE_DEBUG] candidates_low_dim设备: {candidates_low_dim.device if hasattr(candidates_low_dim, 'device') else 'N/A'}, 形状: {candidates_low_dim.shape if hasattr(candidates_low_dim, 'shape') else 'N/A'}")
            
            try:
                # 使用集成管理器预测最佳中心
                # logging.info(f"🔍 [DEVICE_DEBUG] 即将调用 ensemble_manager.predict_best_center")
                
                best_center_low_dim = self.ensemble_manager.predict_best_center(
                    candidates=candidates_low_dim,
                    existing_X=existing_X,
                    existing_y=existing_y
                )
                
                # logging.info(f"🔍 [DEVICE_DEBUG] best_center_low_dim设备: {best_center_low_dim.device if hasattr(best_center_low_dim, 'device') else 'N/A'}, 形状: {best_center_low_dim.shape if hasattr(best_center_low_dim, 'shape') else 'N/A'}")
                # logging.debug(f"🎯 集成模型预测的最佳中心: {best_center_low_dim[:5]}...")
                
                return best_center_low_dim
                
            except Exception as e:
                logging.error(f"🛑 [DEVICE_DEBUG] 集成模型预测失败: {e}")
                import traceback
                logging.error(f"🛑 [DEVICE_DEBUG] 详细错误信息:\n{traceback.format_exc()}")
                raise
        
        # 🤖 单一模型模式：使用原有逻辑
        # logging.info("使用TabPFN预测最佳TR中心点...")
        
        # 更新全局代理模型（只在这里更新一次）
        self._update_global_surrogate(existing_X, existing_y)
        
        if not self.global_surrogate.is_fitted:
            logging.warning("全局模型未训练，使用GA最佳个体作为中心点")
            # 运行一个快速的GA来获取候选点
            quick_ga_config = GAConfig(
                population_size=20,
                max_generations=10,
                crossover_rate=0.8,
                mutation_rate=0.2
            )
            quick_ga = MixedVariableGA(quick_ga_config, self.axus, self.benchmark, self.global_surrogate)
            best_individual = quick_ga.run()
            return best_individual.genes
        
        # 生成候选点（在低维空间）
        candidates_low_dim = self._generate_candidate_centers(n_candidates, historical_points, current_best_low)
        
        # 转换为高维空间用于TabPFN预测
        candidates_high_dim = []
        for candidate in candidates_low_dim:
            # 🔧 关键修复：确保候选点满足AxUS对类别变量的格式要求
            candidate_for_projection = self._fix_categorical_format(candidate)

            # 确保是2D张量用于投影
            if candidate_for_projection.dim() == 1:
                candidate_for_projection = candidate_for_projection.unsqueeze(0)

            try:
                # 投影到高维
                high_dim = self.axus.project_up(candidate_for_projection.T).T
            except Exception as e:
                logging.error(f"AxUS投影失败: {e}")
                logging.error(f"候选点格式: {candidate_for_projection}")
                # 如果投影失败，跳过这个候选点
                continue
            # 确保是2D张量用于转换
            if high_dim.dim() == 1:
                high_dim = high_dim.unsqueeze(0)
            # 🎯 关键修复：对于纯二分问题，避免from_1_around_origin产生0.5值
            if self._is_pure_binary_problem():
                # 纯二分问题：直接将{-1,1}映射到{0,1}，避免0.5值
                high_dim_scaled = (high_dim + 1) / 2
                # 确保严格的{0,1}值
                high_dim_scaled = torch.round(high_dim_scaled)
                high_dim_scaled = torch.clamp(high_dim_scaled, 0.0, 1.0)
                if high_dim.dim() > 1:
                    high_dim_scaled = high_dim_scaled.squeeze(0)
            else:
                # 混合变量问题：使用from_1_around_origin
                from bounce.util.data_handling import from_1_around_origin
                high_dim_scaled = from_1_around_origin(
                    x=high_dim,
                    lb=self.benchmark.lb_vec,
                    ub=self.benchmark.ub_vec
                ).squeeze(0)

            candidates_high_dim.append(high_dim_scaled)
        
        candidates_high_dim = torch.stack(candidates_high_dim)
        
        # 🎯 改进：增加TabPFN选择的多样性
        # 获取所有候选点的质量分数
        quality_scores = self.global_surrogate.predict_quality(candidates_high_dim)

        # 排序获取最佳的几个候选点
        sorted_indices = torch.argsort(quality_scores)
        top_k = min(5, len(candidates_low_dim))  # 选择前5个最佳候选点
        top_indices = sorted_indices[:top_k]

        # 🎯 多样性策略：在前k个最佳候选中随机选择
        import random
        selected_idx = random.choice(top_indices.tolist())
        best_center_low_dim = candidates_low_dim[selected_idx]

        logging.debug(f"🎯 全局模型从前{top_k}个候选中选择了第{selected_idx}个: {best_center_low_dim}")
        logging.debug(f"🎯 质量分数排序: {quality_scores[sorted_indices[:top_k]]}")

        # 🎯 关键修复：确保返回的中心点是离散的
        if self._is_pure_binary_problem():
            # 纯二分问题：确保严格的{-1,1}值
            best_center_low_dim = torch.sign(best_center_low_dim)
            # 处理可能的0值
            zero_mask = best_center_low_dim == 0
            if zero_mask.any():
                best_center_low_dim[zero_mask] = torch.randint(0, 2, (zero_mask.sum(),), dtype=best_center_low_dim.dtype) * 2 - 1
            logging.debug(f"🔧 强制离散化后的中心点: {best_center_low_dim}")
        else:
            # 🎯 混合变量问题：分别处理不同类型的变量
            # 处理二分变量：确保严格的{-1,1}值
            for _, indices in self.axus.bins_and_indices_of_type(ParameterType.BINARY):
                if len(indices) > 0:
                    binary_part = torch.sign(best_center_low_dim[indices])
                    # 处理可能的0值
                    zero_mask = binary_part == 0
                    if zero_mask.any():
                        binary_part[zero_mask] = torch.randint(0, 2, (zero_mask.sum(),), dtype=best_center_low_dim.dtype) * 2 - 1
                    best_center_low_dim[indices] = binary_part

            # 连续变量保持原值（已经在[-1,1]范围内）
            # 类别变量需要确保one-hot编码
            for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                if len(indices) > 0:
                    # 找到最大值的位置，设为1，其他设为-1
                    max_idx = torch.argmax(best_center_low_dim[indices])
                    best_center_low_dim[indices] = -1
                    best_center_low_dim[indices[max_idx]] = 1

            logging.debug(f"🔧 混合变量处理后的中心点: {best_center_low_dim[:5]}...")
            
        # logging.info(f"TabPFN预测完成，预测质量分数: {quality_score:.6f}")
        
        return best_center_low_dim

    def _generate_candidate_centers(self, n_candidates: int, historical_points: torch.Tensor = None,
                                   current_best_low: torch.Tensor = None) -> torch.Tensor:
        """
        生成候选中心点（低维空间）

        Args:
            n_candidates: 候选点数量
            historical_points: 历史最优点（高维），用于增加多样性
            current_best_low: 当前最优解的低维表示
        """
        candidates = []

        # 🚀 方法0: 添加当前全局最优解（最重要！）
        if current_best_low is not None:
            candidates.append(current_best_low.clone())
            logging.debug(f"🏆 添加当前全局最优解到候选中心")

        # 🚀 方法1: 从GA种群中选择（主要来源，格式已经正确）
        if hasattr(self.ga, 'population') and self.ga.population:
            # 获取GA种群中的优秀个体，过滤掉fitness为None的个体
            valid_population = [ind for ind in self.ga.population if ind.fitness is not None]
            if valid_population:
                sorted_population = sorted(valid_population, key=lambda x: x.fitness)
                n_from_ga = min(max(n_candidates - len(candidates), n_candidates // 2), len(sorted_population))

                for i in range(n_from_ga):
                    ga_genes = sorted_population[i].genes.clone()
                    # GA种群的基因格式已经是正确的，直接使用
                    candidates.append(ga_genes)
        
        # 🚀 方法2: 如果还需要更多候选点，从GA种群随机选择
        n_remaining = n_candidates - len(candidates)
        if n_remaining > 0 and hasattr(self.ga, 'population') and self.ga.population:
            # 从整个GA种群中随机选择剩余的候选点
            import random
            if len(valid_population) > 0:
                remaining_population = [ind for ind in self.ga.population if ind not in sorted_population[:n_from_ga]]
                if len(remaining_population) >= n_remaining:
                    selected_individuals = random.sample(remaining_population, n_remaining)
                    for ind in selected_individuals:
                        candidates.append(ind.genes.clone())
                else:
                    # 如果GA种群不够，重复使用现有的
                    for i in range(n_remaining):
                        idx = i % len(self.ga.population)
                        candidates.append(self.ga.population[idx].genes.clone())
        
        return torch.stack(candidates)

    def _fix_categorical_format(self, candidate: torch.Tensor) -> torch.Tensor:
        """
        修复候选点的类别变量格式，确保满足AxUS的要求

        AxUS要求类别变量的格式：每个类别变量只有一个非-1的值
        """
        candidate_fixed = candidate.clone()

        if hasattr(self.axus, 'bins_and_indices_of_type'):
            from bounce.util.benchmark import ParameterType

            # 处理类别变量
            for _, indices in self.axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                if len(indices) > 0:
                    # 检查当前类别变量的格式
                    categorical_part = candidate_fixed[indices]
                    non_neg_one_count = torch.sum(categorical_part != -1)

                    if non_neg_one_count != 1:
                        # 格式不正确，修复为正确的one-hot编码
                        candidate_fixed[indices] = -1  # 先全部设为-1

                        # 找到最大值的位置，或随机选择一个
                        if non_neg_one_count > 1:
                            # 有多个非-1值，选择最大的那个
                            max_idx = torch.argmax(categorical_part)
                            candidate_fixed[indices[max_idx]] = 1
                        elif non_neg_one_count == 0:
                            # 没有非-1值，随机选择一个
                            selected_idx = torch.randint(0, len(indices), (1,)).item()
                            candidate_fixed[indices[selected_idx]] = 1
                        # 如果non_neg_one_count == 1，说明格式已经正确，不需要修改

        return candidate_fixed

    def _update_global_surrogate(self, X: torch.Tensor, y: torch.Tensor):
        """更新全局代理模型"""
        # 在集成模式下，不需要更新单一全局模型
        if self.enable_ensemble:
            return
        
        try:
            # 检查是否有新数据需要添加
            if len(self.global_X_history) == 0:
                # 首次训练，使用所有数据
                self.global_surrogate.fit(X, y)
                self.global_X_history.append(X.clone())
                self.global_y_history.append(y.clone())
                logging.info(f"全局模型首次训练，样本数: {len(X)}")
            else:
                # 检查是否有新的数据点
                last_X = self.global_X_history[-1]
                last_y = self.global_y_history[-1]

                if len(X) > len(last_X):
                    # 有新数据，只添加新的部分
                    new_X = X[len(last_X):]
                    new_y = y[len(last_y):]

                    if len(new_X) > 0:
                        # 🔧 修复：使用正确的方法名
                        self.global_surrogate.update(new_X, new_y)
                        self.global_X_history.append(X.clone())
                        self.global_y_history.append(y.clone())
                        # logging.info(f"全局模型增量更新，新增样本数: {len(new_X)}, 总样本数: {len(X)}")
                    else:
                        logging.debug("没有新数据，跳过全局模型更新")
                else:
                    logging.debug("数据量未增加，跳过全局模型更新")

        except Exception as e:
            logging.error(f"更新全局代理模型失败: {e}")

    def get_integration_info(self) -> dict:
        """获取集成系统信息"""
        ga_info = {
            "population_size": self.ga_config.population_size,
            "max_generations": self.ga_config.max_generations,
            "current_generation": self.ga.generation,
            "best_fitness": self.ga.best_individual.fitness if self.ga.best_individual else None
        }
        
        surrogate_info = self.global_surrogate.get_model_info()
        
        return {
            "ga_info": ga_info,
            "surrogate_info": surrogate_info,
            "total_global_samples": sum(len(x) for x in self.global_X_history)
        }

    def reset(self):
        """重置集成系统"""
        self.ga = MixedVariableGA(self.ga_config, self.axus, self.benchmark)

        # 重新创建全局代理模型
        model_kwargs = {}
        if self.global_model_type.lower() == 'tabpfn' and hasattr(self.global_surrogate, 'n_bins'):
            model_kwargs['n_bins'] = self.global_surrogate.n_bins

        self.surrogate_manager.clear_models()
        self.global_surrogate = self.surrogate_manager.create_model(self.global_model_type, **model_kwargs)

        self.global_X_history = []
        self.global_y_history = []
        logging.info("GA-代理模型集成系统已重置")
