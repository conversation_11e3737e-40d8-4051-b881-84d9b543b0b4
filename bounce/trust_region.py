import math

import gin
import numpy as np
import torch


@gin.configurable
class TrustRegion:
    """
    这是核心类，表示当前的“信赖域”对象，它负责动态管理搜索空间的收缩与扩张。
    Trust region object for the trust region algorithm.
    """

    def __init__(
        self,
        dimensionality: int,                        # 当前低维嵌入的维度 意味着信赖域在 dimensionality 维空间中操作，限制搜索范围。
        length_init_discrete: int = 40,             # 离散变量初始TR半径（单位是步数）这意味着初始搜索范围包括与当前点汉明距离不超过 3 的所有点。
        length_init_continuous: float = 0.8,        # 连续变量初始TR半径（单位是范围）这意味着初始搜索范围是当前点周围 ±0.8 的区域
    ):
        """

        Args:
            dimensionality: the dimensionality of the trust region
            length_init_discrete: the initial trust region length for discrete variables
            length_init_continuous: the initial trust region length for continuous variables
        """
        self.dimensionality = dimensionality

        # 离散信赖域半径不能超过维度限制
        """
            因为在离散空间中，最大可能的汉明距离就是维度本身。
            例如，在5维二进制空间中，两点之间的最大汉明距离是5。
            如果用户设置 length_init_discrete = 40（如默认值），但当前维度只有10维，
            那么实际上只需要10的半径就能覆盖整个搜索空间。这行代码通过取最小值来避免不必要的大半径。
        """
        self.length_init_discrete = min(length_init_discrete, dimensionality)
        self.length_init_continuous = length_init_continuous

        # 最小搜索范围 离散、连续变量的最小信赖域半径，
        # 当信赖域收缩到这个值时，信赖域被认为是"终止"的。
        # 连续变量的最小信赖域半径，当信赖域收缩到这个值时，信赖域被认为是"终止"的。
        self.length_min_discrete = 1
        self.length_min_continuous = 0.5**7

        # 最大搜索范围 
        # 离散变量的最大信赖域半径，信赖域不会扩大超过这个值。
        # 连续变量的最大信赖域半径，信赖域不会扩大超过这个值。
        self.length_max_discrete = dimensionality
        self.length_max_continuous = 1.6

        # 记录当前离散变量的信赖域半径、离散-连续维度、连续维度
        self.length_discrete = self.length_init_discrete

        # 当前离散变量的信赖域半径的连续表示 这个值用于平滑地更新 length_discrete。
        # 如果 length_discrete_continuous = 2.7，这意味着离散信赖域半径的"真实"值是 2.7，但实际使用时会向下取整为 length_discrete = 2。
        # 这个连续表示允许算法更平滑地调整信赖域大小，而不是直接在整数值之间跳跃。
        self.length_discrete_continuous = float(self.length_init_discrete)

        # 当前连续变量的信赖域半径，这个值会在算法运行过程中动态更新。
        self.length_continuous = self.length_init_continuous

        # 一个布尔标志，表示信赖域是否已经终止。
        self.terminated = False

    def reset(self):
        """
        重置TR的初始状态
        Reset the trust region to its initial state.

        Returns:
            None

        """
        self.length_discrete = self.length_init_discrete
        self.length_discrete_continuous = float(self.length_init_discrete)
        self.length_continuous = self.length_init_continuous
        self.terminated = False


def update_tr_state(
    trust_region: TrustRegion,
    fx_next: torch.Tensor,              # 本轮新采样点的最优值
    fx_incumbent: torch.Tensor,         # 历史上的最优值（也就是之前的 best）
    adjustment_factor: np.double,       # 调整因子，用来控制信赖域收缩 or 放大的幅度
) -> None:
    """
    它负责根据优化过程中的进展动态调整信赖域（Trust Region）的大小
    Update the trust region state based on the current function value and the incumbent function value.

    Args:
        trust_region: the trust region object
        fx_next: the function value of the next point
        fx_incumbent: the incumbent function value
        adjustment_factor: the adjustment factor for the trust region length

    Returns:
        None

    """

    # 判断是否有改进 如果 fx_next 和 fx_incumbent 差别不大（小于 0.1%），认为没有显著改进
    if fx_next >= fx_incumbent - 1e-3 * math.fabs(fx_incumbent):
        # 这段代码将离散和连续变量的信赖域半径都乘以调整因子（通常小于1），从而收缩信赖域。
        trust_region.length_discrete_continuous = (
            trust_region.length_discrete_continuous * adjustment_factor.item()
        )
        trust_region.length_discrete = max(
            1, math.floor(trust_region.length_discrete_continuous)
        )

        trust_region.length_continuous = (
            trust_region.length_continuous * adjustment_factor.item()
        )
    else:
        # 这段代码将离散和连续变量的信赖域半径都除以调整因子（相当于乘以一个大于1的数），从而扩大信赖域。同时，它确保信赖域不会超过最大值。
        trust_region.length_discrete_continuous = min(
            trust_region.length_discrete_continuous / adjustment_factor.item(),
            trust_region.length_max_discrete,
        )
        trust_region.length_discrete = max(
            1, math.floor(trust_region.length_discrete_continuous)
        )

        trust_region.length_continuous = min(
            trust_region.length_continuous / adjustment_factor.item(),
            trust_region.length_max_continuous,
        )

    # 这段代码检查信赖域是否已经收缩到最小值，
    # 如果是，则将 trust_region.terminated 设置为  True，表示当前信赖域已经终止。
    # 1e-4 防止浮点精度误差造成“迟迟不触发终止条件”的问题，也叫 浮点容忍窗口（float tolerance margin）。
    discrete_terminated = (
        trust_region.length_discrete_continuous - 1e-4
        <= trust_region.length_min_discrete
    )
    continuous_terminated = (
        trust_region.length_continuous - 1e-4 <= trust_region.length_min_continuous
    )

    if discrete_terminated or continuous_terminated:
        trust_region.terminated = True
