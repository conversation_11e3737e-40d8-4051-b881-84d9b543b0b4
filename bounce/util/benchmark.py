import dataclasses
import json                          # 负责将对象编码成 JSON 格式或解析 JSON 字符串
import os
import random
import subprocess                    # 用于执行终端命令，如运行外部 Python 程序
from dataclasses import dataclass
from enum import Enum
from typing import Optional

# 这是一个接口函数，用于调用一个封装在 Singularity 容器中的 benchmark 测试函数来评估某个点。
def eval_singularity_benchmark(
    eval_points: list[list[float]], singularity_image_path: str, name: str
) -> float:
    """
    Evaluate a benchmark function in a singularity image

    Args:
        eval_points: the points to evaluate   要评估的点（二维列表），每个子列表是一个解向量。
        singularity_image_path: the path to the singularity image   存放 Singularity 镜像路径，也就是封装 benchmark 的文件夹。
        name: the name of the benchmark function    benchmark 函数的名称，作为参数传递给容器中的 main.py。

    Returns:
        the result of the evaluation

    """

    # 构造一个 shell 命令，用于调用容器中的 Python 脚本来执行 benchmark 评估
    # cd {singularity_image_path}：切换到 benchmark 所在目录；
    # poetry env info --path：获取 poetry 的虚拟环境路径；
    # $( ... )/bin/python3：使用该环境中的 python 执行 main.py；
    # --name {name}：指定评估的 benchmark 函数名称；
    cmd = (
        f"$( cd {singularity_image_path} ; poetry env info --path)/bin/python3 {os.path.join(singularity_image_path, 'main.py')} --name {name} "
        f"-x {' '.join(list(map(lambda _x: str(_x), eval_points)))}"
    )

    # 运行上面构造的 shell 命令，并设置特定的环境变量
    process = subprocess.check_output(
        cmd,
        shell=True,
        env={
            **os.environ,
            **{
                "LD_LIBRARY_PATH": f"{singularity_image_path}/data/mujoco210/bin:/usr/lib/nvidia",
                "MUJOCO_PY_MUJOCO_PATH": f"{singularity_image_path}/data/mujoco210",
            },
        },
    )
    # 将输出解码为字符串，并按换行符分割；
    res = process.decode().split("\n")

    # 取倒数第二行作为 benchmark 的输出（这是 main.py 的输出格式假设）；转换为 float 作为最终评估值返回。
    return float(res[-2])


@dataclass
class BenchmarkRequest:
    """
    这是一个用于封装 benchmark 请求的数据结构。
    它表示“我要在哪个 benchmark 上评估哪些点，并可选附带一些额外设置”。
    A request to evaluate a benchmark function

    Args:
        function: the name of the benchmark function
        dim: the dimension of the benchmark function
        eval_points: the points to evaluate
        effective_dim: the effective dimension of the benchmark function
        noise_std: the noise standard deviation of the benchmark function
        max_steps: the maximum number of steps to evaluate the benchmark function
    """

    function: str                               # 代表 benchmark 的名称
    dim: int                                    # 要评估的函数的维度
    eval_points: list[list[float]]              # 要评估的一批点
    effective_dim: Optional[int] = None         # 表示有效维度
    noise_std: Optional[float] = None           # 可选字段，用来模拟有噪声的函数
    max_steps: Optional[int] = 2000             # 评估函数允许的最大步骤数

    # 把这个请求转成 JSON 格式的字符串，方便传输
    def as_json(self) -> str:
        """
        Convert the request to a json string

        Returns:
            the json string

        """
        return json.dumps(dataclasses.asdict(self))


class ParameterType(Enum):
    """
    The type of a parameter
    """

    CONTINUOUS = "continuous"
    BINARY = "binary"
    CATEGORICAL = "categorical"
    ORDINAL = "ordinal"


@dataclass
class Parameter:
    """
    用来封装一个变量（一个决策维度）的完整属性信息，如名称、类型、取值范围、随机扰动方式等。
    A parameter of a benchmark function

    Args:
        name: the name of the parameter
        type: the type of the parameter
        lower_bound: the lower bound of the parameter
        upper_bound: the upper bound of the parameter
        random_sign: the random sign of the parameter (will be randomly chosen if None)
        n_realizations: the number of realizations of the parameter (will be computed automatically if None)
    """

    name: str
    type: ParameterType
    lower_bound: float
    upper_bound: float
    random_sign: int = None            # 用于在 AxUS 映射中添加扰动的随机符号
    n_realizations: float | int = dataclasses.field(init=False)     # 变量能取多少个不同的值

    # 这个方法在 dataclass 创建完对象后自动执行
    def __post_init__(self):
        # 添加随机扰动
        if self.random_sign is None:
            if (
                self.type == ParameterType.BINARY
                or self.type == ParameterType.CONTINUOUS
            ):
                self.random_sign = random.choice([-1, 1])
            elif self.type == ParameterType.CATEGORICAL:
                # random sign random int in [0, n_realizations]
                n_realizations = int(self.upper_bound - self.lower_bound + 1)
                self.random_sign = random.randint(0, n_realizations - 1)
            elif self.type == ParameterType.ORDINAL:
                raise NotImplementedError(
                    "Random sign for ordinal parameters not implemented"
                )
            else:
                raise ValueError(f"Unknown parameter type {self.type}")
        if self.type == ParameterType.CATEGORICAL:
            assert float(
                self.lower_bound
            ).is_integer(), "Categorical parameters must have integer lower bound"
            assert float(
                self.upper_bound
            ).is_integer(), "Categorical parameters must have integer upper bound"
            assert (
                self.lower_bound < self.upper_bound
            ), "Categorical parameters must have lower bound < upper bound"
            self.n_realizations = int(self.upper_bound - self.lower_bound + 1)
        elif self.type == ParameterType.ORDINAL:
            assert float(
                self.lower_bound
            ).is_integer(), "Ordinal parameters must have integer lower bound"
            assert float(
                self.upper_bound
            ).is_integer(), "Ordinal parameters must have integer upper bound"
            assert (
                self.lower_bound < self.upper_bound
            ), "Ordinal parameters must have lower bound < upper bound"
            self.n_realizations = int(self.upper_bound - self.lower_bound + 1)
        elif self.type == ParameterType.BINARY:
            assert self.lower_bound == 0, "Binary parameters must have lower bound 0"
            assert self.upper_bound == 1, "Binary parameters must have upper bound 1"
            assert self.random_sign in [
                -1,
                1,
            ], "Binary parameters must have random sign in [-1, 1]"
            self.n_realizations = 2
        elif self.type == ParameterType.CONTINUOUS:
            assert (
                self.lower_bound < self.upper_bound
            ), "Continuous parameters must have lower bound < upper bound"
            self.n_realizations = float("inf")

    @property
    def dims_required(self) -> int:
        """
        返回当前变量在输入张量中所需要占用的维度数：
        The number of dimensions required to represent the parameter

        Returns:
            the number of dimensions required to represent the parameter

        """
        match self.type:
            case ParameterType.CONTINUOUS:
                return 1
            case ParameterType.CATEGORICAL:
                return self.n_realizations
            case ParameterType.ORDINAL:
                return 1
            case ParameterType.BINARY:
                return 1
            case _:
                raise ValueError(f"Unknown parameter type {self.type}")
