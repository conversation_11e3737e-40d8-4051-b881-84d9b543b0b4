#!/usr/bin/env python3
"""
测试本地TabPFN模型的集成
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_local_tabpfn_basic():
    """测试本地TabPFN基础功能"""
    
    print("🧪 测试本地TabPFN基础功能")
    print("=" * 50)
    
    try:
        from tabpfn import TabPFNClassifier
        print("✅ 成功导入本地TabPFN")
        
        # 创建简单测试数据
        from sklearn.datasets import make_classification
        X, y = make_classification(n_samples=100, n_features=10, n_classes=3,
                                 n_informative=5, n_redundant=2, random_state=42)
        
        # 初始化本地TabPFN
        clf = TabPFNClassifier(device='cpu')
        print("✅ 成功初始化本地TabPFN分类器")
        
        # 训练
        clf.fit(X, y)
        print("✅ 成功训练本地TabPFN模型")
        
        # 预测
        predictions = clf.predict(X[:5])
        probabilities = clf.predict_proba(X[:5])
        
        print(f"预测结果: {predictions}")
        print(f"预测概率形状: {probabilities.shape}")
        print("✅ 本地TabPFN基础功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 本地TabPFN基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tabpfn_surrogate_local():
    """测试TabPFN代理模型使用本地模型"""
    
    print("\n" + "=" * 50)
    print("🧪 测试TabPFN代理模型使用本地模型")
    print("=" * 50)
    
    try:
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 2
                self.representation_dim = 2
                self.lb_vec = torch.zeros(2, dtype=torch.float64)
                self.ub_vec = torch.ones(2, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        
        # 创建TabPFN代理模型（使用本地模型）
        surrogate = TabPFNGlobalSurrogate(benchmark, n_bins=3, device='cpu')
        print("✅ 成功创建TabPFN代理模型（本地）")
        
        # 生成训练数据
        X_train = torch.rand(20, 2, dtype=torch.float64)
        y_train = torch.rand(20, dtype=torch.float64)
        
        # 训练
        surrogate.fit(X_train, y_train)
        print("✅ 成功训练TabPFN代理模型")
        
        # 预测
        X_test = torch.rand(5, 2, dtype=torch.float64)
        quality_scores = surrogate.predict_quality(X_test)
        
        print(f"预测质量分数: {quality_scores}")
        print(f"质量分数形状: {quality_scores.shape}")
        
        # 验证结果
        assert quality_scores.shape[0] == 5, "预测结果数量不正确"
        assert torch.all(quality_scores >= 0), "质量分数应该非负"
        
        print("✅ TabPFN代理模型本地测试通过")
        return True
        
    except Exception as e:
        print(f"❌ TabPFN代理模型本地测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ga_tabpfn_integration_local():
    """测试GA-TabPFN集成使用本地模型"""
    
    print("\n" + "=" * 50)
    print("🧪 测试GA-TabPFN集成使用本地模型")
    print("=" * 50)
    
    try:
        from bounce.ga_tabpfn_integration import GATabPFNIntegration
        from bounce.genetic_algorithm import GAConfig
        from bounce.projection import AxUS
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 2
                self.representation_dim = 2
                self.lb_vec = torch.zeros(2, dtype=torch.float64)
                self.ub_vec = torch.ones(2, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        axus = AxUS(parameters=benchmark.parameters, n_bins=2)
        
        # 创建GA配置
        config = GAConfig(population_size=10, max_generations=3)
        
        # 创建GA-TabPFN集成（使用本地模型）
        integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=config,
            tabpfn_n_bins=3,
            ga_generations=3,
            device='cpu'  # 使用本地CPU
        )
        print("✅ 成功创建GA-TabPFN集成（本地）")
        
        # 生成一些训练数据
        existing_X = torch.rand(15, 2, dtype=torch.float64)
        existing_y = torch.rand(15, dtype=torch.float64)
        
        # 测试全局搜索
        best_low_dim, best_high_dim = integration.run_global_search(existing_X, existing_y)
        print(f"✅ 全局搜索完成，最佳解维度: {best_low_dim.shape}")
        
        # 测试TabPFN预测中心点
        predicted_center = integration.predict_best_center_with_tabpfn(
            existing_X, existing_y, n_candidates=5
        )
        print(f"✅ TabPFN预测中心点完成，中心点维度: {predicted_center.shape}")
        
        # 验证结果
        assert best_low_dim.shape[0] == axus.target_dim, "最佳解维度不正确"
        assert predicted_center.shape[0] == axus.target_dim, "预测中心点维度不正确"
        
        print("✅ GA-TabPFN集成本地测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GA-TabPFN集成本地测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_setup():
    """测试环境设置"""
    
    print("\n" + "=" * 50)
    print("🧪 测试环境设置")
    print("=" * 50)
    
    # 检查TabPFN安装
    try:
        import tabpfn
        print(f"✅ TabPFN版本: {tabpfn.__version__ if hasattr(tabpfn, '__version__') else '未知'}")
    except ImportError:
        print("❌ TabPFN未安装，请运行: pip install tabpfn")
        return False
    
    # 检查环境变量
    if 'TABPFN_ALLOW_CPU_LARGE_DATASET' in os.environ:
        print(f"✅ 环境变量设置: TABPFN_ALLOW_CPU_LARGE_DATASET = {os.environ['TABPFN_ALLOW_CPU_LARGE_DATASET']}")
    else:
        print("ℹ️  环境变量TABPFN_ALLOW_CPU_LARGE_DATASET未设置")
    
    # 检查设备
    if torch.cuda.is_available():
        print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
        print(f"   当前设备: {torch.cuda.current_device()}")
        print(f"   设备名称: {torch.cuda.get_device_name()}")
    else:
        print("ℹ️  CUDA不可用，将使用CPU")
    
    return True


if __name__ == "__main__":
    print("🚀 开始测试本地TabPFN集成")
    print("=" * 60)
    
    # 测试环境
    env_ok = test_environment_setup()
    if not env_ok:
        print("\n❌ 环境测试失败，请检查TabPFN安装")
        sys.exit(1)
    
    # 测试基础功能
    basic_ok = test_local_tabpfn_basic()
    
    # 测试代理模型
    surrogate_ok = test_tabpfn_surrogate_local()
    
    # 测试集成
    integration_ok = test_ga_tabpfn_integration_local()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   环境设置: {'✅ 通过' if env_ok else '❌ 失败'}")
    print(f"   基础功能: {'✅ 通过' if basic_ok else '❌ 失败'}")
    print(f"   代理模型: {'✅ 通过' if surrogate_ok else '❌ 失败'}")
    print(f"   集成测试: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if all([env_ok, basic_ok, surrogate_ok, integration_ok]):
        print("\n🎉 所有测试通过！本地TabPFN集成成功！")
        print("\n📝 修改总结:")
        print("1. ✅ 将tabpfn_client改为本地tabpfn")
        print("2. ✅ 添加device参数支持")
        print("3. ✅ 设置CPU大数据集环境变量")
        print("4. ✅ 更新所有相关组件")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        sys.exit(1)
