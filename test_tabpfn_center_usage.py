#!/usr/bin/env python3
"""
测试TabPFN中心点的实际使用
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_tabpfn_center_usage():
    """测试TabPFN中心点是否被正确使用"""
    
    print("🧪 测试TabPFN中心点的实际使用")
    print("=" * 50)
    
    from bounce.candidates import create_candidates_discrete
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    from bounce.trust_region import TrustRegion
    from botorch.models import SingleTaskGP
    from gpytorch.kernels import MaternKernel
    from gpytorch.means import ConstantMean
    from gpytorch.likelihoods import GaussianLikelihood
    
    # 创建纯二分变量基准
    parameters = [
        Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
        for i in range(5)
    ]
    
    axus = AxUS(parameters=parameters, n_bins=5)
    
    print(f"参数类型: {[p.type for p in parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    print(f"AxUS输入维度: {axus.input_dim}")
    
    # 创建一些模拟的已评估点
    x_scaled = torch.rand(10, axus.target_dim, dtype=torch.float64)
    # 确保是{0,1}值
    x_scaled = torch.round(x_scaled)
    fx_scaled = torch.rand(10, dtype=torch.float64)
    
    print(f"\n已评估点: {x_scaled}")
    print(f"函数值: {fx_scaled}")
    
    # 找到当前最佳点
    best_idx = fx_scaled.argmin()
    current_best = x_scaled[best_idx]
    print(f"当前最佳点: {current_best}")
    
    # 创建TabPFN预测的中心点（不同于当前最佳点）
    tabpfn_center = torch.tensor([1., 0., 1., 0., 1.], dtype=torch.float64)
    x_bests = tabpfn_center.unsqueeze(0)  # 转换为batch格式
    
    print(f"TabPFN预测的中心点: {tabpfn_center}")
    print(f"是否与当前最佳点相同: {torch.allclose(tabpfn_center, current_best)}")
    
    # 创建简单的GP模型
    likelihood = GaussianLikelihood()
    model = SingleTaskGP(
        train_X=x_scaled,
        train_Y=fx_scaled.unsqueeze(-1),
        likelihood=likelihood
    )
    
    # 创建信任域
    trust_region = TrustRegion(
        dimensionality=axus.target_dim,
        length_init_discrete=2,
        length_init_continuous=0.8,
    )
    
    print(f"\n测试1: 不使用TabPFN中心点")
    x_best_no_tabpfn, fx_best_no_tabpfn, tr_state_no_tabpfn = create_candidates_discrete(
        x_scaled=x_scaled,
        fx_scaled=fx_scaled,
        acquisition_function=None,
        model=model,
        axus=axus,
        trust_region=trust_region,
        device='cpu',
        batch_size=1,
        x_bests=None,  # 不使用TabPFN中心点
        add_spray_points=False,
        sampler=None,
    )
    
    print(f"不使用TabPFN时的候选点: {x_best_no_tabpfn}")
    
    print(f"\n测试2: 使用TabPFN中心点")
    x_best_with_tabpfn, fx_best_with_tabpfn, tr_state_with_tabpfn = create_candidates_discrete(
        x_scaled=x_scaled,
        fx_scaled=fx_scaled,
        acquisition_function=None,
        model=model,
        axus=axus,
        trust_region=trust_region,
        device='cpu',
        batch_size=1,
        x_bests=x_bests,  # 使用TabPFN中心点
        add_spray_points=False,
        sampler=None,
    )
    
    print(f"使用TabPFN时的候选点: {x_best_with_tabpfn}")
    
    # 检查是否真的使用了TabPFN中心点
    center_used_no_tabpfn = tr_state_no_tabpfn["center"].flatten()
    center_used_with_tabpfn = tr_state_with_tabpfn["center"].flatten()
    
    print(f"\n不使用TabPFN时的TR中心: {center_used_no_tabpfn}")
    print(f"使用TabPFN时的TR中心: {center_used_with_tabpfn}")
    
    # 验证修复是否生效
    expected_center_with_tabpfn = tabpfn_center.numpy()
    is_using_tabpfn = torch.allclose(
        torch.from_numpy(center_used_with_tabpfn), 
        tabpfn_center, 
        atol=1e-6
    )
    
    print(f"期望的TabPFN中心: {expected_center_with_tabpfn}")
    print(f"是否真正使用了TabPFN中心: {is_using_tabpfn}")
    
    if is_using_tabpfn:
        print("✅ 修复成功：TabPFN预测的中心点被正确使用")
        return True
    else:
        print("❌ 修复失败：TabPFN预测的中心点没有被使用")
        return False


if __name__ == "__main__":
    print("🚀 开始测试TabPFN中心点使用")
    print("=" * 60)
    
    try:
        success = test_tabpfn_center_usage()
        
        if success:
            print("\n🎉 测试通过！")
            print("\n📝 修复总结:")
            print("1. ✅ 修复了create_candidates_discrete中心点选择逻辑")
            print("2. ✅ 对于纯二分问题，完全使用TabPFN预测的中心点")
            print("3. ✅ 对于混合变量问题，部分使用TabPFN预测的中心点")
            print("\n🚨 重要：现在TabPFN预测的TR中心点会被真正使用！")
        else:
            print("\n❌ 测试失败，需要进一步调试")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
