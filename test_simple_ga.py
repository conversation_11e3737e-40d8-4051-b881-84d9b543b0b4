#!/usr/bin/env python3
"""简单的遗传算法测试脚本"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
from bounce.genetic_algorithm import MixedVariableGA, GAConfig, Individual
from bounce.projection import AxUS
from bounce.util.benchmark import Parameter, ParameterType

# 设置日志
logging.basicConfig(level=logging.INFO)


class SimpleBenchmark:
    """简单的测试基准函数"""
    
    def __init__(self, dim=6):
        self.dim = dim
        self.representation_dim = dim
        self.parameters = [
            Parameter(
                name=f"x{i}",
                type=ParameterType.BINARY if i < dim//2 else ParameterType.CONTINUOUS,
                lower_bound=0.0,
                upper_bound=1.0,
            )
            for i in range(dim)
        ]
        self.lb_vec = torch.zeros(dim, dtype=torch.float64)
        self.ub_vec = torch.ones(dim, dtype=torch.float64)
        self.is_discrete = True
        self.is_continuous = False
        self.is_mixed = True
        
    def __call__(self, x):
        """简单的测试函数：最小化所有变量的平方和"""
        if x.dim() == 1:
            x = x.unsqueeze(0)
        return torch.sum(x**2, dim=1)


def test_individual():
    """测试个体类"""
    print("🧬 测试个体类...")
    
    benchmark = SimpleBenchmark(dim=4)
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建个体
    genes = torch.rand(4, dtype=torch.float64) * 2 - 1  # [-1, 1]范围
    individual = Individual(genes, axus)
    
    print(f"基因: {genes}")
    
    # 测试高维转换
    high_dim = individual.to_high_dim(benchmark.lb_vec, benchmark.ub_vec)
    print(f"高维表示: {high_dim}")
    print(f"高维形状: {high_dim.shape}")
    
    assert high_dim.shape[0] == benchmark.representation_dim
    assert torch.all(high_dim >= benchmark.lb_vec)
    assert torch.all(high_dim <= benchmark.ub_vec)
    
    print("✅ 个体测试通过")


def test_ga():
    """测试遗传算法"""
    print("\n🧬 测试遗传算法...")
    
    benchmark = SimpleBenchmark(dim=6)
    axus = AxUS(parameters=benchmark.parameters, n_bins=6)
    
    config = GAConfig(population_size=10, max_generations=5)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"二分变量索引: {ga.binary_indices}")
    print(f"连续变量索引: {ga.continuous_indices}")
    
    # 初始化种群
    ga.initialize_population()
    print(f"种群大小: {len(ga.population)}")
    
    # 运行几代
    for generation in range(3):
        ga.evolve_generation()
        print(f"第{generation+1}代，最佳适应度: {ga.best_individual.fitness:.6f}")
    
    print("✅ 遗传算法测试通过")


def test_mixed_variables():
    """测试混合变量类型"""
    print("\n🧬 测试混合变量类型...")
    
    # 创建包含不同类型变量的基准函数
    parameters = [
        Parameter(name="binary1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="binary2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="continuous1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="continuous2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="categorical1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=2),
    ]
    
    class MixedBenchmark:
        def __init__(self):
            self.parameters = parameters
            self.dim = len(parameters)
            self.representation_dim = 7  # 类别变量需要3维one-hot编码
            self.lb_vec = torch.tensor([0, 0, 0, 0, 0, 0, 0], dtype=torch.float64)
            self.ub_vec = torch.tensor([1, 1, 1, 1, 1, 1, 1], dtype=torch.float64)
            self.is_mixed = True
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = MixedBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    config = GAConfig(population_size=8, max_generations=3)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"二分变量索引: {ga.binary_indices}")
    print(f"连续变量索引: {ga.continuous_indices}")
    print(f"类别变量索引: {ga.categorical_indices}")
    
    # 初始化种群
    ga.initialize_population()
    
    # 验证类别变量的one-hot编码
    for i, individual in enumerate(ga.population[:3]):
        categorical_genes = individual.genes[ga.categorical_indices]
        print(f"个体{i+1}类别基因: {categorical_genes}")
        assert torch.sum(categorical_genes == 1) == 1
        assert torch.sum(categorical_genes == -1) == len(ga.categorical_indices) - 1
    
    print("✅ 混合变量类型测试通过")


if __name__ == "__main__":
    try:
        test_individual()
        test_ga()
        test_mixed_variables()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
