#!/usr/bin/env python3
"""
简化测试：直接测试K-means精英选择在Bounce中的集成
"""

import gin
import logging
from bounce.bounce import Bo<PERSON><PERSON>

def test_maxsat60_kmeans():
    """测试MaxSat60问题的K-means精英选择"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
    
    print("🧬 测试MaxSat60问题的K-means精英选择机制")
    
    # 清除并加载配置
    gin.clear_config()
    gin.parse_config_file("configs/default.gin")
    
    # 创建Bounce实例
    bounce = Bounce()
    
    print(f"✅ 基准函数: {bounce.benchmark.fun_name}")
    print(f"✅ 维度: {bounce.benchmark.dim}")
    print(f"✅ 最大评估次数: {bounce.maximum_number_evaluations}")
    
    # 检查GA配置
    ga_config = bounce.ga_tabpfn_integration.ga_config
    print(f"✅ GA种群大小: {ga_config.population_size}")
    print(f"✅ K-means精英选择: {ga_config.use_kmeans_elite_selection}")
    print(f"✅ 精英比例: {ga_config.elitism_rate}")
    print(f"✅ 聚类比例: {ga_config.elite_clusters_ratio}")
    
    # 执行初始采样
    print("🔍 执行初始采样...")
    bounce.sample_init()
    print(f"✅ 初始采样完成，评估了{len(bounce.fx_global)}个点")
    
    # 测试一次完整的GA-全局搜索
    print("🧬 测试GA全局搜索...")
    try:
        best_low_dim, best_high_dim = bounce.ga_tabpfn_integration.run_global_search(
            existing_X=bounce.x_up_global,
            existing_y=bounce.fx_global
        )
        print("✅ GA全局搜索成功完成")
        print(f"   最佳低维解: {best_low_dim[:5]}...")
        print(f"   GA当前代数: {bounce.ga_tabpfn_integration.ga.generation}")
        
        # 获取种群多样性
        diversity = bounce.ga_tabpfn_integration.ga.get_population_diversity()
        print(f"   种群多样性: {diversity:.4f}")
        
    except Exception as e:
        print(f"❌ GA全局搜索失败: {e}")
        return False
    
    # 测试一步完整的Bounce优化
    print("🚀 测试一步Bounce优化...")
    try:
        # 保存当前评估次数
        initial_evals = bounce._n_evals
        
        # 运行一步优化（修改最大评估次数）
        original_max_evals = bounce.maximum_number_evaluations
        bounce.maximum_number_evaluations = initial_evals + 3  # 只多运行3次评估
        
        # 执行一步优化
        bounce.run()
        
        final_evals = bounce._n_evals
        print(f"✅ Bounce优化完成，总评估次数: {final_evals}")
        print(f"   当前最优值: {bounce.fx_global.min().item():.6f}")
        
        # 恢复原始设置
        bounce.maximum_number_evaluations = original_max_evals
        
    except Exception as e:
        print(f"❌ Bounce优化失败: {e}")
        return False
    
    print("🎉 MaxSat60的K-means精英选择测试完成！")
    return True

def test_ackley53_kmeans():
    """测试Ackley53问题的K-means精英选择"""
    
    print("\n🧬 测试Ackley53问题的K-means精英选择机制")
    
    # 修改配置为Ackley53
    gin.clear_config()
    gin.parse_config_files_and_bindings(
        ["configs/default.gin"], 
        ["Bounce.benchmark = @Ackley53()", "AckleyEffectiveDim.dim = 53"]
    )
    
    try:
        # 创建Bounce实例
        bounce = Ackley53()
        
        print(f"✅ 基准函数: {bounce.benchmark.fun_name}")
        print(f"✅ 维度: {bounce.benchmark.dim}")
        
        # 快速测试
        bounce.sample_init()
        print(f"✅ Ackley53初始采样完成，评估了{len(bounce.fx_global)}个点")
        
        return True
        
    except Exception as e:
        print(f"❌ Ackley53测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 K-means精英选择集成测试")
    print("="*50)
    
    # 测试MaxSat60
    maxsat_success = test_maxsat60_kmeans()
    
    # 测试Ackley53
    # ackley_success = test_ackley53_kmeans()
    
    print("\n📋 测试结果:")
    print(f"   MaxSat60: {'✅ 通过' if maxsat_success else '❌ 失败'}")
    # print(f"   Ackley53: {'✅ 通过' if ackley_success else '❌ 失败'}")
    
    if maxsat_success:
        print("\n🎉 K-means精英选择机制成功集成到Bounce算法中！")
        print("   可以进行完整的实验运行")
    else:
        print("\n⚠️  存在问题，需要进一步调试")