#!/usr/bin/env python3
"""
验证TabPFN修复效果的简单测试
"""

import sys
import os
import torch
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_tabpfn_fix():
    """测试TabPFN修复效果"""
    print("🔍 验证TabPFN修复效果...")
    
    try:
        from bounce.benchmarks import MaxSat60
        from bounce.projection import AxUS
        from bounce.ensemble_manager import EnsembleManager
        
        # 创建测试环境
        benchmark = MaxSat60()
        axus = AxUS(parameters=benchmark.parameters, n_bins=4)
        
        ensemble_manager = EnsembleManager(
            benchmark=benchmark,
            axus=axus,
            device='cpu',
            model_types=['gp', 'rbf', 'tabpfn'],
            weight_window_size=10,
            min_weight_protection=0.1,
            top_k_candidates=3
        )
        
        # 创建训练数据
        X_train = torch.zeros(10, benchmark.representation_dim, dtype=torch.float64)
        for i in range(10):
            X_train[i, :30] = torch.rand(30)  # 前30维有变化
            X_train[i, 30:] = i / 10.0  # 后面维度有系统性差异
        
        y_train = torch.linspace(-160, -50, 10, dtype=torch.float64)
        print(f'训练数据: X形状={X_train.shape}, y范围=[{y_train.min():.2f}, {y_train.max():.2f}]')
        
        # 训练集成模型
        ensemble_manager.fit(X_train, y_train)
        print("✅ 集成模型训练完成")
        
        # 测试预测
        X_test = torch.zeros(5, benchmark.representation_dim, dtype=torch.float64)
        for i in range(5):
            X_test[i, :30] = torch.rand(30)
            X_test[i, 30:] = (i + 0.5) / 5.0
        
        predictions_dict = ensemble_manager.predict_all_models(X_test)
        print(f"获得预测: {list(predictions_dict.keys())}")
        
        # 检查TabPFN是否输出了转换后的预测值
        tabpfn_success = False
        if 'tabpfn' in predictions_dict:
            tabpfn_pred = predictions_dict['tabpfn']
            print(f"TabPFN预测范围: [{tabpfn_pred.min():.4f}, {tabpfn_pred.max():.4f}]")
            
            # 如果是负值且数值合理，说明是转换后的预测值
            if tabpfn_pred.min() < -10 and tabpfn_pred.max() < 0:
                print("✅ TabPFN输出转换后的预测值（负函数值）")
                tabpfn_success = True
            else:
                print("❌ TabPFN可能仍输出质量分数")
        else:
            print("⚠️ TabPFN未参与预测（可能训练失败）")
        
        # 测试性能记录和MAE计算
        y_actual = torch.linspace(-120, -60, 5, dtype=torch.float64)
        
        # 记录性能前的权重
        weights_before = ensemble_manager.weight_manager.get_weights()
        print(f"记录前权重: {weights_before}")
        
        # 记录性能
        ensemble_manager.record_performance(predictions_dict, y_actual)
        
        # 记录性能后的权重
        weights_after = ensemble_manager.weight_manager.get_weights()
        print(f"记录后权重: {weights_after}")
        
        # 检查TabPFN的MAE
        mae_success = False
        for model_name in ensemble_manager.model_types:
            mae = ensemble_manager.weight_manager.calculate_mae(model_name)
            history_count = len(ensemble_manager.weight_manager.performance_history[model_name])
            print(f"{model_name.upper()} MAE: {mae:.4f}, 历史数据: {history_count}条")
            
            if model_name == 'tabpfn' and history_count > 0:
                if mae != 0.5000:  # 不再是默认的0.5000
                    print(f"✅ TabPFN的MAE不再固定为0.5000")
                    mae_success = True
                else:
                    print(f"❌ TabPFN的MAE仍然是0.5000")
        
        if tabpfn_success and mae_success:
            print("🎉 TabPFN修复验证通过！")
            return True
        else:
            print("⚠️ 修复部分成功，可能需要进一步调试")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tabpfn_fix()
    if success:
        print("\n✅ 所有修复验证通过")
    else:
        print("\n❌ 修复验证失败")