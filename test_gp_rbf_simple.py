#!/usr/bin/env python3
"""
简单测试GP和RBF模型预测问题
"""

import logging
import torch
import numpy as np

# 设置DEBUG日志级别
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_gp_rbf_prediction():
    """测试GP和RBF模型的预测行为"""
    print("🔍 测试GP和RBF模型预测行为")
    
    from bounce.benchmarks import MaxSat60
    from bounce.projection import AxUS
    from bounce.gp_surrogate import GPGlobalSurrogate
    from bounce.rbf_surrogate import RBFGlobalSurrogate
    
    # 创建基准函数和投影
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    print(f"问题维度: {benchmark.dim}")
    print(f"目标维度: {axus.target_dim}")
    print(f"表示维度: {benchmark.representation_dim}")
    
    # 生成训练数据（高维空间）
    n_train = 10
    X_train_high = torch.rand(n_train, benchmark.representation_dim, dtype=torch.float64)
    y_train = torch.rand(n_train, dtype=torch.float64) * 100 - 50  # 范围[-50, 50]
    
    print(f"训练数据形状: {X_train_high.shape}")
    print(f"目标值范围: [{y_train.min():.4f}, {y_train.max():.4f}]")
    
    # 生成测试数据（候选点）
    n_test = 3
    candidates_low = torch.rand(n_test, axus.target_dim, dtype=torch.float64) * 2 - 1  # [-1, 1]
    candidates_high = torch.stack([axus.project_up(c.unsqueeze(0).T).T.squeeze(0) for c in candidates_low])
    
    print(f"低维候选点: {candidates_low}")
    print(f"高维候选点形状: {candidates_high.shape}")
    
    # 测试GP模型
    print("\n🧠 测试GP模型...")
    try:
        gp_model = GPGlobalSurrogate(benchmark, axus, device='cpu')
        gp_model.fit(X_train_high, y_train)
        gp_predictions = gp_model.predict(candidates_high)
        
        print(f"GP预测结果: {gp_predictions}")
        print(f"GP预测范围: [{gp_predictions.min():.6f}, {gp_predictions.max():.6f}]")
        print(f"GP预测是否相同: {torch.allclose(gp_predictions, gp_predictions[0], atol=1e-6)}")
        
    except Exception as e:
        print(f"GP模型测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试RBF模型
    print("\n🔄 测试RBF模型...")
    try:
        rbf_model = RBFGlobalSurrogate(benchmark, axus, device='cpu')
        rbf_model.fit(X_train_high, y_train)
        rbf_predictions = rbf_model.predict(candidates_high)
        
        print(f"RBF预测结果: {rbf_predictions}")
        print(f"RBF预测范围: [{rbf_predictions.min():.6f}, {rbf_predictions.max():.6f}]")
        print(f"RBF预测是否相同: {torch.allclose(rbf_predictions, rbf_predictions[0], atol=1e-6)}")
        
    except Exception as e:
        print(f"RBF模型测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gp_rbf_prediction()