#!/usr/bin/env python3
"""
测试 torch.isin 设备一致性修复
"""

import torch
import logging
import sys
import os

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_torch_isin_device_fix():
    """测试 torch.isin 设备一致性修复"""
    print("🔧 测试 torch.isin 设备一致性修复...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"测试设备: {device}")
    
    # 模拟实际场景
    axus_target_dim = 5  # 模拟 axus.target_dim
    
    # indices_to_optimize 在 CUDA 上（从 bounce.py 传递）
    indices_to_optimize = torch.tensor([0, 1, 2], device=device, dtype=torch.long)
    
    print(f"indices_to_optimize设备: {indices_to_optimize.device}")
    
    # 测试原有方法（会失败）
    try:
        if device != 'cpu':
            # 这个会失败（如果在不同设备上）
            full_range_cpu = torch.arange(axus_target_dim)  # 默认在CPU
            result = torch.isin(full_range_cpu, indices_to_optimize)
            print("❌ 原有方法应该失败但没有失败")
            return False
    except RuntimeError as e:
        if "Expected all tensors to be on the same device" in str(e):
            print("✅ 原有方法确实在不同设备上失败（符合预期）")
        else:
            print(f"❌ 意外的错误: {e}")
            return False
    
    # 测试修复后的方法
    try:
        # 修复：确保所有张量在同一设备上
        target_device = indices_to_optimize.device
        full_range = torch.arange(axus_target_dim, device=target_device)
        
        print(f"修复后 full_range设备: {full_range.device}")
        
        # 这个应该成功
        mask = torch.isin(full_range, indices_to_optimize)
        indices_not_to_optimize = full_range[~mask]
        
        print(f"✅ 修复后的方法成功")
        print(f"   indices_to_optimize: {indices_to_optimize}")
        print(f"   indices_not_to_optimize: {indices_not_to_optimize}")
        print(f"   mask: {mask}")
        
        # 验证结果正确性
        expected_not_optimize = torch.tensor([3, 4], device=target_device)
        is_correct = torch.equal(indices_not_to_optimize, expected_not_optimize)
        
        print(f"   结果正确性: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 修复后的方法失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_mixed_variable_scenario():
    """测试混合变量场景"""
    print("\n🔧 测试混合变量场景...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 模拟 Ackley53 问题：3个连续变量 + 50个二分变量
    total_dim = 5  # 简化测试
    continuous_indices = torch.tensor([0, 1, 2], device=device, dtype=torch.long)  # 前3个是连续变量
    
    print(f"连续变量索引: {continuous_indices}")
    print(f"连续变量索引设备: {continuous_indices.device}")
    
    try:
        # 使用修复后的逻辑
        target_device = continuous_indices.device
        full_range = torch.arange(total_dim, device=target_device)
        
        # 计算非连续变量索引（即离散变量索引）
        discrete_mask = ~torch.isin(full_range, continuous_indices)
        discrete_indices = full_range[discrete_mask]
        
        print(f"✅ 混合变量测试成功")
        print(f"   全部索引: {full_range}")
        print(f"   连续变量索引: {continuous_indices}")
        print(f"   离散变量索引: {discrete_indices}")
        print(f"   所有张量设备一致: {all(t.device == target_device for t in [full_range, continuous_indices, discrete_indices])}")
        
        # 验证结果
        expected_discrete = torch.tensor([3, 4], device=target_device)
        is_correct = torch.equal(discrete_indices, expected_discrete)
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 混合变量测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 torch.isin 设备一致性修复验证")
    print("=" * 60)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    if not cuda_available:
        print("⚠️ CUDA不可用，测试将在CPU上运行（部分测试可能跳过）")
    
    # 执行测试
    test_results = []
    
    # 1. 测试 torch.isin 修复
    test_results.append(test_torch_isin_device_fix())
    
    # 2. 测试混合变量场景
    test_results.append(test_mixed_variable_scenario())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有 torch.isin 设备一致性修复测试通过！")
        print("💡 现在 Ackley53 问题应该可以正常运行了。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
    sys.exit(0 if success else 1)