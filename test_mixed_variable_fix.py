#!/usr/bin/env python3
"""
测试混合变量问题的修复效果
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ackley53_tabpfn_prediction():
    """测试Ackley53问题的TabPFN预测是否正确处理混合变量"""
    
    print("🧪 测试Ackley53混合变量TabPFN预测")
    print("=" * 50)
    
    from bounce.benchmarks import Ackley53
    from bounce.projection import AxUS
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    from bounce.util.benchmark import ParameterType
    
    # 创建Ackley53基准（50维二进制 + 3维连续）
    benchmark = Ackley53()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    print(f"基准函数: Ackley53")
    print(f"总维度: {len(benchmark.parameters)}")
    print(f"二进制维度: 50 (x_0 到 x_49)")
    print(f"连续维度: 3 (x_50 到 x_52)")
    print(f"AxUS目标维度: {axus.target_dim}")
    
    # 验证变量类型分布
    binary_indices = []
    continuous_indices = []
    
    for _, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
        binary_indices.extend(indices)
    
    for _, indices in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
        continuous_indices.extend(indices)
    
    print(f"二进制变量索引数量: {len(binary_indices)}")
    print(f"连续变量索引数量: {len(continuous_indices)}")
    
    # 创建GA-TabPFN集成
    ga_config = GAConfig(population_size=20, max_generations=3)
    integration = GATabPFNIntegration(axus, benchmark, ga_config)
    
    # 创建一些训练数据
    train_x = torch.rand(10, benchmark.representation_dim, dtype=torch.float64)
    train_y = benchmark(train_x)
    
    try:
        # 测试候选点生成
        print(f"\n🔍 测试候选点生成")
        candidates = integration._generate_candidate_centers(n_candidates=5)
        
        print(f"生成了 {len(candidates)} 个候选中心")
        for i, candidate in enumerate(candidates):
            print(f"候选{i+1}: {candidate}")
            
            # 验证二进制部分是否为{-1, 1}
            binary_indices_tensor = torch.tensor(binary_indices, dtype=torch.long)
            continuous_indices_tensor = torch.tensor(continuous_indices, dtype=torch.long)

            binary_part = candidate[binary_indices_tensor]
            is_binary_valid = torch.all((binary_part == -1) | (binary_part == 1))

            # 验证连续部分是否在[-1, 1]范围内
            continuous_part = candidate[continuous_indices_tensor]
            is_continuous_valid = torch.all((continuous_part >= -1) & (continuous_part <= 1))
            
            print(f"  二进制部分有效: {is_binary_valid} (值: {binary_part})")
            print(f"  连续部分有效: {is_continuous_valid} (值: {continuous_part})")
            
            if not is_binary_valid:
                print(f"  ❌ 二进制部分包含非{-1,1}值!")
                return False
            
            if not is_continuous_valid:
                print(f"  ❌ 连续部分超出[-1,1]范围!")
                return False
        
        # 测试TabPFN预测
        print(f"\n🔍 测试TabPFN预测")
        predicted_center = integration.predict_best_center_with_tabpfn(
            existing_X=train_x,
            existing_y=train_y,
            n_candidates=10
        )
        
        print(f"TabPFN预测的中心: {predicted_center}")
        
        # 验证预测结果
        binary_indices_tensor = torch.tensor(binary_indices, dtype=torch.long)
        continuous_indices_tensor = torch.tensor(continuous_indices, dtype=torch.long)

        binary_part = predicted_center[binary_indices_tensor]
        continuous_part = predicted_center[continuous_indices_tensor]
        
        is_binary_valid = torch.all((binary_part == -1) | (binary_part == 1))
        is_continuous_valid = torch.all((continuous_part >= -1) & (continuous_part <= 1))
        
        print(f"预测中心二进制部分: {binary_part}")
        print(f"预测中心连续部分: {continuous_part}")
        print(f"二进制部分有效: {is_binary_valid}")
        print(f"连续部分有效: {is_continuous_valid}")
        
        if is_binary_valid and is_continuous_valid:
            print("✅ TabPFN预测正确处理了混合变量")
            return True
        else:
            print("❌ TabPFN预测未正确处理混合变量")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_labs_tabpfn_local():
    """测试Labs问题的TabPFN局部搜索"""
    
    print("\n" + "=" * 50)
    print("🧪 测试Labs问题TabPFN局部搜索")
    print("=" * 50)
    
    from bounce.benchmarks import Labs
    from bounce.projection import AxUS
    from bounce.candidates import create_candidates_discrete
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    from bounce.trust_region import TrustRegion
    
    # 创建Labs基准（纯二进制问题）
    benchmark = Labs(dim=20)  # 使用较小维度便于测试
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    print(f"基准函数: Labs")
    print(f"维度: {len(benchmark.parameters)} (全部二进制)")
    print(f"AxUS目标维度: {axus.target_dim}")
    
    # 创建GA-TabPFN集成
    ga_config = GAConfig(population_size=15, max_generations=2)
    integration = GATabPFNIntegration(axus, benchmark, ga_config)
    
    # 创建一些训练数据
    train_x = torch.randint(0, 2, (8, benchmark.representation_dim), dtype=torch.float64)
    train_y = benchmark(train_x)
    
    # 训练TabPFN
    integration.global_surrogate.fit(train_x, train_y)
    
    # 创建信任域
    trust_region = TrustRegion(dimensionality=axus.target_dim)
    trust_region.length_discrete = 5
    trust_region.length_continuous = 0.1
    
    # 模拟已有的评估点（低维空间，[-1,1]范围）
    x_scaled = torch.randint(0, 2, (5, axus.target_dim), dtype=torch.float64) * 2 - 1
    fx_scaled = torch.rand(5, dtype=torch.float64)
    
    try:
        print(f"\n🔍 测试传统GP局部搜索")
        # 测试传统GP局部搜索
        from bounce.gaussian_process import get_gp
        model = get_gp(x_scaled, fx_scaled, axus)
        
        x_best_gp, fx_best_gp, tr_state_gp = create_candidates_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=None,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device='cpu',
            batch_size=1,
            use_tabpfn_local=False,  # 使用传统GP
            tabpfn_surrogate=None,
        )
        
        print(f"GP局部搜索结果: {x_best_gp}")
        
        print(f"\n🔍 测试TabPFN局部搜索")
        # 测试TabPFN局部搜索
        x_best_tabpfn, fx_best_tabpfn, tr_state_tabpfn = create_candidates_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=None,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device='cpu',
            batch_size=1,
            use_tabpfn_local=True,  # 使用TabPFN
            tabpfn_surrogate=integration.global_surrogate,
        )
        
        print(f"TabPFN局部搜索结果: {x_best_tabpfn}")
        
        # 验证结果都是有效的二进制值
        is_gp_valid = torch.all((x_best_gp == -1) | (x_best_gp == 1))
        is_tabpfn_valid = torch.all((x_best_tabpfn == -1) | (x_best_tabpfn == 1))
        
        print(f"GP结果有效: {is_gp_valid}")
        print(f"TabPFN结果有效: {is_tabpfn_valid}")
        
        if is_gp_valid and is_tabpfn_valid:
            print("✅ 两种局部搜索方法都产生了有效结果")
            return True
        else:
            print("❌ 局部搜索结果无效")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始测试混合变量修复效果")
    print("=" * 60)
    
    try:
        # 测试Ackley53混合变量问题
        ackley53_ok = test_ackley53_tabpfn_prediction()
        
        # 测试Labs纯二进制问题的TabPFN局部搜索
        labs_ok = test_labs_tabpfn_local()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   Ackley53混合变量修复: {'✅ 成功' if ackley53_ok else '❌ 失败'}")
        print(f"   Labs TabPFN局部搜索: {'✅ 成功' if labs_ok else '❌ 失败'}")
        
        if ackley53_ok and labs_ok:
            print("\n🎉 所有测试通过！")
            print("\n📝 修复总结:")
            print("1. ✅ 混合变量TabPFN预测现在正确处理二进制和连续变量")
            print("2. ✅ TabPFN局部搜索功能已实现并测试通过")
            print("3. ✅ 二进制变量严格保持{-1,1}值")
            print("4. ✅ 连续变量保持[-1,1]范围内的连续值")
        else:
            print("\n📝 部分测试未通过，需要进一步调试")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
