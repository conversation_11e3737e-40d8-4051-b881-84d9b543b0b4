models = [gp, rbf, tabpfn]
K_each = 5
alpha = 5.0          # softmax 温度，越大越“贪心”
W = 50               # 验证滑动窗大小
delta = 1e-3         # 去重/距离阈值（混合变量可用混合距离：欧氏+Hamming）

# 1) 收集候选与分数
cand_scores = {}  # x -> {model_name: normalized_score}
for m in models:
    cand_m = m.topk_candidates(K_each)  # [(x, raw_score), ...]
    # min-max 归一化
    vals = [s for _, s in cand_m]
    s_min, s_max = min(vals), max(vals)
    for x, s in cand_m:
        norm = 0.5 if s_max==s_min else (s - s_min) / (s_max - s_min)
        cand_scores.setdefault(x, {})[m.name] = norm

# 2) 计算全局权重（示例：近期验证表现加权）
perf = {}  # m.name -> err over last W evaluated points
for m in models:
    perf[m.name] = rolling_mae(m, window=W)  # 你已有历史数据即可算
# 温度超参 beta
beta = 5.0
raw_w = {name: math.exp(-beta*err) for name, err in perf.items()}
Z = sum(raw_w.values()); weights = {name: w/Z for name, w in raw_w.items()}

# 3) 融合打分
S = {}
for x, score_dict in cand_scores.items():
    S[x] = sum(weights.get(name, 0.0) * score_dict.get(name, 0.0) for name in score_dict.keys())

# 4) 取 top-K_final + softmax 采样
K_final = 5
top = sorted(S.items(), key=lambda kv: kv[1], reverse=True)[:K_final]
vals = [v for _, v in top]
ps = softmax([alpha*v for v in vals])  # 归一化
x_candidates = [x for x, _ in top]
x_center = np_random_choice(x_candidates, p=ps)

# 5) 去重/距近修正
if dist(x_center, last_center) < delta:
    # 选下一个不同的；或增大惩罚再重采样
    for x in x_candidates:
        if dist(x, last_center) >= delta:
            x_center = x; break

return x_center
