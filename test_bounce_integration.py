#!/usr/bin/env python3
"""测试Bounce与GA-TabPFN集成"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import gin
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(level=logging.INFO)

# 配置gin
gin.clear_config()


def test_bounce_with_ga_integration():
    """测试Bounce与GA集成的基本功能"""
    print("🚀 测试Bounce与GA-TabPFN集成...")
    
    # 模拟TabPFN以避免网络调用
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置mock
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_instance.predict_proba.return_value = [[0.8, 0.2], [0.3, 0.7]]
        mock_classifier.return_value = mock_instance
        
        # 导入Bounce类
        from bounce.bounce import <PERSON>unce
        from bounce.benchmarks import MaxSat60
        
        # 创建一个简单的配置
        gin.parse_config([
            "Bounce.number_initial_points = 3",
            "Bounce.initial_target_dimensionality = 3", 
            "Bounce.number_new_bins_on_split = 1",
            "Bounce.maximum_number_evaluations = 15",
            "Bounce.batch_size = 1",
            "Bounce.results_dir = 'test_results'",
            "Bounce.device = 'cpu'",
            "Bounce.dtype = 'float64'",
            "Bounce.use_scipy_lbfgs = False",
            "Bounce.maximum_number_evaluations_until_input_dim = 10"
        ])
        
        # 创建基准函数
        benchmark = MaxSat60()
        
        # 创建Bounce实例
        bounce = Bounce(benchmark=benchmark)
        
        print(f"基准函数维度: {benchmark.dim}")
        print(f"初始目标维度: {bounce.initial_target_dimensionality}")
        print(f"GA-TabPFN集成已初始化: {bounce.ga_tabpfn_integration is not None}")
        
        # 测试初始采样
        bounce.sample_init()
        print(f"初始采样完成，评估次数: {bounce._n_evals}")
        print(f"初始数据形状: {bounce.x_tr.shape}, {bounce.fx_tr.shape}")
        
        # 运行几次迭代来测试集成
        initial_evals = bounce._n_evals
        max_test_evals = min(10, bounce.maximum_number_evaluations)
        
        iteration = 0
        while bounce._n_evals < max_test_evals and iteration < 3:
            iteration += 1
            print(f"\n--- 迭代 {iteration} ---")
            
            # 模拟主循环的一次迭代
            axus = bounce.random_embedding
            x = bounce.x_tr
            fx = bounce.fx_tr
            
            print(f"当前评估次数: {bounce._n_evals}")
            print(f"当前数据点数: {len(x)}")
            print(f"当前最佳值: {fx.min().item():.6f}")
            
            # 检查是否会触发全局搜索
            should_trigger_global = (
                bounce._n_evals - bounce.last_global_search >= bounce.global_search_interval and
                len(bounce.x_up_global) >= 10
            )
            print(f"是否触发全局搜索: {should_trigger_global}")
            
            # 手动触发一次全局搜索测试
            if len(bounce.x_up_global) >= 5:  # 降低阈值用于测试
                try:
                    print("🧬 手动测试全局搜索...")
                    predicted_center = bounce.ga_tabpfn_integration.predict_best_center_with_tabpfn(
                        existing_X=bounce.x_up_global,
                        existing_y=bounce.fx_global
                    )
                    print(f"预测中心形状: {predicted_center.shape}")
                    print(f"预测中心前3个值: {predicted_center[:3]}")
                except Exception as e:
                    print(f"全局搜索测试失败: {e}")
            
            # 简单地添加一个随机点来继续测试
            random_point_low = torch.rand(axus.target_dim, dtype=torch.float64) * 2 - 1
            from bounce.util.data_handling import from_1_around_origin
            random_point_high = from_1_around_origin(
                axus.project_up(((random_point_low + 1) / 2).unsqueeze(0).T).T,
                lb=benchmark.lb_vec, 
                ub=benchmark.ub_vec
            ).squeeze(0)
            
            random_fx = benchmark(random_point_high.unsqueeze(0))
            
            bounce._add_data_to_tr_observations(
                xs_down=random_point_low.unsqueeze(0),
                xs_up=random_point_high.unsqueeze(0),
                fxs=random_fx
            )
            bounce._n_evals += 1
            
            print(f"添加随机点，新的最佳值: {bounce.fx_tr.min().item():.6f}")
        
        print(f"\n✅ 集成测试完成")
        print(f"总评估次数: {bounce._n_evals}")
        print(f"最终最佳值: {bounce.fx_tr.min().item():.6f}")
        print(f"GA-TabPFN集成信息: {bounce.ga_tabpfn_integration.get_integration_info()}")


def test_ga_tabpfn_standalone():
    """测试GA-TabPFN独立功能"""
    print("\n🧬 测试GA-TabPFN独立功能...")
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置mock
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_instance.predict_proba.return_value = [[0.9, 0.1], [0.2, 0.8], [0.5, 0.5]]
        mock_classifier.return_value = mock_instance
        
        from bounce.ga_tabpfn_integration import GATabPFNIntegration
        from bounce.genetic_algorithm import GAConfig
        from bounce.projection import AxUS
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                    Parameter(name="x3", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                    Parameter(name="x4", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 4
                self.representation_dim = 4
                self.lb_vec = torch.zeros(4, dtype=torch.float64)
                self.ub_vec = torch.ones(4, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        axus = AxUS(parameters=benchmark.parameters, n_bins=4)
        
        config = GAConfig(population_size=8, max_generations=3)
        integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=config,
            ga_generations=3
        )
        
        # 测试全局搜索
        print("运行全局搜索...")
        best_low_dim, best_high_dim = integration.run_global_search()
        print(f"最佳低维解: {best_low_dim}")
        print(f"最佳高维解: {best_high_dim}")
        
        # 测试TabPFN预测
        print("测试TabPFN预测...")
        existing_X = torch.rand(12, 4, dtype=torch.float64)
        existing_y = torch.rand(12, dtype=torch.float64)
        
        predicted_center = integration.predict_best_center_with_tabpfn(
            existing_X, existing_y, n_candidates=5
        )
        print(f"预测的中心点: {predicted_center}")
        
        print("✅ GA-TabPFN独立测试通过")


if __name__ == "__main__":
    try:
        test_ga_tabpfn_standalone()
        test_bounce_with_ga_integration()
        print("\n🎉 所有集成测试通过！")
    except Exception as e:
        print(f"\n❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        gin.clear_config()
