#!/usr/bin/env python3
"""
测试修复效果的简化脚本
"""

import sys
import os
import torch
import numpy as np
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_gp_tensor_fix():
    """测试GP预测的0维张量修复"""
    print("🔍 测试GP模型0维张量修复...")
    
    try:
        from bounce.benchmarks import MaxSat60
        from bounce.projection import AxUS
        from bounce.gp_surrogate import GPGlobalSurrogate
        
        # 创建小规模测试
        benchmark = MaxSat60()
        axus = AxUS(parameters=benchmark.parameters, n_bins=4)
        
        # 训练数据
        X_train = torch.rand(5, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(5, dtype=torch.float64) * 100 - 50
        
        # 测试数据（单个样本）
        X_test_single = torch.rand(1, benchmark.representation_dim, dtype=torch.float64)
        X_test_multi = torch.rand(3, benchmark.representation_dim, dtype=torch.float64)
        
        # 创建并训练GP模型
        gp_model = GPGlobalSurrogate(benchmark, axus, device='cpu')
        gp_model.fit(X_train, y_train)
        
        # 测试单样本预测（之前会出错）
        pred_single = gp_model.predict(X_test_single)
        print(f"   单样本预测: {pred_single}, 形状: {pred_single.shape}")
        assert pred_single.shape == (1,), f"单样本预测形状错误: {pred_single.shape}"
        
        # 测试多样本预测
        pred_multi = gp_model.predict(X_test_multi)
        print(f"   多样本预测: {pred_multi}, 形状: {pred_multi.shape}")
        assert pred_multi.shape == (3,), f"多样本预测形状错误: {pred_multi.shape}"
        
        print("   ✅ GP模型张量修复测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ GP模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ensemble_performance_recording():
    """测试集成模型性能记录"""
    print("🔍 测试集成模型性能记录...")
    
    try:
        from bounce.ensemble_manager import EnsembleManager
        from bounce.benchmarks import MaxSat60
        from bounce.projection import AxUS
        
        # 创建测试环境
        benchmark = MaxSat60()
        axus = AxUS(parameters=benchmark.parameters, n_bins=4)
        
        ensemble_manager = EnsembleManager(
            benchmark=benchmark,
            axus=axus,
            device='cpu',
            model_types=['gp', 'rbf', 'tabpfn'],
            weight_window_size=10,
            min_weight_protection=0.1,
            top_k_candidates=3
        )
        
        # 训练数据
        X_train = torch.rand(10, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(10, dtype=torch.float64) * 100 - 50
        
        # 训练集成模型
        ensemble_manager.fit(X_train, y_train)
        
        # 预测测试
        X_test = torch.rand(5, benchmark.representation_dim, dtype=torch.float64)
        predictions_dict = ensemble_manager.predict_all_models(X_test)
        
        print(f"   获得预测结果: {list(predictions_dict.keys())}")
        
        # 模拟真实评估值
        y_actual = torch.rand(5, dtype=torch.float64) * 100 - 50
        
        # 记录性能（测试修复的MAE计算）
        print("   测试性能记录...")
        ensemble_manager.record_performance(predictions_dict, y_actual)
        
        # 检查权重更新
        weights = ensemble_manager.weight_manager.get_weights()
        print(f"   权重更新后: {weights}")
        
        # 检查各模型的性能历史
        for model_name in ensemble_manager.model_types:
            history = ensemble_manager.weight_manager.performance_history[model_name]
            print(f"   {model_name.upper()} 性能历史数据: {len(history)}条")
        
        print("   ✅ 集成模型性能记录测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 集成模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tabpfn_sampling():
    """测试TabPFN采样方法修复"""
    print("🔍 测试TabPFN采样方法...")
    
    try:
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        from bounce.benchmarks import MaxSat60
        
        benchmark = MaxSat60()
        tabpfn_model = TabPFNGlobalSurrogate(benchmark, n_bins=5, device='cpu')
        
        # 测试采样方法
        X_large = torch.rand(100, benchmark.representation_dim, dtype=torch.float64)
        y_large = torch.rand(100, dtype=torch.float64) * 100 - 50
        
        X_sampled, y_sampled = tabpfn_model._sample_training_data(X_large, y_large, max_samples=50)
        
        print(f"   原始数据: {X_large.shape[0]}样本")
        print(f"   采样后: {X_sampled.shape[0]}样本")
        assert X_sampled.shape[0] <= 50, "采样数量超出限制"
        assert X_sampled.shape[0] == y_sampled.shape[0], "X和y样本数不匹配"
        
        print("   ✅ TabPFN采样方法测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ TabPFN采样测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("🚀 开始验证修复效果...\n")
    
    tests = [
        test_gp_tensor_fix,
        test_ensemble_performance_recording,
        test_tabpfn_sampling,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试 {test.__name__} 异常: {e}")
            results.append(False)
        print()
    
    # 汇总结果
    print("=" * 50)
    print("🎯 测试结果汇总:")
    passed = sum(results)
    total = len(results)
    
    print(f"   通过: {passed}/{total}")
    if passed == total:
        print("   🎉 所有修复验证通过！")
    else:
        print(f"   ⚠️ 有 {total - passed} 个测试失败")
    
    return passed == total

if __name__ == "__main__":
    main()