#!/usr/bin/env python3
"""
简化的K-means实现验证（不依赖torch等复杂库）
"""

import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

def test_kmeans_logic():
    """测试K-means聚类逻辑"""
    print("🧪 测试K-means聚类逻辑（简化版）")
    
    # 模拟GA种群数据
    np.random.seed(42)
    n_individuals = 50
    n_dimensions = 10
    
    # 生成3个聚类的数据
    cluster1 = np.random.normal([1, 1, 1, 1, 1, -1, -1, -1, -1, -1], 0.2, (17, n_dimensions))
    cluster2 = np.random.normal([-1, -1, -1, 1, 1, 1, 1, -1, -1, -1], 0.2, (16, n_dimensions))
    cluster3 = np.random.normal([0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 0.3, (17, n_dimensions))
    
    population_genes = np.vstack([cluster1, cluster2, cluster3])
    fitness_scores = [np.linalg.norm(genes) for genes in population_genes]
    
    print(f"   ✅ 生成{len(population_genes)}个个体，每个{n_dimensions}维")
    
    # K-means聚类参数
    elite_count = 20
    elite_clusters_ratio = 0.5
    n_clusters = max(2, int(elite_count * elite_clusters_ratio))
    
    # 确保聚类数合理
    if n_clusters * 2 > elite_count:
        n_clusters = elite_count // 2
    n_clusters = min(n_clusters, len(population_genes) // 2)
    
    print(f"   📊 聚类参数: 精英数量={elite_count}, 聚类比例={elite_clusters_ratio}")
    print(f"   🎯 计算聚类数量: {n_clusters}")
    
    if n_clusters < 2:
        print("   ⚠️  聚类数太少，使用传统选择")
        return False
    
    # 数据标准化
    scaler = StandardScaler()
    genes_scaled = scaler.fit_transform(population_genes)
    
    # 执行K-means聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(genes_scaled)
    
    print(f"   🔍 聚类结果: {np.bincount(cluster_labels)}")
    
    # 从每个聚类选择精英
    selected_elites = []
    
    for cluster_id in range(n_clusters):
        cluster_indices = np.where(cluster_labels == cluster_id)[0]
        
        if len(cluster_indices) == 0:
            continue
            
        # 按适应度排序
        cluster_fitness = [(i, fitness_scores[i]) for i in cluster_indices]
        cluster_fitness.sort(key=lambda x: x[1])
        
        # 每个聚类选择最优的2个
        elites_per_cluster = min(2, len(cluster_fitness))
        for j in range(elites_per_cluster):
            selected_elites.append(cluster_fitness[j])
            
        print(f"     聚类{cluster_id}: {len(cluster_indices)}个体 → 选择{elites_per_cluster}个精英")
    
    print(f"   ✅ K-means选择: 总共{len(selected_elites)}个精英")
    
    # 计算多样性
    elite_indices = [elite[0] for elite in selected_elites]
    elite_genes = population_genes[elite_indices]
    
    # K-means精英的多样性
    kmeans_diversity = calculate_diversity(elite_genes)
    
    # 传统精英选择的多样性
    sorted_indices = sorted(range(len(fitness_scores)), key=lambda i: fitness_scores[i])
    traditional_elites = population_genes[sorted_indices[:len(selected_elites)]]
    traditional_diversity = calculate_diversity(traditional_elites)
    
    print(f"   📈 K-means精英多样性: {kmeans_diversity:.4f}")
    print(f"   📉 传统精英多样性: {traditional_diversity:.4f}")
    
    improvement = ((kmeans_diversity - traditional_diversity) / traditional_diversity * 100) if traditional_diversity > 0 else 0
    print(f"   🚀 多样性提升: {improvement:.1f}%")
    
    return kmeans_diversity > traditional_diversity

def calculate_diversity(individuals):
    """计算个体间的平均距离（多样性指标）"""
    if len(individuals) < 2:
        return 0.0
        
    total_distance = 0.0
    count = 0
    
    for i in range(len(individuals)):
        for j in range(i + 1, len(individuals)):
            distance = np.linalg.norm(individuals[i] - individuals[j])
            total_distance += distance
            count += 1
    
    return total_distance / count if count > 0 else 0.0

def main():
    """主函数"""
    print("🚀 K-means聚类精英选择 - 简化验证")
    print("="*50)
    
    try:
        success = test_kmeans_logic()
        
        print("\\n📋 验证结果:")
        if success:
            print("   ✅ K-means聚类逻辑验证通过！")
            print("   ✅ 多样性确实得到提升")
            print("\\n🎉 实现正确！可以进行完整实验")
        else:
            print("   ❌ 验证失败，需要检查实现")
            
        return success
        
    except Exception as e:
        print(f"   ❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)