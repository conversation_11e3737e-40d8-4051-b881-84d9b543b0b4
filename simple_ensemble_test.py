#!/usr/bin/env python3
"""
集成代理模型简单验证脚本
验证集成模式是否正常工作
"""

print("🚀 开始集成代理模型验证...")

try:
    # 测试导入
    from bounce.ensemble_manager import EnsembleManager, WeightManager, CandidateSelector
    print("✅ EnsembleManager导入成功")
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    print("✅ GATabPFNIntegration导入成功")
    
    # 测试基本初始化
    import torch
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建简单的基准函数
    class TestBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name="x0", type=ParameterType.BINARY),
                Parameter(name="x1", type=ParameterType.BINARY),
            ]
            self.dim = 2
            self.representation_dim = 2
            self.lb_vec = torch.zeros(2, dtype=torch.float64)
            self.ub_vec = torch.ones(2, dtype=torch.float64)
        
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = TestBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=2)
    
    # 测试集成模式
    print("\n🧪 测试集成模式初始化...")
    ga_integration = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        device='cpu',
        enable_ensemble=True
    )
    
    assert ga_integration.enable_ensemble == True
    assert ga_integration.ensemble_manager is not None
    print(f"✅ 集成模式初始化成功，支持模型: {ga_integration.ensemble_manager.model_types}")
    
    # 测试单一模式
    print("\n🧪 测试单一模式初始化...")
    ga_single = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        device='cpu',
        enable_ensemble=False,
        global_model_type='gp'
    )
    
    assert ga_single.enable_ensemble == False
    assert ga_single.global_surrogate is not None
    print("✅ 单一模式初始化成功")
    
    print("\n" + "=" * 50)
    print("🎉 集成代理模型验证成功！")
    print("✅ 模块导入正常")
    print("✅ 集成模式工作正常") 
    print("✅ 单一模式工作正常")
    print("✅ 代码结构完整")
    print("=" * 50)
    
except Exception as e:
    print(f"\n❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()