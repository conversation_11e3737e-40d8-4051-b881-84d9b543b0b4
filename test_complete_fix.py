#!/usr/bin/env python3
"""
测试完整的二分问题小数修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_complete_fix():
    """测试完整的修复"""
    
    print("🧪 测试完整的二分问题小数修复")
    print("=" * 50)
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig, Individual
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
    
    # 创建纯二分变量基准
    class BinaryBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
                for i in range(5)
            ]
            self.dim = 5
            axus_temp = AxUS(parameters=self.parameters, n_bins=5)
            self.representation_dim = axus_temp.input_dim
            self.lb_vec = torch.zeros(self.representation_dim, dtype=torch.float64)
            self.ub_vec = torch.ones(self.representation_dim, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = BinaryBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    print(f"基准参数类型: {[p.type for p in benchmark.parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    print(f"AxUS输入维度: {axus.input_dim}")
    
    # 创建GA配置
    ga_config = GAConfig(population_size=10, max_generations=2)
    
    # 创建GA-TabPFN集成
    integration = GATabPFNIntegration(axus, benchmark, ga_config)
    
    print(f"\n🧪 测试1: Individual的高维转换")
    print("=" * 30)
    
    # 测试Individual的高维转换
    test_genes = torch.tensor([-1., 1., -1., 1., -1.], dtype=torch.float64)
    individual = Individual(test_genes, axus)
    
    print(f"低维基因: {individual.genes}")
    
    # 获取高维表示
    high_dim = individual.get_high_dim_genes(benchmark.lb_vec, benchmark.ub_vec)
    print(f"高维基因: {high_dim}")
    
    # 检查是否有小数
    unique_values = torch.unique(high_dim)
    has_decimals = not torch.all(torch.isin(unique_values, torch.tensor([0.0, 1.0])))
    
    print(f"唯一值: {unique_values}")
    print(f"是否包含小数: {has_decimals}")
    
    if not has_decimals:
        print("✅ Individual高维转换正确")
        test1_pass = True
    else:
        print("❌ Individual高维转换仍有小数")
        test1_pass = False
    
    print(f"\n🧪 测试2: 候选中心生成")
    print("=" * 30)
    
    # 测试候选中心生成
    candidates = integration._generate_candidate_centers(n_candidates=5)
    print(f"生成了 {len(candidates)} 个候选中心")
    
    all_discrete = True
    for i, candidate in enumerate(candidates):
        unique_values = torch.unique(candidate)
        is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
        
        print(f"候选中心 {i+1}: {candidate}")
        print(f"  唯一值: {unique_values}")
        print(f"  是否为{-1,1}值: {is_discrete}")
        
        if not is_discrete:
            all_discrete = False
    
    if all_discrete:
        print("✅ 候选中心生成正确")
        test2_pass = True
    else:
        print("❌ 候选中心生成仍有问题")
        test2_pass = False
    
    print(f"\n🧪 测试3: TabPFN预测中心")
    print("=" * 30)
    
    # 测试TabPFN预测中心
    train_x = torch.randint(0, 2, (5, benchmark.representation_dim), dtype=torch.float64)
    train_y = benchmark(train_x)
    
    try:
        predicted_center = integration.predict_best_center_with_tabpfn(train_x, train_y, n_candidates=5)
        
        print(f"TabPFN预测的中心: {predicted_center}")
        
        # 检查离散性
        unique_values = torch.unique(predicted_center)
        is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
        
        print(f"唯一值: {unique_values}")
        print(f"是否为{-1,1}值: {is_discrete}")
        
        if is_discrete:
            print("✅ TabPFN预测中心正确")
            test3_pass = True
        else:
            print("❌ TabPFN预测中心仍有问题")
            test3_pass = False
    except Exception as e:
        print(f"❌ TabPFN预测失败: {e}")
        test3_pass = False
    
    print(f"\n🧪 测试4: project_up函数")
    print("=" * 30)
    
    # 测试project_up函数
    test_input = torch.tensor([[0., 1., 0., 1., 0.]], dtype=torch.float64)
    projected = axus.project_up(test_input)
    
    print(f"输入: {test_input}")
    print(f"project_up输出: {projected}")
    
    # 检查输出
    unique_values = torch.unique(projected)
    is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
    
    print(f"唯一值: {unique_values}")
    print(f"是否为{-1,1}值: {is_discrete}")
    
    if is_discrete:
        print("✅ project_up函数正确")
        test4_pass = True
    else:
        print("❌ project_up函数仍有问题")
        test4_pass = False
    
    # 总结
    print(f"\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   Individual高维转换: {'✅ 通过' if test1_pass else '❌ 失败'}")
    print(f"   候选中心生成: {'✅ 通过' if test2_pass else '❌ 失败'}")
    print(f"   TabPFN预测中心: {'✅ 通过' if test3_pass else '❌ 失败'}")
    print(f"   project_up函数: {'✅ 通过' if test4_pass else '❌ 失败'}")
    
    all_pass = test1_pass and test2_pass and test3_pass and test4_pass
    
    if all_pass:
        print("\n🎉 所有测试通过！")
        print("\n📝 修复总结:")
        print("1. ✅ 修复了Individual的高维转换")
        print("2. ✅ 修复了候选中心生成")
        print("3. ✅ 修复了TabPFN预测中心")
        print("4. ✅ 修复了project_up函数")
        print("5. ✅ 修复了bounce.py中的两处from_1_around_origin使用")
        print("6. ✅ 修复了candidates.py中的中心点选择逻辑")
        print("\n🚨 重要：现在二分问题应该不会再出现小数值！")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    print("🚀 开始测试完整的二分问题小数修复")
    print("=" * 60)
    
    try:
        success = test_complete_fix()
        
        if success:
            print("\n🎉 所有修复验证通过！")
        else:
            print("\n❌ 部分修复验证失败")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
