name: website

# build the documentation whenever there are new commits on main
on:
  push:
    branches:
      - main

# security: restrict permissions for CI jobs.
permissions:
  contents: read

jobs:
  # Build the documentation and upload the static HTML files as an artifact.
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install Poetry
        uses: snok/install-poetry@v1
      - name: Install project
        run: poetry install --no-interaction
      - name: Install pdoc
        run: poetry add pdoc3
      - name: Build documentation with pdoc
        run: poetry run pdoc bounce --html --output-dir docs
      - uses: actions/upload-pages-artifact@v2
        with:
          path: docs/

  # Deploy the artifact to GitHub pages.
  # This is a separate job so that only actions/deploy-pages has the necessary permissions.
  deploy:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      pages: write
      id-token: write
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - id: deployment
        uses: actions/deploy-pages@v2