name: singularity
on:
  push:
    branches:
      - main
  schedule:
    - cron: '0 0 * * *'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
      - uses: eWaterCycle/setup-singularity@v7
        with:
          singularity-version: 3.8.3
      - name: build singularity container
        env:
          SINGULARITYENV_GH_TOKEN: ${{ github.token }}
        run: |
          singularity build --sandbox --fakeroot bounce.sif singularity_container
          if [ $? -ne 0 ]; then
            echo "Singularity build failed."
            exit 1
          fi
          # run pytest in the container
          singularity exec --fakeroot bounce.sif bash -c "PATH='/bs/poetry/bin:$PATH' cd /bs/bounce && PATH='/bs/poetry/bin:$PATH' poetry run python3 -m pytest -v"