#!/usr/bin/env python3
"""
测试数据重复累积问题修复效果
验证GP、RBF、TabPFN三个模型的样本数量一致且正确
"""

import torch
import logging
import sys
import os

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_data_duplication_fix():
    """测试数据重复累积修复效果"""
    print("🔍 测试数据重复累积问题修复效果")
    print("=" * 60)
    
    try:
        from bounce.benchmarks import Ackley53
        from bounce.projection import AxUS
        from bounce.ensemble_manager import EnsembleManager
        
        # 创建Ackley53基准
        benchmark = Ackley53()
        axus = AxUS(parameters=benchmark.parameters, n_bins=5)
        
        print(f"基准函数: Ackley53")
        print(f"总维度: {len(benchmark.parameters)}")
        print(f"表示维度: {benchmark.representation_dim}")
        
        # 创建集成管理器
        ensemble_manager = EnsembleManager(
            benchmark=benchmark,
            axus=axus,
            device='cpu',
            model_types=['gp', 'rbf', 'tabpfn'],
            weight_window_size=10,
            min_weight_protection=0.1,
            top_k_candidates=3
        )
        
        print(f"\n🌟 集成管理器创建成功")
        print(f"   支持模型: {ensemble_manager.model_types}")
        
        # 创建初始训练数据
        print(f"\n📊 第1次训练（初始数据）")
        n_initial = 10
        X_train = torch.rand(n_initial, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(n_initial, dtype=torch.float64) * 100 - 50
        
        print(f"   训练数据: {X_train.shape}, 目标值范围: [{y_train.min():.2f}, {y_train.max():.2f}]")
        
        # 初始训练
        ensemble_manager.fit(X_train, y_train)
        
        # 检查初始训练后的样本数
        initial_samples = {}
        for model_type in ensemble_manager.model_types:
            model = ensemble_manager.models[model_type]
            if model_type == 'gp':
                samples = len(model.train_x) if hasattr(model, 'train_x') and model.train_x is not None else 0
            elif model_type == 'rbf':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            elif model_type == 'tabpfn':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            else:
                samples = 0
            
            initial_samples[model_type] = samples
            print(f"   📈 {model_type.upper()}: {samples}个样本")
        
        # 验证初始样本数一致
        sample_counts = list(initial_samples.values())
        if len(set(sample_counts)) == 1:
            print(f"   ✅ 初始训练样本数一致: {sample_counts[0]}")
        else:
            print(f"   ❌ 初始训练样本数不一致: {initial_samples}")
            return False
        
        # 模拟增量更新（这是关键测试）
        print(f"\n📊 第2次更新（模拟ensemble_manager的update调用）")
        
        # 添加新数据到现有数据
        n_new = 3
        X_new = torch.rand(n_new, benchmark.representation_dim, dtype=torch.float64)
        y_new = torch.rand(n_new, dtype=torch.float64) * 100 - 50
        
        # 合并为完整数据集（这是ensemble_manager的实际调用方式）
        X_total = torch.cat([X_train, X_new], dim=0)
        y_total = torch.cat([y_train, y_new], dim=0)
        
        print(f"   新增数据: {X_new.shape}")
        print(f"   完整数据集: {X_total.shape}")
        print(f"   预期每个模型样本数: {len(X_total)}")
        
        # 调用ensemble_manager的update_models（传入完整数据集）
        ensemble_manager.update_models(X_total, y_total)
        
        # 检查更新后的样本数
        updated_samples = {}
        for model_type in ensemble_manager.model_types:
            model = ensemble_manager.models[model_type]
            if model_type == 'gp':
                samples = len(model.train_x) if hasattr(model, 'train_x') and model.train_x is not None else 0
            elif model_type == 'rbf':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            elif model_type == 'tabpfn':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            else:
                samples = 0
            
            updated_samples[model_type] = samples
            print(f"   📈 {model_type.upper()}: {samples}个样本")
        
        # 验证更新后样本数一致且正确
        expected_samples = len(X_total)
        sample_counts = list(updated_samples.values())
        
        all_correct = all(count == expected_samples for count in sample_counts)
        all_consistent = len(set(sample_counts)) == 1
        
        if all_correct and all_consistent:
            print(f"   ✅ 更新后样本数正确且一致: {sample_counts[0]} (预期: {expected_samples})")
        else:
            print(f"   ❌ 更新后样本数错误: {updated_samples} (预期: {expected_samples})")
            return False
        
        # 再做一次更新测试，确保不会重复累积
        print(f"\n📊 第3次更新（再次测试）")
        
        # 再添加新数据
        n_new2 = 2
        X_new2 = torch.rand(n_new2, benchmark.representation_dim, dtype=torch.float64)
        y_new2 = torch.rand(n_new2, dtype=torch.float64) * 100 - 50
        
        # 合并为新的完整数据集
        X_total2 = torch.cat([X_total, X_new2], dim=0)
        y_total2 = torch.cat([y_total, y_new2], dim=0)
        
        print(f"   再次新增数据: {X_new2.shape}")
        print(f"   最终完整数据集: {X_total2.shape}")
        print(f"   预期每个模型样本数: {len(X_total2)}")
        
        # 再次调用update_models
        ensemble_manager.update_models(X_total2, y_total2)
        
        # 检查最终样本数
        final_samples = {}
        for model_type in ensemble_manager.model_types:
            model = ensemble_manager.models[model_type]
            if model_type == 'gp':
                samples = len(model.train_x) if hasattr(model, 'train_x') and model.train_x is not None else 0
            elif model_type == 'rbf':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            elif model_type == 'tabpfn':
                samples = len(model.X_train) if hasattr(model, 'X_train') and model.X_train is not None else 0
            else:
                samples = 0
            
            final_samples[model_type] = samples
            print(f"   📈 {model_type.upper()}: {samples}个样本")
        
        # 验证最终样本数
        expected_final = len(X_total2)
        final_counts = list(final_samples.values())
        
        all_final_correct = all(count == expected_final for count in final_counts)
        all_final_consistent = len(set(final_counts)) == 1
        
        if all_final_correct and all_final_consistent:
            print(f"   ✅ 最终样本数正确且一致: {final_counts[0]} (预期: {expected_final})")
            return True
        else:
            print(f"   ❌ 最终样本数错误: {final_samples} (预期: {expected_final})")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 数据重复累积问题修复验证")
    print("=" * 80)
    
    success = test_data_duplication_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 数据重复累积问题修复验证通过！")
        print("💡 现在GP、RBF、TabPFN三个模型的样本数量一致且正确。")
        print("🔧 修复已解决数据重复累积问题，模型更新逻辑正确。")
        print("📈 预期在实际运行中不再出现样本数不一致的问题。")
    else:
        print("⚠️ 数据重复累积问题修复验证失败，需要进一步检查。")
    
    print("=" * 80)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)