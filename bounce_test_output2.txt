2025-09-02 13:12:36,967 - INFO - 初始维度3维，算法目标维度3维
2025-09-02 13:12:36,968 - INFO - 🤖 <PERSON><PERSON><PERSON> will split at most 0 times.  每次分裂1个箱子
2025-09-02 13:12:36,968 - INFO - 🤖 初始3维，有15 次评估预算 
2025-09-02 13:12:36,973 - INFO - 每一个可能维度以及评估预算：{3: 15}；以及总预算为15次
2025-09-02 13:12:36,973 - INFO - 🔄 策略切换机制已启用，切换阈值: 3步
2025-09-02 13:12:36,973 - INFO - 🎯 局部搜索将使用TABPFN代理模型
2025-09-02 13:12:36,973 - INFO - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 3, 类别变量: 0
2025-09-02 13:12:36,973 - INFO - 创建TabPFN代理模型，n_bins: 5
2025-09-02 13:12:36,973 - INFO - 使用TABPFN作为全局代理模型
2025-09-02 13:12:36,973 - INFO - GP全局代理模型初始化完成，参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True}
2025-09-02 13:12:36,974 - INFO - 创建GP代理模型，参数: {}
2025-09-02 13:12:36,974 - INFO - ✅ GP模型初始化完成
2025-09-02 13:12:36,974 - INFO - RBF全局代理模型初始化完成，核函数: gaussian, epsilon: 1.0
2025-09-02 13:12:36,974 - INFO - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0
2025-09-02 13:12:36,974 - INFO - ✅ RBF模型初始化完成
2025-09-02 13:12:36,974 - INFO - ✅ TabPFN模型初始化完成
2025-09-02 13:12:36,974 - INFO - 🌟 集成代理模型管理器初始化完成
2025-09-02 13:12:36,975 - INFO -    支持模型: ['gp', 'rbf', 'tabpfn']
2025-09-02 13:12:36,975 - INFO -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333}
2025-09-02 13:12:36,975 - INFO - 🌟 集成代理模型系统已启用
2025-09-02 13:12:36,975 - INFO - GA-全局模型集成系统初始化完成
2025-09-02 13:12:36,984 - INFO - GA维度变化或未初始化，重新初始化GA (目标维度: 3)
2025-09-02 13:12:36,984 - INFO - 动态种群大小: 59 (目标维度: 3)
2025-09-02 13:12:36,987 - INFO - 初始化种群完成，种群大小: 59
2025-09-02 13:12:37,442 - INFO - GP模型训练完成，训练样本数: 5, 输入维度: 3
2025-09-02 13:12:37,442 - INFO - GP模型训练完成
2025-09-02 13:12:37,451 - INFO - RBF模型训练完成，训练样本数: 5
2025-09-02 13:12:37,452 - INFO - RBF模型训练完成
2025-09-02 13:12:37,624 - INFO - TabPFN模型训练完成，训练样本数: 5, 类别数: 2
2025-09-02 13:12:37,624 - INFO - TABPFN模型训练完成
2025-09-02 13:12:37,656 - INFO - GP模型训练完成，训练样本数: 2, 输入维度: 3
2025-09-02 13:12:37,661 - INFO - RBF模型训练完成，训练样本数: 2
2025-09-02 13:12:37,791 - INFO - TabPFN模型训练完成，训练样本数: 2, 类别数: 2
2025-09-02 13:12:39,594 - INFO - 🌟 集成模型选择中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:39,594 - INFO -    当前权重: gp:0.388 | rbf:0.593 | tabpfn:0.020
2025-09-02 13:12:39,595 - INFO - 🌟 集成模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:39,595 - INFO - 🌟 集成代理模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:40,226 - INFO - 使用TABPFN评估了6个候选点
2025-09-02 13:12:41,220 - INFO - 🚀 Iteration 5: No improvement. Best function value 1.000 [全局模型策略]
2025-09-02 13:12:41,704 - INFO - TabPFN评估了38个GA个体
2025-09-02 13:12:41,733 - INFO - GP模型训练完成，训练样本数: 8, 输入维度: 3
2025-09-02 13:12:41,734 - INFO - GP模型更新完成，总样本数: 8
2025-09-02 13:12:41,734 - INFO - GP模型更新完成
2025-09-02 13:12:41,735 - INFO - RBF模型训练完成，训练样本数: 8
2025-09-02 13:12:41,735 - INFO - RBF模型更新完成，总样本数: 8
2025-09-02 13:12:41,735 - INFO - RBF模型更新完成
2025-09-02 13:12:41,803 - INFO - TabPFN模型更新完成，总样本数: 8
2025-09-02 13:12:41,803 - INFO - TABPFN模型更新完成
2025-09-02 13:12:41,856 - INFO - GP模型训练完成，训练样本数: 3, 输入维度: 3
2025-09-02 13:12:41,861 - INFO - RBF模型训练完成，训练样本数: 3
2025-09-02 13:12:41,926 - INFO - TabPFN模型训练完成，训练样本数: 3, 类别数: 3
2025-09-02 13:12:42,974 - INFO - 🌟 集成模型选择中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:12:42,974 - INFO -    当前权重: gp:0.427 | rbf:0.375 | tabpfn:0.198
2025-09-02 13:12:42,975 - INFO - 🌟 集成模型预测的TR中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:12:42,976 - INFO - 🌟 集成代理模型预测的TR中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:12:43,468 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:12:46,009 - INFO - 🚀 Iteration 6: No improvement. Best function value 1.000 [全局模型策略]
2025-09-02 13:12:46,545 - INFO - TabPFN评估了39个GA个体
2025-09-02 13:12:46,577 - INFO - GP模型训练完成，训练样本数: 10, 输入维度: 3
2025-09-02 13:12:46,577 - INFO - GP模型更新完成，总样本数: 10
2025-09-02 13:12:46,577 - INFO - GP模型更新完成
2025-09-02 13:12:46,579 - INFO - RBF模型训练完成，训练样本数: 10
2025-09-02 13:12:46,579 - INFO - RBF模型更新完成，总样本数: 10
2025-09-02 13:12:46,579 - INFO - RBF模型更新完成
2025-09-02 13:12:46,650 - INFO - TabPFN模型更新完成，总样本数: 10
2025-09-02 13:12:46,650 - INFO - TABPFN模型更新完成
2025-09-02 13:12:46,696 - INFO - GP模型训练完成，训练样本数: 3, 输入维度: 3
2025-09-02 13:12:46,702 - INFO - RBF模型训练完成，训练样本数: 3
2025-09-02 13:12:46,757 - INFO - TabPFN模型训练完成，训练样本数: 3, 类别数: 3
2025-09-02 13:12:47,736 - INFO - 🌟 集成模型选择中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:47,736 - INFO -    当前权重: gp:0.528 | rbf:0.339 | tabpfn:0.132
2025-09-02 13:12:47,737 - INFO - 🌟 集成模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:47,738 - INFO - 🌟 集成代理模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:48,188 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:12:50,226 - INFO - ✨ Iteration 7: [92mNew incumbent function value 0.000[0m [全局模型策略有效]
2025-09-02 13:12:50,735 - INFO - TabPFN评估了41个GA个体
2025-09-02 13:12:50,765 - INFO - GP模型训练完成，训练样本数: 11, 输入维度: 3
2025-09-02 13:12:50,766 - INFO - GP模型更新完成，总样本数: 11
2025-09-02 13:12:50,766 - INFO - GP模型更新完成
2025-09-02 13:12:50,767 - INFO - RBF模型训练完成，训练样本数: 11
2025-09-02 13:12:50,768 - INFO - RBF模型更新完成，总样本数: 11
2025-09-02 13:12:50,768 - INFO - RBF模型更新完成
2025-09-02 13:12:50,837 - INFO - TabPFN模型更新完成，总样本数: 11
2025-09-02 13:12:50,837 - INFO - TABPFN模型更新完成
2025-09-02 13:12:50,868 - INFO - GP模型训练完成，训练样本数: 4, 输入维度: 3
2025-09-02 13:12:50,871 - INFO - RBF模型训练完成，训练样本数: 4
2025-09-02 13:12:50,943 - INFO - TabPFN模型训练完成，训练样本数: 4, 类别数: 4
2025-09-02 13:12:52,118 - INFO - 🌟 集成模型选择中心: tensor([-1., -1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:52,119 - INFO -    当前权重: gp:0.407 | rbf:0.407 | tabpfn:0.186
2025-09-02 13:12:52,119 - INFO - 🌟 集成模型预测的TR中心: tensor([-1., -1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:52,120 - INFO - 🌟 集成代理模型预测的TR中心: tensor([-1., -1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:52,693 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:12:54,203 - INFO - 🚀 Iteration 8: No improvement. Best function value 0.000 [全局模型策略]
2025-09-02 13:12:54,744 - INFO - TabPFN评估了43个GA个体
2025-09-02 13:12:54,773 - INFO - GP模型训练完成，训练样本数: 13, 输入维度: 3
2025-09-02 13:12:54,774 - INFO - GP模型更新完成，总样本数: 13
2025-09-02 13:12:54,774 - INFO - GP模型更新完成
2025-09-02 13:12:54,775 - INFO - RBF模型训练完成，训练样本数: 13
2025-09-02 13:12:54,775 - INFO - RBF模型更新完成，总样本数: 13
2025-09-02 13:12:54,776 - INFO - RBF模型更新完成
2025-09-02 13:12:54,844 - INFO - TabPFN模型更新完成，总样本数: 13
2025-09-02 13:12:54,845 - INFO - TABPFN模型更新完成
2025-09-02 13:12:54,872 - INFO - GP模型训练完成，训练样本数: 4, 输入维度: 3
2025-09-02 13:12:54,874 - INFO - RBF模型训练完成，训练样本数: 4
2025-09-02 13:12:54,933 - INFO - TabPFN模型训练完成，训练样本数: 4, 类别数: 4
2025-09-02 13:12:55,887 - INFO - 🌟 集成模型选择中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:55,887 - INFO -    当前权重: gp:0.424 | rbf:0.363 | tabpfn:0.213
2025-09-02 13:12:55,888 - INFO - 🌟 集成模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:55,889 - INFO - 🌟 集成代理模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:56,415 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:12:56,428 - INFO - 🚀 Iteration 9: No improvement. Best function value 0.000 [全局模型策略]
2025-09-02 13:12:57,061 - INFO - TabPFN评估了46个GA个体
2025-09-02 13:12:57,095 - INFO - GP模型训练完成，训练样本数: 14, 输入维度: 3
2025-09-02 13:12:57,095 - INFO - GP模型更新完成，总样本数: 14
2025-09-02 13:12:57,095 - INFO - GP模型更新完成
2025-09-02 13:12:57,097 - INFO - RBF模型训练完成，训练样本数: 14
2025-09-02 13:12:57,098 - INFO - RBF模型更新完成，总样本数: 14
2025-09-02 13:12:57,098 - INFO - RBF模型更新完成
2025-09-02 13:12:57,216 - INFO - TabPFN模型更新完成，总样本数: 14
2025-09-02 13:12:57,217 - INFO - TABPFN模型更新完成
2025-09-02 13:12:57,249 - INFO - GP模型训练完成，训练样本数: 5, 输入维度: 3
2025-09-02 13:12:57,253 - INFO - RBF模型训练完成，训练样本数: 5
2025-09-02 13:12:57,313 - INFO - TabPFN模型训练完成，训练样本数: 5, 类别数: 2
2025-09-02 13:12:58,740 - INFO - 🌟 集成模型选择中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:58,740 - INFO -    当前权重: gp:0.550 | rbf:0.427 | tabpfn:0.023
2025-09-02 13:12:58,740 - INFO - 🌟 集成模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:58,741 - INFO - 🌟 集成代理模型预测的TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)...
2025-09-02 13:12:59,426 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:12:59,440 - INFO - 🚀 Iteration 10: No improvement. Best function value 0.000 [全局模型策略]
2025-09-02 13:12:59,440 - WARNING - 🔄 策略切换: 全局模型策略 -> 历史最优策略 (停滞3步)
2025-09-02 13:13:00,265 - INFO - TabPFN评估了43个GA个体
2025-09-02 13:13:00,266 - INFO - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)... (fx=0.000)
2025-09-02 13:13:00,870 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:13:00,882 - INFO - 🚀 Iteration 11: No improvement. Best function value 0.000 [历史最优策略]
2025-09-02 13:13:01,550 - INFO - TabPFN评估了39个GA个体
2025-09-02 13:13:01,555 - INFO - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)... (fx=0.000)
2025-09-02 13:13:02,205 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:13:02,219 - INFO - 🚀 Iteration 12: No improvement. Best function value 0.000 [历史最优策略]
2025-09-02 13:13:02,865 - INFO - TabPFN评估了47个GA个体
2025-09-02 13:13:02,869 - INFO - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([-1.,  1.,  1.], dtype=torch.float64)... (fx=0.000)
2025-09-02 13:13:03,585 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:13:03,599 - INFO - 🚀 Iteration 13: No improvement. Best function value 0.000 [历史最优策略]
2025-09-02 13:13:03,599 - WARNING - 🔄 策略切换: 历史最优策略 -> 全局模型策略 (停滞3步)
2025-09-02 13:13:04,200 - INFO - TabPFN评估了36个GA个体
2025-09-02 13:13:04,244 - INFO - GP模型训练完成，训练样本数: 19, 输入维度: 3
2025-09-02 13:13:04,244 - INFO - GP模型更新完成，总样本数: 19
2025-09-02 13:13:04,245 - INFO - GP模型更新完成
2025-09-02 13:13:04,246 - INFO - RBF模型训练完成，训练样本数: 19
2025-09-02 13:13:04,246 - INFO - RBF模型更新完成，总样本数: 19
2025-09-02 13:13:04,246 - INFO - RBF模型更新完成
2025-09-02 13:13:04,320 - INFO - TabPFN模型更新完成，总样本数: 19
2025-09-02 13:13:04,320 - INFO - TABPFN模型更新完成
2025-09-02 13:13:04,355 - INFO - GP模型训练完成，训练样本数: 7, 输入维度: 3
2025-09-02 13:13:04,358 - INFO - RBF模型训练完成，训练样本数: 7
2025-09-02 13:13:04,507 - INFO - TabPFN模型训练完成，训练样本数: 7, 类别数: 2
2025-09-02 13:13:05,726 - INFO - 🌟 集成模型选择中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:13:05,726 - INFO -    当前权重: gp:0.434 | rbf:0.541 | tabpfn:0.025
2025-09-02 13:13:05,727 - INFO - 🌟 集成模型预测的TR中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:13:05,727 - INFO - 🌟 集成代理模型预测的TR中心: tensor([1., 1., 1.], dtype=torch.float64)...
2025-09-02 13:13:06,329 - INFO - 使用TABPFN评估了3个候选点
2025-09-02 13:13:06,341 - INFO - 🚀 Iteration 14: No improvement. Best function value 0.000 [全局模型策略]
2025-09-02 13:13:06,342 - INFO - 🏁 Reached full dimensionality. Restarting with new random samples.
🚀 开始测试Bounce算法与集成代理模型系统...
✅ 基准函数: SimpleBinary, 维度: 3
cpu
✅ Bounce算法初始化完成
   集成模式启用: True
   支持的模型: ['gp', 'rbf', 'tabpfn']

🏃‍♂️ 开始运行Bounce算法...

✅ Bounce算法运行完成!
   总评估次数: 79
   最终最优值: 0.000000
   最优解: tensor([0., 0., 0.], dtype=torch.float64)
   最终权重: {'gp': 0.4343489547254063, 'rbf': 0.5408628635135257, 'tabpfn': 0.02478818176106794}

🎉 集成代理模型系统与Bounce算法集成测试成功！
