#!/usr/bin/env python3
"""
对比GP和TabPFN作为局部代理模型的性能差异
测试Labs问题，每种方法运行2次，取均值
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import subprocess
import time
import re
from typing import List, Dict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_bounce_experiment(use_tabpfn_local: bool, run_id: int) -> Dict:
    """
    运行一次Bounce实验
    
    Args:
        use_tabpfn_local: 是否使用TabPFN作为局部代理模型
        run_id: 运行ID
        
    Returns:
        实验结果字典
    """
    method_name = "TabPFN" if use_tabpfn_local else "GP"
    print(f"\n🚀 开始运行 {method_name} 局部搜索 - 第{run_id}次")
    
    # 构建命令
    cmd = [
        "python", "main.py",
        "--gin-files", "configs/default.gin",
        "--n-repeat", "1",
        "--gin-bindings", "Bounce.maximum_number_evaluations = 100",
        "--gin-bindings", "Bounce.maximum_number_evaluations_until_input_dim = 25",
    ]
    
    # 根据是否使用TabPFN添加配置
    if use_tabpfn_local:
        cmd.extend(["--gin-bindings", "Bounce.use_tabpfn_local = True"])
    else:
        cmd.extend(["--gin-bindings", "Bounce.use_tabpfn_local = False"])
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 运行实验
        result = subprocess.run(
            cmd,
            cwd="/mnt/c/Users/<USER>/Desktop/论文/混合变量优化/2023+NIPS+Bounce+TR+分箱/bounce-main",
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        # 记录结束时间
        end_time = time.time()
        runtime = end_time - start_time
        
        if result.returncode != 0:
            print(f"❌ {method_name} 第{run_id}次运行失败")
            print(f"错误输出: {result.stderr}")
            return None
        
        # 解析结果
        output = result.stdout
        
        # 提取最终函数值
        final_value = None
        improvement_count = 0
        tabpfn_usage_count = 0
        
        # 查找最终结果
        lines = output.split('\n')
        for line in lines:
            # 查找改进次数
            if "New incumbent function value" in line:
                improvement_count += 1
                # 提取函数值
                match = re.search(r'function value ([-\d.]+)', line)
                if match:
                    final_value = float(match.group(1))
            
            # 查找TabPFN使用次数
            if "使用TabPFN评估了" in line:
                tabpfn_usage_count += 1
        
        result_dict = {
            'method': method_name,
            'run_id': run_id,
            'final_value': final_value,
            'improvement_count': improvement_count,
            'tabpfn_usage_count': tabpfn_usage_count,
            'runtime': runtime,
            'success': True
        }
        
        print(f"✅ {method_name} 第{run_id}次完成:")
        print(f"   最终函数值: {final_value}")
        print(f"   改进次数: {improvement_count}")
        print(f"   TabPFN使用次数: {tabpfn_usage_count}")
        print(f"   运行时间: {runtime:.1f}秒")
        
        return result_dict
        
    except subprocess.TimeoutExpired:
        print(f"❌ {method_name} 第{run_id}次运行超时")
        return None
    except Exception as e:
        print(f"❌ {method_name} 第{run_id}次运行出错: {e}")
        return None


def analyze_results(results: List[Dict]):
    """分析实验结果"""
    
    print("\n" + "=" * 60)
    print("📊 实验结果分析")
    print("=" * 60)
    
    # 按方法分组
    gp_results = [r for r in results if r and r['method'] == 'GP']
    tabpfn_results = [r for r in results if r and r['method'] == 'TabPFN']
    
    def calculate_stats(method_results, method_name):
        if not method_results:
            print(f"\n❌ {method_name} 没有成功的运行结果")
            return None
        
        final_values = [r['final_value'] for r in method_results if r['final_value'] is not None]
        improvement_counts = [r['improvement_count'] for r in method_results]
        runtimes = [r['runtime'] for r in method_results]
        tabpfn_usages = [r['tabpfn_usage_count'] for r in method_results]
        
        stats = {
            'method': method_name,
            'runs': len(method_results),
            'final_value_mean': sum(final_values) / len(final_values) if final_values else None,
            'final_value_best': min(final_values) if final_values else None,
            'improvement_count_mean': sum(improvement_counts) / len(improvement_counts),
            'runtime_mean': sum(runtimes) / len(runtimes),
            'tabpfn_usage_mean': sum(tabpfn_usages) / len(tabpfn_usages),
        }
        
        print(f"\n🔍 {method_name} 局部搜索结果:")
        print(f"   成功运行次数: {stats['runs']}")
        print(f"   平均最终函数值: {stats['final_value_mean']:.3f}" if stats['final_value_mean'] else "   平均最终函数值: N/A")
        print(f"   最佳函数值: {stats['final_value_best']:.3f}" if stats['final_value_best'] else "   最佳函数值: N/A")
        print(f"   平均改进次数: {stats['improvement_count_mean']:.1f}")
        print(f"   平均运行时间: {stats['runtime_mean']:.1f}秒")
        print(f"   平均TabPFN使用次数: {stats['tabpfn_usage_mean']:.1f}")
        
        return stats
    
    gp_stats = calculate_stats(gp_results, "GP")
    tabpfn_stats = calculate_stats(tabpfn_results, "TabPFN")
    
    # 性能对比
    if gp_stats and tabpfn_stats:
        print(f"\n🏆 性能对比:")
        
        if gp_stats['final_value_mean'] and tabpfn_stats['final_value_mean']:
            value_diff = tabpfn_stats['final_value_mean'] - gp_stats['final_value_mean']
            value_improvement = -value_diff / abs(gp_stats['final_value_mean']) * 100
            
            print(f"   最终函数值:")
            print(f"     GP: {gp_stats['final_value_mean']:.3f}")
            print(f"     TabPFN: {tabpfn_stats['final_value_mean']:.3f}")
            print(f"     差异: {value_diff:+.3f} ({value_improvement:+.1f}%)")
        
        improvement_diff = tabpfn_stats['improvement_count_mean'] - gp_stats['improvement_count_mean']
        print(f"   改进次数:")
        print(f"     GP: {gp_stats['improvement_count_mean']:.1f}")
        print(f"     TabPFN: {tabpfn_stats['improvement_count_mean']:.1f}")
        print(f"     差异: {improvement_diff:+.1f}")
        
        runtime_diff = tabpfn_stats['runtime_mean'] - gp_stats['runtime_mean']
        runtime_ratio = tabpfn_stats['runtime_mean'] / gp_stats['runtime_mean']
        print(f"   运行时间:")
        print(f"     GP: {gp_stats['runtime_mean']:.1f}秒")
        print(f"     TabPFN: {tabpfn_stats['runtime_mean']:.1f}秒")
        print(f"     差异: {runtime_diff:+.1f}秒 ({runtime_ratio:.1f}x)")
        
        # 总结
        print(f"\n📝 总结:")
        if tabpfn_stats['final_value_mean'] and gp_stats['final_value_mean']:
            if tabpfn_stats['final_value_mean'] < gp_stats['final_value_mean']:
                print("   ✅ TabPFN局部搜索在最终函数值上表现更好")
            elif tabpfn_stats['final_value_mean'] > gp_stats['final_value_mean']:
                print("   ✅ GP局部搜索在最终函数值上表现更好")
            else:
                print("   ⚖️ 两种方法在最终函数值上表现相当")
        
        if tabpfn_stats['improvement_count_mean'] > gp_stats['improvement_count_mean']:
            print("   ✅ TabPFN局部搜索有更多改进次数")
        elif tabpfn_stats['improvement_count_mean'] < gp_stats['improvement_count_mean']:
            print("   ✅ GP局部搜索有更多改进次数")
        else:
            print("   ⚖️ 两种方法改进次数相当")


if __name__ == "__main__":
    print("🚀 开始局部代理模型性能对比测试")
    print("测试问题: Labs")
    print("评估预算: 100次")
    print("每种方法运行: 2次")
    print("=" * 60)
    
    all_results = []
    
    try:
        # 测试GP局部搜索 (2次)
        for run_id in range(1, 3):
            result = run_bounce_experiment(use_tabpfn_local=False, run_id=run_id)
            if result:
                all_results.append(result)
        
        # 测试TabPFN局部搜索 (2次)
        for run_id in range(1, 3):
            result = run_bounce_experiment(use_tabpfn_local=True, run_id=run_id)
            if result:
                all_results.append(result)
        
        # 分析结果
        analyze_results(all_results)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        if all_results:
            print("分析已完成的结果:")
            analyze_results(all_results)
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
