#!/usr/bin/env python3
"""
Bounce算法集成遗传算法和TabPFN全局代理模型的完整示例

这个示例展示了如何使用新集成的GA-TabPFN功能来改进Bounce算法的全局搜索能力。

主要特性：
1. 遗传算法执行全局搜索，探索整个搜索空间
2. TabPFN作为全局代理模型，预测最优的信赖域中心点
3. 支持混合变量类型（二分、连续、类别变量）
4. 自动处理不同变量类型的编码和操作
"""

import logging
import gin
import torch
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_bounce_with_ga_tabpfn():
    """运行集成了GA-TabPFN的Bounce算法"""
    
    print("🚀 启动Bounce算法 + 遗传算法 + TabPFN全局代理模型")
    print("=" * 60)
    
    # 模拟TabPFN以避免网络调用（在实际使用中可以移除这部分）
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        # 模拟预测概率：第一个类别（最好）概率更高
        mock_instance.predict_proba.return_value = [
            [0.7, 0.2, 0.1],  # 候选点1：很可能是最好的
            [0.3, 0.4, 0.3],  # 候选点2：中等质量
            [0.1, 0.3, 0.6],  # 候选点3：可能较差
        ]
        mock_classifier.return_value = mock_instance
        
        # 清除之前的gin配置
        gin.clear_config()

        # 导入必要的模块（必须在gin配置之前）
        from bounce.bounce import Bounce
        from bounce.benchmarks import MaxSat60

        # 配置Bounce算法参数
        gin.parse_config([
            # 基本参数
            "Bounce.number_initial_points = 5",
            "Bounce.initial_target_dimensionality = 4", 
            "Bounce.number_new_bins_on_split = 2",
            "Bounce.maximum_number_evaluations = 50",
            "Bounce.batch_size = 1",
            "Bounce.results_dir = 'results_ga_tabpfn'",
            "Bounce.device = 'cpu'",
            "Bounce.dtype = 'float64'",
            "Bounce.use_scipy_lbfgs = False",
            "Bounce.maximum_number_evaluations_until_input_dim = 40"
        ])
        
        # 创建基准函数（60维二分变量MaxSAT问题）
        benchmark = MaxSat60()
        print(f"📊 基准函数: {benchmark.__class__.__name__}")
        print(f"📏 问题维度: {benchmark.dim}")
        print(f"🔢 变量类型: 全部为二分变量")
        
        # 创建Bounce实例（自动集成GA-TabPFN）
        bounce = Bounce(benchmark=benchmark)
        
        print(f"\n🧬 遗传算法配置:")
        ga_info = bounce.ga_tabpfn_integration.get_integration_info()['ga_info']
        print(f"   种群大小: {ga_info['population_size']}")
        print(f"   最大代数: {ga_info['max_generations']}")
        
        print(f"\n🤖 TabPFN代理模型配置:")
        surrogate_info = bounce.ga_tabpfn_integration.get_integration_info()['surrogate_info']
        print(f"   离散化箱数: {surrogate_info['n_bins']}")
        print(f"   特征维度: {surrogate_info['n_features']}")
        
        print(f"\n⚙️ Bounce算法配置:")
        print(f"   初始目标维度: {bounce.initial_target_dimensionality}")
        print(f"   最大评估次数: {bounce.maximum_number_evaluations}")
        print(f"   全局搜索间隔: {bounce.global_search_interval}")
        
        print(f"\n🏃‍♂️ 开始优化过程...")
        print("-" * 60)
        
        # 运行优化
        bounce.run()
        
        print("\n" + "=" * 60)
        print("🎯 优化结果:")
        print(f"   总评估次数: {bounce._n_evals}")
        print(f"   最佳函数值: {bounce.fx_global.min().item():.6f}")
        print(f"   数据点总数: {len(bounce.x_up_global)}")
        
        # 显示GA-TabPFN集成的统计信息
        final_info = bounce.ga_tabpfn_integration.get_integration_info()
        print(f"\n🧬 遗传算法统计:")
        print(f"   当前代数: {final_info['ga_info']['current_generation']}")
        print(f"   最佳适应度: {final_info['ga_info']['best_fitness']}")
        
        print(f"\n🤖 TabPFN代理模型统计:")
        print(f"   训练样本数: {final_info['surrogate_info']['n_train_samples']}")
        print(f"   模型状态: {'已训练' if final_info['surrogate_info']['is_fitted'] else '未训练'}")
        
        print(f"\n📈 评估历史:")
        eval_history = bounce.eval_history
        if eval_history:
            print(f"   初始值: {eval_history[0][1]:.6f}")
            print(f"   最终值: {eval_history[-1][1]:.6f}")
            print(f"   改进幅度: {eval_history[0][1] - eval_history[-1][1]:.6f}")
        
        print(f"\n💾 结果保存位置: {bounce.results_dir}")
        
        return bounce


def demonstrate_mixed_variables():
    """演示混合变量类型的处理"""
    
    print("\n" + "=" * 60)
    print("🔀 混合变量类型演示")
    print("=" * 60)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建包含不同类型变量的基准函数
    parameters = [
        Parameter(name="binary1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="binary2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="continuous1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="continuous2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="categorical1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=2),
        Parameter(name="categorical2", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=3),
    ]
    
    class MixedBenchmark:
        def __init__(self):
            self.parameters = parameters
            self.dim = len(parameters)
            self.representation_dim = 10  # 类别变量需要one-hot编码
            self.lb_vec = torch.zeros(10, dtype=torch.float64)
            self.ub_vec = torch.ones(10, dtype=torch.float64)
            self.is_mixed = True
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            # 简单的测试函数：最小化所有变量的平方和
            return torch.sum(x**2, dim=1)
    
    benchmark = MixedBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=6)
    
    print(f"📊 混合基准函数:")
    print(f"   二分变量: 2个")
    print(f"   连续变量: 2个") 
    print(f"   类别变量: 2个 (3类 + 4类)")
    print(f"   总表示维度: {benchmark.representation_dim}")
    
    # 创建遗传算法
    config = GAConfig(population_size=12, max_generations=5)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"\n🧬 遗传算法变量索引分析:")
    print(f"   二分变量索引: {ga.binary_indices.tolist()}")
    print(f"   连续变量索引: {ga.continuous_indices.tolist()}")
    print(f"   类别变量索引: {ga.categorical_indices.tolist()}")
    
    # 运行遗传算法
    print(f"\n🏃‍♂️ 运行遗传算法...")
    best_individual = ga.run()
    
    print(f"\n🎯 优化结果:")
    print(f"   最佳适应度: {best_individual.fitness:.6f}")
    print(f"   进化代数: {ga.generation}")
    print(f"   种群多样性: {ga.get_population_diversity():.6f}")
    
    # 显示最佳个体的基因
    print(f"\n🧬 最佳个体基因 (低维空间):")
    genes = best_individual.genes
    print(f"   二分变量: {genes[ga.binary_indices].tolist()}")
    print(f"   连续变量: {genes[ga.continuous_indices].tolist()}")
    print(f"   类别变量: {genes[ga.categorical_indices].tolist()}")
    
    # 转换到高维空间
    high_dim = best_individual.to_high_dim(benchmark.lb_vec, benchmark.ub_vec)
    print(f"\n🔄 高维空间表示:")
    print(f"   形状: {high_dim.shape}")
    print(f"   前5个值: {high_dim[:5].tolist()}")


if __name__ == "__main__":
    try:
        # 运行主要的Bounce + GA + TabPFN示例
        bounce_result = run_bounce_with_ga_tabpfn()
        
        # 演示混合变量类型处理
        demonstrate_mixed_variables()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("=" * 60)
        
        print("\n📝 总结:")
        print("1. ✅ Bounce算法成功集成了遗传算法和TabPFN全局代理模型")
        print("2. ✅ 支持混合变量类型的优化（二分、连续、类别）")
        print("3. ✅ 自动处理不同变量类型的编码和遗传操作")
        print("4. ✅ TabPFN代理模型能够预测最优的信赖域中心点")
        print("5. ✅ 全局搜索与局部搜索有效结合")
        
        print("\n🔧 使用说明:")
        print("- 在实际使用中，移除TabPFN的mock设置")
        print("- 根据问题特性调整GA参数（种群大小、代数等）")
        print("- 根据计算资源调整全局搜索频率")
        print("- 可以通过gin配置文件灵活调整所有参数")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        gin.clear_config()
