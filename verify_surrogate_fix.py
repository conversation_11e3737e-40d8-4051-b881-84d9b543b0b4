#!/usr/bin/env python3
"""
验证代理模型修复的脚本
检查集成代理模型是否正确用于遗传算法评估
"""

import logging
import gin
import torch
from bounce.bounce import Bounce

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(levelname)s:%(asctime)s - (%(filename)s:%(lineno)d) - %(message)s"
    )

def verify_surrogate_usage():
    """验证代理模型是否被正确使用"""
    print("🔍 开始验证代理模型修复...")
    
    # 解析配置
    gin.parse_config_files_and_bindings(["configs/default.gin"], [])
    
    # 创建Bounce实例
    bounce = Bounce()
    
    # 运行初始化
    bounce.sample_init()
    print(f"✅ 初始化完成，初始数据点数: {len(bounce.x_up_global)}")
    
    # 检查GA-TabPFN集成系统
    ga_integration = bounce.ga_tabpfn_integration
    print(f"✅ GA-TabPFN集成系统: {type(ga_integration).__name__}")
    print(f"✅ 集成模式启用: {ga_integration.enable_ensemble}")
    
    if ga_integration.enable_ensemble:
        ensemble_manager = ga_integration.ensemble_manager
        print(f"✅ 集成管理器: {type(ensemble_manager).__name__}")
        print(f"✅ 支持的模型: {ensemble_manager.model_types}")
        print(f"✅ 初始权重: {ensemble_manager.weight_manager.get_weights()}")
        
        # 检查GA是否正确配置了集成管理器
        ga = ga_integration.ga
        print(f"✅ GA配置检查:")
        print(f"   - global_surrogate: {ga.global_surrogate}")
        print(f"   - ensemble_manager: {ga.ensemble_manager}")
        print(f"   - 种群大小: {len(ga.population) if ga.population else 0}")
    
    # 运行一次迭代来验证代理模型使用
    print("\n🚀 运行一次迭代验证代理模型使用...")
    
    # 记录关键日志消息
    key_messages = []

    class LogCapture(logging.Handler):
        def emit(self, record):
            message = self.format(record)
            if any(keyword in message for keyword in [
                "集成代理模型评估", "首次训练集成代理模型", "更新集成代理模型",
                "集成模型开始预测最佳中心点", "权重适度调整", "集成模型预测详情"
            ]):
                key_messages.append(message)
    
    # 添加日志捕获器
    log_capture = LogCapture()
    logging.getLogger().addHandler(log_capture)
    
    try:
        # 运行一次优化迭代
        original_max_evals = bounce.maximum_number_evaluations
        bounce.maximum_number_evaluations = 10  # 只运行几次迭代
        
        # 运行优化
        for i in range(3):  # 运行3次迭代
            if bounce._n_evals >= bounce.maximum_number_evaluations:
                break
                
            print(f"\n--- 迭代 {i+1} ---")
            
            # 模拟一次优化步骤
            axus = bounce.random_embedding
            x = bounce.x_tr
            fx = bounce.fx_tr
            
            # 检查是否会运行全局搜索
            if (bounce.enable_ga_every_iteration and
                len(bounce.x_up_global) >= bounce.min_data_for_tabpfn):
                
                print(f"✅ 满足全局搜索条件: 数据点数={len(bounce.x_up_global)}, 最小需求={bounce.min_data_for_tabpfn}")
                
                try:
                    # 运行全局搜索
                    best_low_dim, best_high_dim = bounce.ga_tabpfn_integration.run_global_search(
                        existing_X=bounce.x_up_global,
                        existing_y=bounce.fx_global
                    )
                    print(f"✅ 全局搜索成功，返回最佳点: {best_low_dim[:3]}...")

                    # 继续运行预测中心点的步骤
                    print("🔍 运行预测最佳中心点...")
                    best_center = bounce.ga_tabpfn_integration.predict_best_center_with_tabpfn(
                        existing_X=bounce.x_up_global,
                        existing_y=bounce.fx_global
                    )
                    print(f"✅ 预测中心点成功: {best_center[:3]}...")
                    break
                    
                except Exception as e:
                    print(f"❌ 全局搜索失败: {e}")
                    break
            else:
                print(f"❌ 不满足全局搜索条件: 数据点数={len(bounce.x_up_global)}, 最小需求={bounce.min_data_for_tabpfn}")
                break
        
        # 恢复原始设置
        bounce.maximum_number_evaluations = original_max_evals
        
    finally:
        # 移除日志捕获器
        logging.getLogger().removeHandler(log_capture)
    
    # 分析捕获的日志
    print(f"\n📊 捕获到 {len(key_messages)} 条关键日志消息:")
    for i, msg in enumerate(key_messages, 1):
        print(f"  {i}. {msg}")
    
    # 验证结果
    success_indicators = [
        "集成代理模型评估" in " ".join(key_messages),
        "训练集成代理模型" in " ".join(key_messages),
        ("集成模型开始预测最佳中心点" in " ".join(key_messages) or
         "集成模型预测详情" in " ".join(key_messages))
    ]
    
    print(f"\n🎯 验证结果:")
    print(f"  ✅ 集成代理模型评估GA个体: {'是' if success_indicators[0] else '否'}")
    print(f"  ✅ 集成代理模型训练: {'是' if success_indicators[1] else '否'}")
    print(f"  ✅ 集成模型预测中心点: {'是' if success_indicators[2] else '否'}")
    
    overall_success = all(success_indicators)
    print(f"\n{'🎉 修复验证成功！' if overall_success else '❌ 修复验证失败！'}")
    
    return overall_success

if __name__ == "__main__":
    setup_logging()
    success = verify_surrogate_usage()
    exit(0 if success else 1)
