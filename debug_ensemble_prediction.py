#!/usr/bin/env python3
"""
调试集成代理模型预测问题
专门调查GP和RBF模型为什么对所有候选点返回相同值
"""

import torch
import logging
import numpy as np
from bounce.ensemble_manager import EnsembleManager
from bounce.gp_surrogate import GPGlobalSurrogate
from bounce.rbf_surrogate import RBFGlobalSurrogate
from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
from bounce.benchmarks import MaxSat60
from bounce.projection import AxUS

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def debug_individual_models():
    """测试单个模型的预测行为"""
    print("🔍 调试单个模型的预测行为...")
    
    # 创建基准函数和投影
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    print(f"问题维度: {benchmark.dim}")
    print(f"目标维度: {axus.target_dim}")
    print(f"表示维度: {benchmark.representation_dim}")
    
    # 生成训练数据（高维空间）
    n_train = 15
    X_train_high = torch.rand(n_train, benchmark.representation_dim, dtype=torch.float64)
    y_train = torch.rand(n_train, dtype=torch.float64) * 100 - 50  # 范围[-50, 50]
    
    print(f"\n📊 训练数据:")
    print(f"   高维训练数据形状: {X_train_high.shape}")
    print(f"   目标值范围: [{y_train.min():.4f}, {y_train.max():.4f}]")
    
    # 生成测试数据（候选点）
    n_test = 5
    candidates_low = torch.rand(n_test, axus.target_dim, dtype=torch.float64) * 2 - 1  # [-1, 1]
    candidates_high = torch.stack([axus.project_up(c.unsqueeze(0).T).T.squeeze(0) for c in candidates_low])
    
    print(f"\n🎯 测试数据:")
    print(f"   低维候选点形状: {candidates_low.shape}")
    print(f"   高维候选点形状: {candidates_high.shape}")
    print(f"   候选点示例 (低维): {candidates_low[0]}")
    print(f"   候选点示例 (高维): {candidates_high[0][:10]}...")  # 只显示前10维
    
    # 测试GP模型
    print("\n🧠 测试GP模型...")
    try:
        gp_model = GPGlobalSurrogate(benchmark, axus, device='cpu')
        gp_model.fit(X_train_high, y_train)
        gp_predictions = gp_model.predict(candidates_high)
        
        print(f"   GP训练成功")
        print(f"   GP预测结果: {gp_predictions}")
        print(f"   GP预测范围: [{gp_predictions.min():.4f}, {gp_predictions.max():.4f}]")
        print(f"   GP预测是否相同: {torch.allclose(gp_predictions, gp_predictions[0], atol=1e-6)}")
        
        # 检查GP内部状态
        print(f"   GP内部状态:")
        print(f"     - is_fitted: {gp_model.is_fitted}")
        print(f"     - 训练数据形状: {gp_model.train_x.shape if gp_model.train_x is not None else 'None'}")
        print(f"     - y_mean: {gp_model.y_mean:.4f}")
        print(f"     - y_std: {gp_model.y_std:.4f}")
        
    except Exception as e:
        print(f"   ❌ GP模型测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试RBF模型
    print("\n🔄 测试RBF模型...")
    try:
        rbf_model = RBFGlobalSurrogate(benchmark, axus, device='cpu')
        rbf_model.fit(X_train_high, y_train)
        rbf_predictions = rbf_model.predict(candidates_high)
        
        print(f"   RBF训练成功")
        print(f"   RBF预测结果: {rbf_predictions}")
        print(f"   RBF预测范围: [{rbf_predictions.min():.4f}, {rbf_predictions.max():.4f}]")
        print(f"   RBF预测是否相同: {torch.allclose(rbf_predictions, rbf_predictions[0], atol=1e-6)}")
        
        # 检查RBF内部状态
        print(f"   RBF内部状态:")
        print(f"     - is_fitted: {rbf_model.is_fitted}")
        print(f"     - 训练数据形状: {rbf_model.X_train.shape if rbf_model.X_train is not None else 'None'}")
        print(f"     - 权重形状: {rbf_model.weights.shape if rbf_model.weights is not None else 'None'}")
        
    except Exception as e:
        print(f"   ❌ RBF模型测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试TabPFN模型
    print("\n🤖 测试TabPFN模型...")
    try:
        # 模拟TabPFN以避免网络调用
        from unittest.mock import Mock, patch
        with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
            mock_instance = Mock()
            mock_instance.fit.return_value = None
            mock_instance.predict_proba.return_value = np.random.rand(n_test, 5)  # 随机概率
            mock_classifier.return_value = mock_instance
            
            tabpfn_model = TabPFNGlobalSurrogate(benchmark, n_bins=5, device='cpu', axus=axus)
            tabpfn_model.fit(X_train_high, y_train)
            tabpfn_predictions = tabpfn_model.predict(candidates_high)
            
            print(f"   TabPFN训练成功（模拟）")
            print(f"   TabPFN预测结果: {tabpfn_predictions}")
            print(f"   TabPFN预测范围: [{tabpfn_predictions.min():.4f}, {tabpfn_predictions.max():.4f}]")
            print(f"   TabPFN预测是否相同: {torch.allclose(tabpfn_predictions, tabpfn_predictions[0], atol=1e-6)}")
        
    except Exception as e:
        print(f"   ❌ TabPFN模型测试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_data_preprocessing():
    """调试数据预处理过程"""
    print("\n🔧 调试数据预处理过程...")
    
    # 创建简单的测试数据
    X_train = torch.tensor([
        [1.0, 2.0, 3.0],
        [4.0, 5.0, 6.0],
        [7.0, 8.0, 9.0],
        [10.0, 11.0, 12.0],
        [13.0, 14.0, 15.0]
    ], dtype=torch.float64)
    
    y_train = torch.tensor([10.0, 20.0, 30.0, 40.0, 50.0], dtype=torch.float64)
    
    X_test = torch.tensor([
        [2.0, 3.0, 4.0],
        [5.0, 6.0, 7.0],
        [8.0, 9.0, 10.0]
    ], dtype=torch.float64)
    
    print(f"原始训练数据:")
    print(f"   X_train: {X_train}")
    print(f"   y_train: {y_train}")
    print(f"原始测试数据:")
    print(f"   X_test: {X_test}")
    
    # 测试GP的数据预处理
    print(f"\n📊 GP数据预处理:")
    X_min = X_train.min(dim=0)[0]
    X_max = X_train.max(dim=0)[0]
    X_range = X_max - X_min
    X_range = torch.where(X_range < 1e-8, torch.ones_like(X_range), X_range)
    
    X_train_normalized = (X_train - X_min) / X_range
    X_test_normalized = (X_test - X_min) / X_range
    
    y_mean = y_train.mean()
    y_std = y_train.std()
    y_train_normalized = (y_train - y_mean) / y_std
    
    print(f"   X_min: {X_min}")
    print(f"   X_max: {X_max}")
    print(f"   X_range: {X_range}")
    print(f"   X_train_normalized: {X_train_normalized}")
    print(f"   X_test_normalized: {X_test_normalized}")
    print(f"   y_mean: {y_mean}")
    print(f"   y_std: {y_std}")
    print(f"   y_train_normalized: {y_train_normalized}")
    
    # 检查是否标准化后的数据有问题
    if torch.allclose(X_test_normalized[0], X_test_normalized[1], atol=1e-6):
        print("   ⚠️ 警告：测试数据标准化后变得相同！")
    else:
        print("   ✅ 测试数据标准化后仍然不同")

def debug_ensemble_prediction():
    """调试集成模型的预测过程"""
    print("\n🌟 调试集成模型预测过程...")
    
    # 创建基准函数和投影
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建集成管理器
    ensemble_manager = EnsembleManager(
        benchmark=benchmark,
        axus=axus,
        device='cpu',
        model_types=['gp', 'rbf', 'tabpfn'],
        weight_window_size=10,
        min_weight_protection=0.1,
        top_k_candidates=3
    )
    
    # 生成训练数据
    n_train = 15
    X_train_high = torch.rand(n_train, benchmark.representation_dim, dtype=torch.float64)
    y_train = torch.rand(n_train, dtype=torch.float64) * 100 - 50
    
    # 训练集成模型
    ensemble_manager.fit(X_train_high, y_train)
    
    # 生成候选点
    n_candidates = 5
    candidates_low = torch.rand(n_candidates, axus.target_dim, dtype=torch.float64) * 2 - 1
    
    print(f"候选点（低维）: {candidates_low}")
    
    # 调用predict_all_models方法
    candidates_high = torch.stack([axus.project_up(c.unsqueeze(0).T).T.squeeze(0) for c in candidates_low])
    predictions_dict = ensemble_manager.predict_all_models(candidates_high)
    
    print(f"\n各模型预测结果:")
    for model_name, predictions in predictions_dict.items():
        print(f"   {model_name.upper()}: {predictions}")
        if len(predictions) > 1:
            all_same = torch.allclose(predictions, predictions[0], atol=1e-6)
            print(f"   {model_name.upper()} 所有预测是否相同: {all_same}")
        
        # 检查是否是单一值
        if len(set(predictions.tolist())) == 1:
            print(f"   🚨 {model_name.upper()} 返回单一值: {predictions[0].item()}")

def main():
    print("🚀 开始调试集成代理模型预测问题...")
    print("=" * 60)
    
    # 调试个别模型
    debug_individual_models()
    
    # 调试数据预处理
    debug_data_preprocessing()
    
    # 调试集成预测
    debug_ensemble_prediction()
    
    print("\n" + "=" * 60)
    print("🎯 调试总结:")
    print("   请检查上述输出，重点关注:")
    print("   1. GP和RBF模型是否对不同输入返回相同预测值")
    print("   2. 数据预处理是否导致输入变得相同")
    print("   3. 模型内部状态是否正常")

if __name__ == "__main__":
    main()