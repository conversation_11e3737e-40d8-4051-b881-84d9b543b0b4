#!/usr/bin/env python3
"""
测试冗余警告修复的脚本
验证集成管理器不再输出重复的方差过小警告
"""

import logging
import gin
import torch
from bounce.bounce import Bounce

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.WARNING,  # 只显示WARNING及以上级别
        format="%(levelname)s:%(asctime)s - (%(filename)s:%(lineno)d) - %(message)s"
    )

def test_redundant_warnings():
    """测试冗余警告是否被移除"""
    print("🔍 测试冗余警告修复...")
    
    # 解析配置
    gin.parse_config_files_and_bindings(["configs/default.gin"], [])
    
    # 创建Bounce实例
    bounce = Bounce()
    
    # 运行初始化
    bounce.sample_init()
    print(f"✅ 初始化完成，初始数据点数: {len(bounce.x_up_global)}")
    
    # 捕获警告信息
    warning_messages = []
    
    class WarningCapture(logging.Handler):
        def emit(self, record):
            if record.levelno >= logging.WARNING:
                message = self.format(record)
                if "预测值方差过小" in message:
                    warning_messages.append(message)
    
    # 添加警告捕获器
    warning_capture = WarningCapture()
    logging.getLogger().addHandler(warning_capture)
    
    try:
        # 运行一次全局搜索来触发预测
        ga_integration = bounce.ga_tabpfn_integration
        
        if (ga_integration.enable_ensemble and 
            len(bounce.x_up_global) >= bounce.min_data_for_tabpfn):
            
            print("🚀 运行全局搜索以触发预测...")
            
            # 运行全局搜索
            best_low_dim, best_high_dim = ga_integration.run_global_search(
                existing_X=bounce.x_up_global,
                existing_y=bounce.fx_global
            )
            
            # 运行预测中心点
            best_center = ga_integration.predict_best_center_with_tabpfn(
                existing_X=bounce.x_up_global,
                existing_y=bounce.fx_global
            )
            
            print(f"✅ 全局搜索和预测完成")
        
    finally:
        # 移除警告捕获器
        logging.getLogger().removeHandler(warning_capture)
    
    # 分析捕获的警告
    print(f"\n📊 捕获到 {len(warning_messages)} 条方差过小警告:")
    
    gp_warnings = [msg for msg in warning_messages if "GP" in msg]
    rbf_warnings = [msg for msg in warning_messages if "RBF" in msg]
    ensemble_warnings = [msg for msg in warning_messages if "🚨" in msg and ("GP模型" in msg or "RBF模型" in msg)]
    
    print(f"  - GP原始警告: {len(gp_warnings)}")
    print(f"  - RBF原始警告: {len(rbf_warnings)}")
    print(f"  - 集成管理器重复警告: {len(ensemble_warnings)}")
    
    # 显示具体警告（如果有的话）
    if warning_messages:
        print("\n📝 具体警告信息:")
        for i, msg in enumerate(warning_messages, 1):
            print(f"  {i}. {msg}")
    
    # 验证结果
    redundant_removed = len(ensemble_warnings) == 0
    
    print(f"\n🎯 测试结果:")
    print(f"  ✅ 原始模型警告正常: {'是' if (gp_warnings or rbf_warnings) else '否'}")
    print(f"  ✅ 集成管理器冗余警告已移除: {'是' if redundant_removed else '否'}")
    
    overall_success = redundant_removed
    print(f"\n{'🎉 冗余警告修复成功！' if overall_success else '❌ 冗余警告修复失败！'}")
    
    return overall_success

if __name__ == "__main__":
    setup_logging()
    success = test_redundant_warnings()
    exit(0 if success else 1)
