#!/usr/bin/env python3
"""
测试修改后的GA是否保持离散变量的离散性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_binary_discreteness():
    """测试二分变量的离散性"""
    
    print("🧪 测试二分变量的离散性")
    print("=" * 50)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建纯二分变量基准
    class BinaryBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
                for i in range(5)
            ]
            self.dim = 5
            self.representation_dim = 5
            self.lb_vec = torch.zeros(5, dtype=torch.float64)
            self.ub_vec = torch.ones(5, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = BinaryBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    # 创建GA
    config = GAConfig(population_size=10, max_generations=3)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"二分变量索引: {ga.binary_indices}")
    print(f"连续变量索引: {ga.continuous_indices}")
    print(f"类别变量索引: {ga.categorical_indices}")
    
    # 初始化种群
    ga.initialize_population()
    
    print("\n--- 检查初始种群的离散性 ---")
    for i, individual in enumerate(ga.population[:3]):  # 检查前3个个体
        genes = individual.genes
        binary_genes = genes[ga.binary_indices] if len(ga.binary_indices) > 0 else torch.tensor([])
        
        print(f"个体{i+1}:")
        print(f"  完整基因: {genes}")
        print(f"  二分基因: {binary_genes}")
        
        # 验证二分变量是否为{-1, 1}
        if len(binary_genes) > 0:
            unique_values = torch.unique(binary_genes)
            is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
            print(f"  二分变量是否离散: {is_discrete}")
            print(f"  二分变量唯一值: {unique_values}")
        
    # 进化几代并检查离散性
    print("\n--- 进化过程中的离散性检查 ---")
    for generation in range(3):
        print(f"\n第{generation+1}代:")
        
        # 进化一代
        ga.evolve_generation()
        
        # 检查进化后的离散性
        all_discrete = True
        for i, individual in enumerate(ga.population[:2]):  # 检查前2个个体
            genes = individual.genes
            binary_genes = genes[ga.binary_indices] if len(ga.binary_indices) > 0 else torch.tensor([])
            
            if len(binary_genes) > 0:
                unique_values = torch.unique(binary_genes)
                is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
                
                print(f"  个体{i+1} 二分基因: {binary_genes}")
                print(f"  个体{i+1} 是否离散: {is_discrete}")
                
                if not is_discrete:
                    all_discrete = False
                    print(f"  ❌ 发现非离散值: {unique_values}")
        
        if all_discrete:
            print(f"  ✅ 第{generation+1}代所有二分变量保持离散")
        else:
            print(f"  ❌ 第{generation+1}代存在非离散的二分变量")
    
    return all_discrete


def test_categorical_onehot():
    """测试类别变量的one-hot编码正确性"""
    
    print("\n" + "=" * 50)
    print("🧪 测试类别变量的one-hot编码")
    print("=" * 50)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建包含类别变量的基准
    class CategoricalBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name="cat1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=2),  # 3个类别
                Parameter(name="cat2", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=3),  # 4个类别
            ]
            self.dim = 2
            self.representation_dim = 7  # 3 + 4 = 7 (one-hot编码)
            self.lb_vec = torch.zeros(7, dtype=torch.float64)
            self.ub_vec = torch.ones(7, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = CategoricalBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=2)
    
    # 创建GA
    config = GAConfig(population_size=6, max_generations=2)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"类别变量索引: {ga.categorical_indices}")
    
    # 初始化种群
    ga.initialize_population()
    
    print("\n--- 检查初始种群的one-hot编码 ---")
    for i, individual in enumerate(ga.population[:2]):
        genes = individual.genes
        print(f"个体{i+1} 完整基因: {genes}")
        
        # 检查每个类别变量的one-hot编码
        for bin_obj, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            if len(indices) > 0:
                segment = genes[indices]
                n_ones = torch.sum(segment == 1.0).item()
                n_minus_ones = torch.sum(segment == -1.0).item()
                
                print(f"  类别段 {indices}: {segment}")
                print(f"  1的个数: {n_ones}, -1的个数: {n_minus_ones}")
                
                is_valid_onehot = (n_ones == 1) and (n_minus_ones == len(indices) - 1)
                print(f"  是否有效one-hot: {is_valid_onehot}")
    
    # 进化并检查one-hot编码
    print("\n--- 进化过程中的one-hot编码检查 ---")
    for generation in range(2):
        print(f"\n第{generation+1}代:")
        ga.evolve_generation()
        
        all_valid = True
        for i, individual in enumerate(ga.population[:2]):
            genes = individual.genes
            
            for bin_obj, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                if len(indices) > 0:
                    segment = genes[indices]
                    n_ones = torch.sum(segment == 1.0).item()
                    n_minus_ones = torch.sum(segment == -1.0).item()
                    
                    is_valid_onehot = (n_ones == 1) and (n_minus_ones == len(indices) - 1)
                    
                    print(f"  个体{i+1} 类别段: {segment}, 有效: {is_valid_onehot}")
                    
                    if not is_valid_onehot:
                        all_valid = False
        
        if all_valid:
            print(f"  ✅ 第{generation+1}代所有类别变量保持有效one-hot编码")
        else:
            print(f"  ❌ 第{generation+1}代存在无效的one-hot编码")
    
    return all_valid


def test_mixed_variables():
    """测试混合变量类型的正确性"""
    
    print("\n" + "=" * 50)
    print("🧪 测试混合变量类型")
    print("=" * 50)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建混合变量基准
    class MixedBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name="bin1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                Parameter(name="cont1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                Parameter(name="cat1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=1),  # 2个类别
            ]
            self.dim = 3
            self.representation_dim = 4  # 1 + 1 + 2 = 4
            self.lb_vec = torch.zeros(4, dtype=torch.float64)
            self.ub_vec = torch.ones(4, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = MixedBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=3)
    
    # 创建GA
    config = GAConfig(population_size=4, max_generations=2)
    ga = MixedVariableGA(config, axus, benchmark)
    
    print(f"二分变量索引: {ga.binary_indices}")
    print(f"连续变量索引: {ga.continuous_indices}")
    print(f"类别变量索引: {ga.categorical_indices}")
    
    # 初始化种群
    ga.initialize_population()
    
    # 进化并检查所有变量类型
    print("\n--- 混合变量进化检查 ---")
    for generation in range(2):
        print(f"\n第{generation+1}代:")
        ga.evolve_generation()
        
        for i, individual in enumerate(ga.population[:2]):
            genes = individual.genes
            
            # 检查二分变量
            if len(ga.binary_indices) > 0:
                binary_genes = genes[ga.binary_indices]
                binary_discrete = torch.all(torch.isin(binary_genes, torch.tensor([-1.0, 1.0])))
                print(f"  个体{i+1} 二分变量: {binary_genes}, 离散: {binary_discrete}")
            
            # 检查连续变量
            if len(ga.continuous_indices) > 0:
                continuous_genes = genes[ga.continuous_indices]
                in_range = torch.all((continuous_genes >= -1) & (continuous_genes <= 1))
                print(f"  个体{i+1} 连续变量: {continuous_genes}, 范围正确: {in_range}")
            
            # 检查类别变量
            for bin_obj, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
                if len(indices) > 0:
                    segment = genes[indices]
                    n_ones = torch.sum(segment == 1.0).item()
                    valid_onehot = n_ones == 1
                    print(f"  个体{i+1} 类别变量: {segment}, 有效one-hot: {valid_onehot}")
    
    print("✅ 混合变量测试完成")


if __name__ == "__main__":
    print("🚀 开始测试离散GA的正确性")
    print("=" * 60)
    
    try:
        # 测试二分变量离散性
        binary_ok = test_binary_discreteness()
        
        # 测试类别变量one-hot编码
        categorical_ok = test_categorical_onehot()
        
        # 测试混合变量
        test_mixed_variables()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   二分变量离散性: {'✅ 通过' if binary_ok else '❌ 失败'}")
        print(f"   类别变量one-hot: {'✅ 通过' if categorical_ok else '❌ 失败'}")
        print(f"   混合变量测试: ✅ 完成")
        
        if binary_ok and categorical_ok:
            print("\n🎉 所有离散性测试通过！")
            print("\n📝 修改总结:")
            print("1. ✅ 二分变量严格保持{-1, 1}值")
            print("2. ✅ 类别变量保持正确的one-hot编码")
            print("3. ✅ 交叉和变异操作保持离散性")
            print("4. ✅ 初始化种群就是离散的")
        else:
            print("\n❌ 部分测试失败，需要进一步调试")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
