#!/usr/bin/env python3
"""
测试TR中心点的离散化
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_tr_center_discretization():
    """测试TR中心点对于二分变量的离散化"""
    
    print("🧪 测试TR中心点离散化")
    print("=" * 50)
    
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建纯二分变量基准（模拟MaxSat）
    parameters = [
        Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
        for i in range(5)
    ]
    
    axus = AxUS(parameters=parameters, n_bins=5)
    
    print(f"参数类型: {[p.type for p in parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    
    # 模拟TabPFN预测的连续中心点（在[-1,1]空间）
    predicted_centers = [
        torch.tensor([ 0.0854,  0.2515, -0.0396, -0.2320, -0.0769]),
        torch.tensor([-0.9078, -0.8350,  0.2567, -0.8869, -0.2551]),
        torch.tensor([ 0.6936,  0.9025,  0.5089, -0.7134,  0.4463]),
    ]
    
    print("\n--- 测试TR中心点离散化 ---")
    
    for i, predicted_center in enumerate(predicted_centers):
        print(f"\n测试案例 {i+1}:")
        print(f"  TabPFN预测中心 ([-1,1]空间): {predicted_center}")
        
        # 转换为[0,1]空间
        center_01 = (predicted_center + 1) / 2
        print(f"  转换为[0,1]空间: {center_01}")
        
        # 对二分变量进行离散化
        center_01_discrete = center_01.clone()
        if hasattr(axus, 'bins_and_indices_of_type'):
            for _, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
                if len(indices) > 0:
                    print(f"  二分变量索引: {indices}")
                    print(f"  离散化前: {center_01_discrete[indices]}")
                    # 二分变量：四舍五入到{0,1}
                    center_01_discrete[indices] = torch.round(center_01_discrete[indices])
                    print(f"  离散化后: {center_01_discrete[indices]}")
        
        print(f"  最终TR中心: {center_01_discrete}")
        
        # 验证是否为{0,1}值
        unique_values = torch.unique(center_01_discrete)
        is_binary = torch.all(torch.isin(unique_values, torch.tensor([0.0, 1.0])))
        print(f"  是否为二分值: {is_binary}")
        print(f"  唯一值: {unique_values}")
        
        if not is_binary:
            print(f"  ❌ 错误：TR中心不是二分值！")
            return False
        else:
            print(f"  ✅ 正确：TR中心是二分值")
    
    return True


def test_mixed_variables_tr_center():
    """测试混合变量的TR中心点离散化"""
    
    print("\n" + "=" * 50)
    print("🧪 测试混合变量TR中心点离散化")
    print("=" * 50)
    
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建混合变量基准
    parameters = [
        Parameter(name="bin1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="cont1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
        Parameter(name="bin2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
        Parameter(name="cat1", type=ParameterType.CATEGORICAL, lower_bound=0, upper_bound=2),  # 3个类别
    ]
    
    axus = AxUS(parameters=parameters, n_bins=4)
    
    print(f"参数类型: {[p.type for p in parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    
    # 模拟TabPFN预测的中心点
    predicted_center = torch.tensor([0.3, -0.7, 0.8, 0.2, -0.5, 0.1])  # 6维
    
    print(f"\nTabPFN预测中心 ([-1,1]空间): {predicted_center}")
    
    # 转换为[0,1]空间
    center_01 = (predicted_center + 1) / 2
    print(f"转换为[0,1]空间: {center_01}")
    
    # 分别处理不同类型的变量
    center_01_processed = center_01.clone()
    
    # 处理二分变量
    binary_indices = []
    continuous_indices = []
    categorical_indices = []
    
    for param_type in [ParameterType.BINARY, ParameterType.CONTINUOUS, ParameterType.CATEGORICAL]:
        for _, indices in axus.bins_and_indices_of_type(param_type):
            if len(indices) > 0:
                if param_type == ParameterType.BINARY:
                    binary_indices.extend(indices.tolist())
                    print(f"二分变量索引: {indices}")
                    print(f"  离散化前: {center_01_processed[indices]}")
                    center_01_processed[indices] = torch.round(center_01_processed[indices])
                    print(f"  离散化后: {center_01_processed[indices]}")
                elif param_type == ParameterType.CONTINUOUS:
                    continuous_indices.extend(indices.tolist())
                    print(f"连续变量索引: {indices}")
                    print(f"  保持连续: {center_01_processed[indices]}")
                elif param_type == ParameterType.CATEGORICAL:
                    categorical_indices.extend(indices.tolist())
                    print(f"类别变量索引: {indices}")
                    print(f"  原始值: {center_01_processed[indices]}")
                    # 类别变量：选择最大值位置，设为1，其余设为0
                    max_idx = torch.argmax(center_01_processed[indices])
                    center_01_processed[indices] = 0.0
                    center_01_processed[indices[max_idx]] = 1.0
                    print(f"  one-hot化: {center_01_processed[indices]}")
    
    print(f"\n最终TR中心: {center_01_processed}")
    
    # 验证各类型变量
    if binary_indices:
        binary_values = center_01_processed[binary_indices]
        binary_unique = torch.unique(binary_values)
        binary_correct = torch.all(torch.isin(binary_unique, torch.tensor([0.0, 1.0])))
        print(f"二分变量正确性: {binary_correct}, 值: {binary_values}")
    
    if categorical_indices:
        cat_values = center_01_processed[categorical_indices]
        cat_sum = torch.sum(cat_values)
        cat_correct = torch.isclose(cat_sum, torch.tensor(1.0))
        print(f"类别变量正确性: {cat_correct}, 值: {cat_values}, 和: {cat_sum}")
    
    if continuous_indices:
        cont_values = center_01_processed[continuous_indices]
        print(f"连续变量值: {cont_values}")
    
    return True


def test_actual_bounce_run():
    """测试实际的Bounce运行中TR中心的离散化"""
    
    print("\n" + "=" * 50)
    print("🧪 测试实际Bounce运行")
    print("=" * 50)
    
    # 运行一个简短的Bounce测试
    import subprocess
    import tempfile
    
    try:
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.gin', delete=False) as f:
            f.write("""
# 临时测试配置
Bounce.maximum_number_evaluations = 10
Bounce.maximum_number_evaluations_until_input_dim = 5
""")
            temp_config = f.name
        
        # 运行Bounce
        cmd = [
            'python', 'main.py',
            '--gin-files', 'configs/default.gin',
            '--gin-files', temp_config,
            '--n-repeat', '1'
        ]
        
        print("运行命令:", ' '.join(cmd))
        result = subprocess.run(
            cmd,
            cwd='/mnt/c/Users/<USER>/Desktop/论文/混合变量优化/2023+NIPS+Bounce+TR+分箱/bounce-main',
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print("返回码:", result.returncode)
        
        # 查找TR中心点的日志
        lines = result.stdout.split('\n') + result.stderr.split('\n')
        tr_centers = []
        
        for line in lines:
            if "最终TR中心点:" in line:
                tr_centers.append(line)
                print(f"发现TR中心: {line}")
        
        if tr_centers:
            print(f"✅ 找到 {len(tr_centers)} 个TR中心点")
            return True
        else:
            print("❌ 未找到TR中心点日志")
            print("标准输出:")
            print(result.stdout[-1000:])  # 最后1000字符
            print("标准错误:")
            print(result.stderr[-1000:])  # 最后1000字符
            return False
            
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_config)
        except:
            pass


if __name__ == "__main__":
    print("🚀 开始测试TR中心点离散化")
    print("=" * 60)
    
    try:
        # 测试纯二分变量
        binary_ok = test_tr_center_discretization()
        
        # 测试混合变量
        mixed_ok = test_mixed_variables_tr_center()
        
        # 测试实际运行
        actual_ok = test_actual_bounce_run()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   纯二分变量TR中心: {'✅ 通过' if binary_ok else '❌ 失败'}")
        print(f"   混合变量TR中心: {'✅ 通过' if mixed_ok else '❌ 失败'}")
        print(f"   实际运行测试: {'✅ 通过' if actual_ok else '❌ 失败'}")
        
        if binary_ok and mixed_ok:
            print("\n🎉 TR中心点离散化测试通过！")
            print("\n📝 修改总结:")
            print("1. ✅ 二分变量TR中心四舍五入到{0,1}")
            print("2. ✅ 连续变量TR中心保持连续")
            print("3. ✅ 类别变量TR中心one-hot化")
            print("4. ✅ 混合变量正确处理")
        else:
            print("\n❌ 部分测试失败，需要进一步调试")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
