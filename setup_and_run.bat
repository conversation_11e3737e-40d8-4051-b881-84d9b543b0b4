@echo off
echo ================================================================
echo K-means聚类精英选择实验 - Windows环境安装和运行指南
echo ================================================================
echo.

echo 🔍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未正确安装，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 📦 安装必要的Python包...
echo 这可能需要几分钟时间，请耐心等待...

pip install scikit-learn numpy pandas gin-config
if %errorlevel% neq 0 (
    echo ❌ 包安装失败，请检查网络连接或使用国内镜像：
    echo    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple scikit-learn numpy pandas gin-config
    pause
    exit /b 1
)

echo.
echo 🧪 尝试运行简化验证...
python simple_verify_kmeans.py
if %errorlevel% neq 0 (
    echo ❌ 验证失败，请检查实现
    pause
    exit /b 1
)

echo.
echo ✅ 基础验证通过！
echo.

echo 📋 接下来你可以运行以下命令进行完整实验：
echo.
echo   对于MaxSat60问题：
echo   python run_kmeans_experiments.py --problems maxsat60 --n-repeat 3
echo.
echo   对于Ackley53问题：
echo   python run_kmeans_experiments.py --problems ackley53 --n-repeat 3
echo.
echo   运行所有实验：
echo   python run_kmeans_experiments.py --problems both --n-repeat 5
echo.

echo 🚀 实验说明：
echo   - 实现了K-means聚类精英选择机制
echo   - 聚类数量设置为精英数量的一半
echo   - 每个聚类选择最优的2个个体作为精英
echo   - 这样可以保持种群多样性，避免解的相似性过高
echo.

echo 📁 结果文件将保存在：
echo   - 当前目录：kmeans_elite_selection_results_*.csv
echo   - results/目录：详细的实验日志和结果
echo.

pause