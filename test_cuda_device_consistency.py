#!/usr/bin/env python3
"""
测试CUDA设备一致性修复效果
"""

import torch
import logging
import traceback
from bounce.benchmarks import MaxSat60
from bounce.projection import AxUS
from bounce.ga_tabpfn_integration import GATabPFNIntegration
from bounce.genetic_algorithm import GAConfig
from bounce.device_manager import DeviceManager

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_device_manager():
    """测试设备管理器功能"""
    print("\n" + "=" * 60)
    print("🧪 测试设备管理器")
    print("=" * 60)
    
    # 测试CUDA设备（如果可用）
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"目标设备: {device}")
    
    device_manager = DeviceManager(device)
    print(f"设备管理器初始化: {device_manager}")
    
    # 测试张量转换
    cpu_tensor = torch.randn(5, 10, dtype=torch.float64)
    device_tensor = device_manager.ensure_tensor_device(cpu_tensor)
    
    print(f"原始张量设备: {cpu_tensor.device}")
    print(f"转换后张量设备: {device_tensor.device}")
    print(f"设备一致性检查: {device_tensor.device == device_manager.device}")
    
    # 测试设备信息
    info = device_manager.get_device_info()
    print(f"设备信息: {info}")
    
    return device_manager.device.type == device

def test_individual_surrogate_models(device):
    """测试各个代理模型的设备一致性"""
    print("\n" + "=" * 60)
    print("🧪 测试各代理模型设备一致性")
    print("=" * 60)
    
    # 创建测试数据
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建训练数据
    X_train = torch.rand(10, benchmark.representation_dim, dtype=torch.float64)
    y_train = torch.rand(10, dtype=torch.float64)
    
    # 创建测试数据
    X_test = torch.rand(5, benchmark.representation_dim, dtype=torch.float64)
    
    print(f"测试数据形状: X_train={X_train.shape}, y_train={y_train.shape}")
    print(f"测试数据形状: X_test={X_test.shape}")
    
    # 测试各个代理模型
    from bounce.surrogate_manager import SurrogateManager
    
    surrogate_manager = SurrogateManager(benchmark, axus, device)
    
    model_types = ['gp', 'rbf', 'tabpfn']
    test_results = {}
    
    for model_type in model_types:
        print(f"\n🔧 测试 {model_type.upper()} 模型...")
        
        try:
            # 创建模型
            if model_type == 'tabpfn':
                model = surrogate_manager.create_model(model_type, n_bins=5)
            else:
                model = surrogate_manager.create_model(model_type)
            
            print(f"   ✅ {model_type.upper()}模型创建成功")
            
            # 训练模型
            model.fit(X_train, y_train)
            print(f"   ✅ {model_type.upper()}模型训练成功")
            
            # 预测
            predictions = model.predict(X_test)
            print(f"   ✅ {model_type.upper()}模型预测成功")
            print(f"   📊 预测结果设备: {predictions.device}")
            print(f"   📊 预测结果形状: {predictions.shape}")
            print(f"   📊 预测结果范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
            
            # 检查设备一致性
            device_consistent = str(predictions.device).startswith(device) or (device == 'cpu' and predictions.device.type == 'cpu')
            print(f"   🎯 设备一致性: {device_consistent}")
            
            test_results[model_type] = {
                'success': True,
                'device_consistent': device_consistent,
                'prediction_shape': predictions.shape,
                'device': str(predictions.device)
            }
            
        except Exception as e:
            print(f"   ❌ {model_type.upper()}模型测试失败: {e}")
            print(f"   📜 错误详情: {traceback.format_exc()}")
            test_results[model_type] = {
                'success': False,
                'error': str(e)
            }
    
    return test_results

def test_ensemble_manager(device):
    """测试集成管理器设备一致性"""
    print("\n" + "=" * 60)
    print("🧪 测试集成管理器设备一致性")
    print("=" * 60)
    
    # 创建测试数据
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    from bounce.ensemble_manager import EnsembleManager
    
    try:
        # 创建集成管理器
        ensemble_manager = EnsembleManager(
            benchmark=benchmark,
            axus=axus,
            device=device,
            model_types=['gp', 'rbf', 'tabpfn'],
            weight_window_size=5,
            min_weight_protection=0.1,
            top_k_candidates=3
        )
        print(f"✅ 集成管理器创建成功，设备: {ensemble_manager.device_manager.device}")
        
        # 创建训练数据
        X_train = torch.rand(8, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(8, dtype=torch.float64)
        
        # 训练集成模型
        ensemble_manager.fit(X_train, y_train)
        print("✅ 集成模型训练成功")
        
        # 创建测试数据
        X_test = torch.rand(5, benchmark.representation_dim, dtype=torch.float64)
        
        # 测试所有模型预测
        predictions_dict = ensemble_manager.predict_all_models(X_test)
        print(f"✅ 集成预测成功，获得{len(predictions_dict)}个模型的预测")
        
        # 检查每个模型的设备一致性
        device_results = {}
        for model_name, predictions in predictions_dict.items():
            device_consistent = str(predictions.device).startswith(device) or (device == 'cpu' and predictions.device.type == 'cpu')
            device_results[model_name] = {
                'device': str(predictions.device),
                'device_consistent': device_consistent,
                'shape': predictions.shape,
                'range': f"[{predictions.min():.4f}, {predictions.max():.4f}]"
            }
            print(f"   📊 {model_name.upper()}: 设备={predictions.device}, 一致性={device_consistent}")
        
        # 测试候选点选择
        candidates_low = torch.rand(5, axus.target_dim, dtype=torch.float64) * 2 - 1  # [-1, 1] 范围
        best_center = ensemble_manager.predict_best_center(
            candidates=candidates_low,
            existing_X=X_train,
            existing_y=y_train
        )
        print(f"✅ 候选点选择成功: 设备={best_center.device}, 形状={best_center.shape}")
        
        device_consistent = str(best_center.device).startswith(device) or (device == 'cpu' and best_center.device.type == 'cpu')
        
        return {
            'success': True,
            'device_consistent': device_consistent,
            'model_results': device_results,
            'best_center_device': str(best_center.device)
        }
        
    except Exception as e:
        print(f"❌ 集成管理器测试失败: {e}")
        print(f"📜 错误详情: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e)
        }

def test_ga_tabpfn_integration(device):
    """测试GA-TabPFN集成系统设备一致性"""
    print("\n" + "=" * 60)
    print("🧪 测试GA-TabPFN集成系统设备一致性")
    print("=" * 60)
    
    # 创建测试数据
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    try:
        # 创建GA配置
        ga_config = GAConfig(population_size=10, max_generations=2)
        
        # 创建GA-TabPFN集成（集成模式）
        ga_integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=ga_config,
            device=device,
            enable_ensemble=True  # 启用集成模式
        )
        print(f"✅ GA-TabPFN集成系统创建成功，集成模式: {ga_integration.enable_ensemble}")
        print(f"   🎯 设备: {device}")
        
        # 创建训练数据
        X_train = torch.rand(10, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(10, dtype=torch.float64)
        
        # 测试最佳中心点预测
        best_center = ga_integration.predict_best_center_with_tabpfn(
            existing_X=X_train,
            existing_y=y_train,
            n_candidates=5
        )
        print(f"✅ 最佳中心点预测成功: 设备={best_center.device}, 形状={best_center.shape}")
        
        device_consistent = str(best_center.device).startswith(device) or (device == 'cpu' and best_center.device.type == 'cpu')
        
        # 测试全局搜索
        best_low_dim, best_high_dim = ga_integration.run_global_search(X_train, y_train)
        print(f"✅ 全局搜索成功: 低维={best_low_dim.device}, 高维={best_high_dim.device}")
        
        low_dim_consistent = str(best_low_dim.device).startswith(device) or (device == 'cpu' and best_low_dim.device.type == 'cpu')
        high_dim_consistent = str(best_high_dim.device).startswith(device) or (device == 'cpu' and best_high_dim.device.type == 'cpu')
        
        return {
            'success': True,
            'device_consistent': device_consistent and low_dim_consistent and high_dim_consistent,
            'best_center_device': str(best_center.device),
            'best_low_dim_device': str(best_low_dim.device),
            'best_high_dim_device': str(best_high_dim.device)
        }
        
    except Exception as e:
        print(f"❌ GA-TabPFN集成系统测试失败: {e}")
        print(f"📜 错误详情: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主测试函数"""
    print("🚀 多代理模型集成CUDA设备一致性测试")
    print("=" * 80)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    device = 'cuda' if cuda_available else 'cpu'
    
    print(f"CUDA可用性: {cuda_available}")
    print(f"测试设备: {device}")
    
    if cuda_available:
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        print(f"当前CUDA设备: {torch.cuda.current_device()}")
        print(f"CUDA设备名称: {torch.cuda.get_device_name()}")
    
    # 执行各项测试
    test_results = {}
    
    # 1. 测试设备管理器
    test_results['device_manager'] = test_device_manager()
    
    # 2. 测试各代理模型
    test_results['surrogate_models'] = test_individual_surrogate_models(device)
    
    # 3. 测试集成管理器
    test_results['ensemble_manager'] = test_ensemble_manager(device)
    
    # 4. 测试GA-TabPFN集成系统
    test_results['ga_tabpfn_integration'] = test_ga_tabpfn_integration(device)
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    overall_success = True
    
    for test_name, result in test_results.items():
        if isinstance(result, dict):
            success = result.get('success', False)
            device_consistent = result.get('device_consistent', False)
            status = "✅ 通过" if success and device_consistent else "❌ 失败"
            print(f"{test_name:25s}: {status}")
            
            if not (success and device_consistent):
                overall_success = False
                if 'error' in result:
                    print(f"  错误: {result['error']}")
        else:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:25s}: {status}")
            if not result:
                overall_success = False
    
    print("\n" + "=" * 80)
    if overall_success:
        print("🎉 所有测试通过！CUDA设备一致性问题已修复。")
    else:
        print("⚠️  部分测试失败，需要进一步修复。")
    print("=" * 80)
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)