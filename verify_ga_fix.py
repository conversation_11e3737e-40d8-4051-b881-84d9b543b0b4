#!/usr/bin/env python3
"""
简单验证混合变量修复效果
"""

def check_genetic_algorithm_fix():
    """检查遗传算法修复"""
    print("🔍 检查遗传算法代码修复")
    print("=" * 50)
    
    # 读取修复后的代码
    with open('bounce/genetic_algorithm.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复点
    fixes_found = []
    
    # 1. 检查混合变量初始化修复
    if "# 🎯 混合变量问题：分别处理不同类型的变量" in content:
        fixes_found.append("✅ 混合变量初始化修复")
        print("✅ 找到混合变量初始化修复")
    else:
        print("❌ 未找到混合变量初始化修复")
    
    # 2. 检查二分变量严格生成
    if "binary_values = torch.randint(0, 2," in content:
        fixes_found.append("✅ 二分变量严格生成")
        print("✅ 找到二分变量严格{-1,1}生成")
    else:
        print("❌ 未找到二分变量严格生成")
    
    # 3. 检查连续变量单独处理
    if "genes[self.continuous_indices] = torch.rand" in content:
        fixes_found.append("✅ 连续变量单独处理")
        print("✅ 找到连续变量单独处理")
    else:
        print("❌ 未找到连续变量单独处理")
    
    # 4. 检查交叉操作修复
    if "🎯 关键修复：确保交叉后二分变量仍为{-1,1}值" in content:
        fixes_found.append("✅ 交叉操作修复")
        print("✅ 找到交叉操作修复")
    else:
        print("❌ 未找到交叉操作修复")
    
    # 5. 检查变异操作修复
    if "🎯 关键修复：确保位翻转后仍为{-1,1}值" in content:
        fixes_found.append("✅ 变异操作修复")
        print("✅ 找到变异操作修复")
    else:
        print("❌ 未找到变异操作修复")
    
    print(f"\n修复检查结果: {len(fixes_found)}/5")
    
    return len(fixes_found) == 5

def check_fix_logic():
    """检查修复逻辑的正确性"""
    print("\n🔍 检查修复逻辑正确性")
    print("=" * 50)
    
    with open('bounce/genetic_algorithm.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    issues_found = []
    
    # 查找初始化部分
    in_initialize = False
    in_else_block = False
    
    for i, line in enumerate(lines):
        if "def initialize_population(self)" in line:
            in_initialize = True
        elif in_initialize and "def " in line and "initialize_population" not in line:
            break
        
        if in_initialize:
            # 检查else块（混合变量处理）
            if "else:" in line and "混合变量问题" in lines[i+1] if i+1 < len(lines) else False:
                in_else_block = True
                print(f"✅ 找到混合变量处理else块 (第{i+1}行)")
            
            # 在else块中检查具体实现
            if in_else_block:
                if "genes = torch.zeros(self.axus.target_dim" in line:
                    print(f"✅ 找到基因初始化为零 (第{i+1}行)")
                
                if "binary_values = torch.randint(0, 2," in line:
                    print(f"✅ 找到二分变量随机生成 (第{i+1}行)")
                
                if "genes[self.binary_indices] = binary_values * 2 - 1" in line:
                    print(f"✅ 找到二分变量{-1,1}转换 (第{i+1}行)")
                
                if "genes[self.continuous_indices] = torch.rand" in line:
                    print(f"✅ 找到连续变量单独生成 (第{i+1}行)")
    
    print("\n修复逻辑检查完成")
    return True

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n🔍 分析潜在问题")
    print("=" * 50)
    
    print("📋 修复效果预期:")
    print("1. Ackley53问题的前3个维度应该是连续值 [-1, 1]")
    print("2. Ackley53问题的后50个维度应该严格是{-1, 1}值")
    print("3. GA初始化时不再对所有维度使用torch.rand")
    print("4. 交叉和变异操作保持变量类型正确性")
    
    print("\n🎯 这个修复应该解决的问题:")
    print("- 原问题: 集成模型预测的TR中心有连续值在二分维度")
    print("- 根本原因: GA生成候选点时对二分变量也生成了连续值")
    print("- 修复方案: GA初始化时分别处理不同类型的变量")
    
    print("\n✅ 修复完成，可以重新运行Ackley53测试")

def main():
    """主函数"""
    print("🔧 混合变量修复验证")
    print("=" * 80)
    
    # 检查修复
    fix_valid = check_genetic_algorithm_fix()
    
    # 检查逻辑
    logic_valid = check_fix_logic()
    
    # 分析问题
    analyze_potential_issues()
    
    print("\n" + "=" * 80)
    if fix_valid:
        print("🎉 遗传算法混合变量修复验证通过！")
        print("💡 现在可以重新运行Ackley53问题，应该能看到正确的变量类型。")
        print("🔧 二分变量将严格保持{-1,1}值，连续变量保持连续性。")
    else:
        print("⚠️ 修复验证失败，请检查代码修改。")
    
    print("=" * 80)

if __name__ == "__main__":
    main()