2025-09-02 13:08:26,441 - DEBUG - Parameter type ParameterType.BINARY gets 3/3 bins.
2025-09-02 13:08:26,442 - DEBUG - Total number of bins: 3
2025-09-02 13:08:26,443 - DEBUG - Parameter type ParameterType.BINARY gets 3/3 bins.
2025-09-02 13:08:26,443 - DEBUG - Total number of bins: 3
2025-09-02 13:08:26,443 - INFO - GP全局代理模型初始化完成，参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True}
2025-09-02 13:08:26,444 - INFO - 创建GP代理模型，参数: {}
2025-09-02 13:08:26,444 - INFO - ✅ GP模型初始化完成
2025-09-02 13:08:26,444 - INFO - RBF全局代理模型初始化完成，核函数: gaussian, epsilon: 1.0
2025-09-02 13:08:26,444 - INFO - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0
2025-09-02 13:08:26,444 - INFO - ✅ RBF模型初始化完成
2025-09-02 13:08:26,444 - INFO - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 3, 类别变量: 0
2025-09-02 13:08:26,445 - INFO - 创建TabPFN代理模型，n_bins: 5
2025-09-02 13:08:26,445 - INFO - ✅ TabPFN模型初始化完成
2025-09-02 13:08:26,445 - INFO - 🌟 集成代理模型管理器初始化完成
2025-09-02 13:08:26,445 - INFO -    支持模型: ['gp', 'rbf', 'tabpfn']
2025-09-02 13:08:26,445 - INFO -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333}
2025-09-02 13:08:26,446 - DEBUG - 生成了5个候选点
2025-09-02 13:08:26,449 - DEBUG - 🌟 开始集成代理模型预测TR中心...
2025-09-02 13:08:26,701 - INFO - GP模型训练完成，训练样本数: 5, 输入维度: 3
2025-09-02 13:08:26,701 - INFO - GP模型训练完成
2025-09-02 13:08:26,701 - DEBUG - ✅ GP模型更新完成
2025-09-02 13:08:26,704 - INFO - RBF模型训练完成，训练样本数: 5
2025-09-02 13:08:26,704 - INFO - RBF模型训练完成
2025-09-02 13:08:26,704 - DEBUG - ✅ RBF模型更新完成
2025-09-02 13:08:26,740 - DEBUG - `use_flash_attention` was specified in the config. This will be ignored and the attention implementation selected automatically.
2025-09-02 13:08:26,740 - DEBUG - Keys in config that were not parsed by architecture config: seq_len, aggregate_k_gradients, progress_bar, timing, num_global_att_tokens, adaptive_max_seq_len_to_max_full_table_size, batch_size, max_num_features, semisupervised_enabled, differentiable_hps_as_style, task_type, multi_query_factor
2025-09-02 13:08:26,901 - INFO - TabPFN模型训练完成，训练样本数: 5, 类别数: 2
2025-09-02 13:08:26,901 - INFO - TABPFN模型训练完成
2025-09-02 13:08:26,902 - DEBUG - ✅ TABPFN模型更新完成
/home/<USER>/.cache/pypoetry/virtualenvs/bounce-ykOO-UcJ-py3.10/lib/python3.10/site-packages/botorch/models/utils/assorted.py:201: InputDataWarning: Input data is not standardized. Please consider scaling the input to zero mean and unit variance.
  warnings.warn(msg, InputDataWarning)
2025-09-02 13:08:26,971 - INFO - GP模型训练完成，训练样本数: 2, 输入维度: 3
2025-09-02 13:08:26,976 - INFO - RBF模型训练完成，训练样本数: 2
2025-09-02 13:08:26,992 - DEBUG - `use_flash_attention` was specified in the config. This will be ignored and the attention implementation selected automatically.
2025-09-02 13:08:26,992 - DEBUG - Keys in config that were not parsed by architecture config: seq_len, aggregate_k_gradients, progress_bar, timing, num_global_att_tokens, adaptive_max_seq_len_to_max_full_table_size, batch_size, max_num_features, semisupervised_enabled, differentiable_hps_as_style, task_type, multi_query_factor
2025-09-02 13:08:27,074 - INFO - TabPFN模型训练完成，训练样本数: 2, 类别数: 2
2025-09-02 13:08:27,075 - DEBUG - TABPFN权重计算失败: 'TabPFNGlobalSurrogate' object has no attribute 'predict'
2025-09-02 13:08:27,075 - DEBUG - 📊 权重更新: gp:0.500 | rbf:0.500 | tabpfn:0.000
2025-09-02 13:08:27,075 - DEBUG - 开始收集候选点评分，候选点数量: 5
2025-09-02 13:08:27,077 - DEBUG - 投影后高维候选点数量: 5
2025-09-02 13:08:27,079 - DEBUG - 🔍 GP选择了5个候选点
2025-09-02 13:08:27,079 - DEBUG - 🔍 RBF选择了5个候选点
2025-09-02 13:08:28,092 - DEBUG - 🔍 TABPFN选择了5个候选点
2025-09-02 13:08:28,092 - DEBUG - 🎯 Softmax选择: 候选2 (idx=2), 分数=0.5000
2025-09-02 13:08:28,093 - INFO - 🌟 集成模型选择中心: tensor([-1., -1.,  1.], dtype=torch.float64)...
2025-09-02 13:08:28,093 - INFO -    当前权重: gp:0.500 | rbf:0.500 | tabpfn:0.000
🔍 开始调试集成代理模型...
基准设置 - 参数数: 3, 目标维度: 3
✅ 集成管理器创建完成

🔍 测试候选点生成...
✅ 生成候选点成功，形状: torch.Size([5, 3])
   候选点示例: tensor([-1.,  1.,  1.], dtype=torch.float64)

🔍 测试投影到高维...
✅ 投影成功，高维形状: torch.Size([5, 3])
   高维示例: tensor([1., 1., 0.], dtype=torch.float64)

🔍 准备测试数据...
训练数据形状: torch.Size([5, 3])

🔍 测试完整集成预测...
✅ 集成预测成功!
   预测中心: tensor([-1., -1.,  1.], dtype=torch.float64)
   中心形状: torch.Size([3])

🎉 调试成功！
