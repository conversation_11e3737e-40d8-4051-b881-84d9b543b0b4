#!/usr/bin/env python3
"""
权重动态调整效果验证脚本
测试新的超敏感权重更新算法是否能正确响应MAE差异
"""

import torch
import logging
import sys
import os

# 设置日志级别为INFO以查看权重变化
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_weight_sensitivity():
    """测试权重敏感度"""
    print("🔬 测试权重动态调整敏感度")
    print("=" * 60)
    
    try:
        from bounce.benchmarks import Ackley53
        from bounce.projection import AxUS
        from bounce.ensemble_manager import EnsembleManager
        
        # 创建测试环境
        benchmark = Ackley53()
        axus = AxUS(parameters=benchmark.parameters, n_bins=5)
        
        ensemble_manager = EnsembleManager(
            benchmark=benchmark,
            axus=axus,
            device='cpu',
            model_types=['gp', 'rbf', 'tabpfn'],
            weight_window_size=10,
            min_weight_protection=0.05,  # 降低最小权重保护
            top_k_candidates=3
        )
        
        print(f"✅ 集成管理器创建成功，最小权重保护: {ensemble_manager.weight_manager.min_weight_protection}")
        
        # 创建训练数据
        X_train = torch.rand(12, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(12, dtype=torch.float64) * 200 - 100  # [-100, 100]范围
        
        print(f"📊 训练数据: X={X_train.shape}, y范围=[{y_train.min():.2f}, {y_train.max():.2f}]")
        
        # 训练集成模型
        ensemble_manager.fit(X_train, y_train)
        
        # 获取初始权重
        initial_weights = ensemble_manager.weight_manager.get_weights()
        print(f"\n🎯 初始权重: {initial_weights}")
        
        # 模拟多轮性能记录，创造明显的MAE差异
        print(f"\n🔄 第1轮性能记录 - 模拟TabPFN表现优异")
        X_test1 = torch.rand(3, benchmark.representation_dim, dtype=torch.float64)
        
        # 手动创建有差异的预测结果
        predictions_dict1 = {
            'gp': torch.tensor([50.0, 60.0, 70.0], dtype=torch.float64),      # GP预测不准
            'rbf': torch.tensor([45.0, 65.0, 75.0], dtype=torch.float64),     # RBF预测也不准  
            'tabpfn': torch.tensor([20.0, 25.0, 30.0], dtype=torch.float64)   # TabPFN预测很准
        }
        actual_values1 = torch.tensor([20.0, 25.0, 30.0], dtype=torch.float64)  # TabPFN完全准确
        
        print(f"   📈 真实值: {actual_values1.tolist()}")
        print(f"   📈 GP预测:  {predictions_dict1['gp'].tolist()}")
        print(f"   📈 RBF预测: {predictions_dict1['rbf'].tolist()}")
        print(f"   📈 TabPFN预测: {predictions_dict1['tabpfn'].tolist()}")
        
        # 记录性能
        ensemble_manager.record_performance(predictions_dict1, actual_values1)
        
        # 检查权重变化
        round1_weights = ensemble_manager.weight_manager.get_weights()
        print(f"\n🎯 第1轮后权重: {round1_weights}")
        
        # 检查MAE
        for model_name in ensemble_manager.model_types:
            mae = ensemble_manager.weight_manager.calculate_mae(model_name)
            history_count = len(ensemble_manager.weight_manager.performance_history[model_name])
            print(f"   📊 {model_name.upper()}: MAE={mae:.4f}, 数据={history_count}条")
        
        # 第2轮：继续强化差异
        print(f"\n🔄 第2轮性能记录 - 继续强化TabPFN的优势")
        
        predictions_dict2 = {
            'gp': torch.tensor([80.0, 90.0, 100.0], dtype=torch.float64),     # GP继续预测不准
            'rbf': torch.tensor([75.0, 85.0, 95.0], dtype=torch.float64),     # RBF继续预测不准
            'tabpfn': torch.tensor([40.0, 50.0, 60.0], dtype=torch.float64)   # TabPFN继续很准
        }
        actual_values2 = torch.tensor([40.0, 50.0, 60.0], dtype=torch.float64)
        
        print(f"   📈 真实值: {actual_values2.tolist()}")
        print(f"   📈 GP预测:  {predictions_dict2['gp'].tolist()}")
        print(f"   📈 RBF预测: {predictions_dict2['rbf'].tolist()}")
        print(f"   📈 TabPFN预测: {predictions_dict2['tabpfn'].tolist()}")
        
        ensemble_manager.record_performance(predictions_dict2, actual_values2)
        
        round2_weights = ensemble_manager.weight_manager.get_weights()
        print(f"\n🎯 第2轮后权重: {round2_weights}")
        
        # 检查MAE
        for model_name in ensemble_manager.model_types:
            mae = ensemble_manager.weight_manager.calculate_mae(model_name)
            history_count = len(ensemble_manager.weight_manager.performance_history[model_name])
            print(f"   📊 {model_name.upper()}: MAE={mae:.4f}, 数据={history_count}条")
        
        # 第3轮：极端差异测试
        print(f"\n🔄 第3轮性能记录 - 极端差异测试")
        
        predictions_dict3 = {
            'gp': torch.tensor([200.0, 250.0, 300.0], dtype=torch.float64),   # GP极度不准
            'rbf': torch.tensor([180.0, 220.0, 280.0], dtype=torch.float64),  # RBF也极度不准
            'tabpfn': torch.tensor([10.0, 15.0, 20.0], dtype=torch.float64)   # TabPFN极准
        }
        actual_values3 = torch.tensor([10.0, 15.0, 20.0], dtype=torch.float64)
        
        print(f"   📈 真实值: {actual_values3.tolist()}")
        print(f"   📈 GP预测:  {predictions_dict3['gp'].tolist()}")
        print(f"   📈 RBF预测: {predictions_dict3['rbf'].tolist()}")
        print(f"   📈 TabPFN预测: {predictions_dict3['tabpfn'].tolist()}")
        
        ensemble_manager.record_performance(predictions_dict3, actual_values3)
        
        final_weights = ensemble_manager.weight_manager.get_weights()
        print(f"\n🎯 最终权重: {final_weights}")
        
        # 最终MAE统计
        print(f"\n📊 最终MAE统计:")
        final_maes = {}
        for model_name in ensemble_manager.model_types:
            mae = ensemble_manager.weight_manager.calculate_mae(model_name)
            history_count = len(ensemble_manager.weight_manager.performance_history[model_name])
            final_maes[model_name] = mae
            print(f"   📊 {model_name.upper()}: MAE={mae:.4f}, 数据={history_count}条")
        
        # 验证权重响应是否正确
        print(f"\n🔍 权重响应分析:")
        
        # 计算权重变化幅度
        initial_std = torch.std(torch.tensor(list(initial_weights.values())))
        final_std = torch.std(torch.tensor(list(final_weights.values())))
        
        print(f"   📈 权重分散度: 初始={initial_std:.5f} → 最终={final_std:.5f}")
        
        # 检查TabPFN是否获得最高权重
        best_model = max(final_weights, key=final_weights.get)
        best_weight = final_weights[best_model]
        
        # 检查MAE最低的模型是否获得最高权重
        best_mae_model = min(final_maes, key=final_maes.get)
        
        print(f"   🏆 最高权重模型: {best_model.upper()} ({best_weight:.4f})")
        print(f"   🎯 最低MAE模型: {best_mae_model.upper()} ({final_maes[best_mae_model]:.4f})")
        
        # 验证结果
        success_criteria = []
        
        # 1. 权重应该显著变化（不再是1/3）
        weight_changed = abs(final_weights['tabpfn'] - 0.3333) > 0.05
        success_criteria.append(("权重显著变化", weight_changed))
        
        # 2. TabPFN应该获得最高权重
        tabpfn_highest = best_model == 'tabpfn'
        success_criteria.append(("TabPFN获得最高权重", tabpfn_highest))
        
        # 3. 最低MAE的模型应该获得最高权重
        mae_weight_match = best_model == best_mae_model
        success_criteria.append(("MAE-权重匹配", mae_weight_match))
        
        # 4. 权重分散度应该增加（不再均匀）
        weight_diversity = final_std > initial_std * 1.5
        success_criteria.append(("权重分散度增加", weight_diversity))
        
        print(f"\n✅ 验证结果:")
        all_passed = True
        for criterion, passed in success_criteria:
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}: {passed}")
            all_passed = all_passed and passed
        
        return all_passed
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 权重动态调整敏感度验证")
    print("=" * 80)
    
    success = test_weight_sensitivity()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 权重动态调整验证通过！")
        print("💡 新的超敏感权重算法正确响应MAE差异")
        print("🔧 权重系统现在能够：")
        print("   - 检测微小的性能差异（0.1%阈值）")
        print("   - 使用指数级权重调整算法")
        print("   - 给最佳模型显著更高的权重")
        print("   - 实时监控和记录性能状态")
    else:
        print("⚠️ 权重动态调整验证失败，需要进一步优化。")
    
    print("=" * 80)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)