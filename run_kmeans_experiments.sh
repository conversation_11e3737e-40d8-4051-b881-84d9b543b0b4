#!/bin/bash

# K-means聚类精英选择实验运行脚本（WSL环境）

echo "🧬 K-means聚类精英选择实验 - WSL环境"
echo "================================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未找到，请安装Python3"
    exit 1
fi

# 检查必要的Python包
echo "🔍 检查Python依赖..."
python3 -c "import sklearn, torch, numpy, pandas, gin" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要的Python包，请安装: scikit-learn torch numpy pandas gin-config"
    echo "   可以使用以下命令安装:"
    echo "   pip install scikit-learn torch numpy pandas gin-config"
    exit 1
fi

echo "✅ Python依赖检查通过"

# 创建结果目录
mkdir -p results
echo "📁 结果目录已准备"

# 首先运行测试脚本
echo ""
echo "🧪 运行K-means精英选择机制测试..."
python3 test_kmeans_elite_selection.py

if [ $? -eq 0 ]; then
    echo "✅ 测试通过，开始正式实验"
    
    echo ""
    echo "🚀 运行MaxSat60问题实验..."
    python3 run_kmeans_experiments.py --problems maxsat60 --n-repeat 3
    
    echo ""
    echo "🚀 运行Ackley53问题实验..."
    python3 run_kmeans_experiments.py --problems ackley53 --n-repeat 3
    
    echo ""
    echo "🎉 所有实验完成！"
    echo "📊 结果文件保存在当前目录和results/目录中"
    
    # 显示结果文件
    echo ""
    echo "📋 生成的结果文件："
    ls -la kmeans_elite_selection_results_*.csv 2>/dev/null || echo "   (结果文件将在实验完成后生成)"
    ls -la results/ 2>/dev/null || echo "   (详细结果在results/目录中)"
    
else
    echo "❌ 测试失败，请检查代码和配置"
    exit 1
fi

echo ""
echo "🏁 实验脚本执行完成"