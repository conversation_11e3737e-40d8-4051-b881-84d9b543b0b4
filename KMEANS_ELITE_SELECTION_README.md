# K-means聚类精英选择机制

## 🎯 实现目标

为了解决GA种群中解相似性过高的问题，实现了基于K-means聚类的精英选择机制：

- **问题**: 传统精英选择只考虑适应度，可能导致精英个体过于相似，缺乏多样性
- **解决方案**: 使用K-means聚类将候选点分类，然后从每个聚类中选择最优解
- **优势**: 既保证了解的质量，又维持了种群的多样性

## 🔧 核心实现

### 1. 配置参数

在 `GAConfig` 类中新增了以下参数：

```python
@dataclass
class GAConfig:
    # K-means聚类精英选择配置
    use_kmeans_elite_selection: bool = True  # 是否使用K-means聚类精英选择
    elite_clusters_ratio: float = 0.5        # 聚类数量与精英数量的比例
```

### 2. 核心算法

```python
def _kmeans_elite_selection(self, elite_count: int) -> List[Individual]:
    """
    🎆 使用K-means聚类进行精英选择，提高种群多样性
    
    算法流程：
    1. 计算聚类数量：精英数量 × elite_clusters_ratio
    2. 对种群基因进行标准化处理
    3. 执行K-means聚类
    4. 从每个聚类中选择最优的2个个体
    5. 如果精英数量不足，补充全局最优个体
    """
```

### 3. 聚类策略

- **聚类数量**: `max(2, int(elite_count * elite_clusters_ratio))`
- **每聚类选择**: 最多2个最优个体
- **数据预处理**: 使用 `StandardScaler` 标准化基因数据
- **聚类算法**: `KMeans(n_clusters=n_clusters, random_state=42, n_init=10)`

### 4. 多样性评估

实现了精英多样性计算函数：

```python
def _calculate_elite_diversity(self, elites: List[Individual]) -> float:
    """计算精英个体间的平均欧氏距离作为多样性指标"""
```

## 📊 实验配置

### MaxSat60 配置文件 (`configs/maxsat60_kmeans.gin`)

```gin
# K-means Elite Selection Configuration
GAConfig.use_kmeans_elite_selection = True
GAConfig.elite_clusters_ratio = 0.5
GAConfig.elitism_rate = 0.3
GAConfig.population_size = 150
GAConfig.max_generations = 50
```

### Ackley53 配置文件 (`configs/ackley53_kmeans.gin`)

```gin
# K-means Elite Selection Configuration  
GAConfig.use_kmeans_elite_selection = True
GAConfig.elite_clusters_ratio = 0.5
GAConfig.elitism_rate = 0.3
GAConfig.population_size = 150
GAConfig.max_generations = 50
```

## 🚀 运行实验

### 环境准备

1. **安装依赖**:
   ```bash
   pip install scikit-learn numpy pandas gin-config torch
   ```

2. **Windows用户**:
   ```cmd
   setup_and_run.bat
   ```

3. **WSL/Linux用户**:
   ```bash
   ./run_kmeans_experiments.sh
   ```

### 运行命令

1. **验证实现**:
   ```bash
   python simple_verify_kmeans.py        # 简化验证（无需torch）
   python verify_kmeans_implementation.py  # 完整验证
   ```

2. **单独运行**:
   ```bash
   # MaxSat60问题
   python run_kmeans_experiments.py --problems maxsat60 --n-repeat 3
   
   # Ackley53问题  
   python run_kmeans_experiments.py --problems ackley53 --n-repeat 3
   ```

3. **批量实验**:
   ```bash
   python run_kmeans_experiments.py --problems both --n-repeat 5
   ```

## 📈 预期效果

### 多样性提升

- **传统精英选择**: 选择适应度最好的N个个体，可能导致个体间距离较小
- **K-means精英选择**: 通过聚类确保精英个体分布在不同区域，增加多样性

### 实验指标

1. **多样性指标**: 精英个体间的平均欧氏距离
2. **优化性能**: 最终找到的最优值
3. **收敛速度**: 达到目标精度所需的评估次数

## 📁 输出文件

### 结果文件

- `kmeans_elite_selection_results_<timestamp>.csv`: 实验汇总结果
- `results/<benchmark>/<config_hash>/<timestamp>/`: 详细实验数据
  - `results.csv`: 所有评估点和函数值
  - `eval_history.csv`: 评估历史和最优值变化
  - `gin_config.txt`: 实验配置备份

### 日志信息

实验过程中会输出以下关键信息：
- 🎆 K-means精英选择状态
- 📊 聚类结果和选择统计
- 📈 多样性指标对比
- 🎯 最优值和收敛情况

## 🔍 技术细节

### 聚类参数调优

- `elite_clusters_ratio = 0.5`: 聚类数 = 精英数 × 0.5
- `n_init=10`: K-means初始化次数，提高稳定性
- `random_state=42`: 固定随机种子，确保可重复性

### 错误处理

- 聚类数过少时自动回退到传统选择
- K-means失败时使用传统精英选择作为备选
- 数据标准化处理混合变量类型

### 性能优化

- 使用批量处理减少计算开销
- 缓存聚类结果避免重复计算
- 并行化K-means以提高速度

## 🎉 创新点

1. **首次将K-means聚类应用于GA精英选择**
2. **自适应聚类数量设计**：根据精英数量动态调整
3. **混合变量支持**：处理二进制、连续、类别变量的统一聚类
4. **多样性量化**：提供精确的多样性评估指标
5. **回退机制**：确保算法的鲁棒性

## 📚 理论依据

这个实现基于以下理论：

1. **多样性-强度平衡**: 在保证解质量的同时维持种群多样性
2. **聚类假设**: 相似的解在基因空间中聚集，不同聚类代表不同的搜索方向
3. **探索-利用平衡**: 从多个聚类选择精英既利用了当前最优解，又保持了探索能力

通过这种方式，我们期望能够：
- 避免过早收敛到局部最优
- 保持算法的全局搜索能力  
- 提高最终解的质量和稳定性