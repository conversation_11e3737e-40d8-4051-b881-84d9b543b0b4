#!/usr/bin/env python3
"""
测试每次迭代都进行GA+TabPFN的新模式
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import gin
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_every_iteration_ga_mode():
    """测试每次迭代都进行GA+TabPFN的模式"""
    
    print("🚀 测试每次迭代都进行GA+TabPFN的新模式")
    print("=" * 60)
    
    # 模拟TabPFN以避免网络调用
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        # 模拟预测概率：随机返回不同的概率分布
        mock_instance.predict_proba.return_value = [
            [0.6, 0.3, 0.1],  # 候选点1：较好
            [0.2, 0.5, 0.3],  # 候选点2：中等
            [0.1, 0.2, 0.7],  # 候选点3：较差
        ]
        mock_classifier.return_value = mock_instance
        
        # 清除之前的gin配置
        gin.clear_config()
        
        # 导入必要的模块
        from bounce.bounce import Bounce
        from bounce.benchmarks import MaxSat60
        
        # 配置Bounce算法参数（小规模测试）
        gin.parse_config([
            "Bounce.number_initial_points = 3",
            "Bounce.initial_target_dimensionality = 3", 
            "Bounce.number_new_bins_on_split = 1",
            "Bounce.maximum_number_evaluations = 15",  # 小规模测试
            "Bounce.batch_size = 1",
            "Bounce.results_dir = 'test_results_every_iter'",
            "Bounce.device = 'cpu'",
            "Bounce.dtype = 'float64'",
            "Bounce.use_scipy_lbfgs = False",
            "Bounce.maximum_number_evaluations_until_input_dim = 12"
        ])
        
        # 创建基准函数
        benchmark = MaxSat60()
        
        # 创建Bounce实例
        bounce = Bounce(benchmark=benchmark)
        
        print(f"📊 基准函数: {benchmark.__class__.__name__}")
        print(f"📏 问题维度: {benchmark.dim}")
        print(f"⚙️ 每次迭代都进行GA: {bounce.enable_ga_every_iteration}")
        print(f"⚙️ TabPFN最少数据点: {bounce.min_data_for_tabpfn}")
        
        # 手动执行几次迭代来观察行为
        bounce.sample_init()
        print(f"\n✅ 初始采样完成，评估次数: {bounce._n_evals}")
        
        # 记录每次迭代的GA状态
        ga_generations_history = []
        best_fitness_history = []
        
        # 模拟主循环的几次迭代
        max_test_iterations = 8
        iteration = 0
        
        while bounce._n_evals < bounce.maximum_number_evaluations and iteration < max_test_iterations:
            iteration += 1
            print(f"\n--- 迭代 {iteration} (评估次数: {bounce._n_evals}) ---")
            
            # 检查是否会触发GA
            will_trigger_ga = (
                bounce.enable_ga_every_iteration and 
                len(bounce.x_up_global) >= bounce.min_data_for_tabpfn
            )
            print(f"🧬 是否触发GA: {will_trigger_ga}")
            
            if will_trigger_ga:
                # 记录GA状态
                ga_info = bounce.ga_tabpfn_integration.get_integration_info()
                current_generation = ga_info['ga_info']['current_generation']
                current_best_fitness = ga_info['ga_info']['best_fitness']
                
                print(f"   GA当前代数: {current_generation}")
                print(f"   GA最佳适应度: {current_best_fitness}")
                
                ga_generations_history.append(current_generation)
                best_fitness_history.append(current_best_fitness)
            
            # 添加一个随机点来继续迭代（简化测试）
            axus = bounce.random_embedding
            random_point_low = torch.rand(axus.target_dim, dtype=torch.float64) * 2 - 1
            
            # 转换到高维并评估
            from bounce.util.data_handling import from_1_around_origin
            random_point_01 = (random_point_low + 1) / 2
            high_dim = axus.project_up(random_point_01.unsqueeze(0).T).T
            random_point_high = from_1_around_origin(
                x=high_dim,
                lb=benchmark.lb_vec, 
                ub=benchmark.ub_vec
            ).squeeze(0)
            
            random_fx = benchmark(random_point_high.unsqueeze(0))
            
            # 添加到数据中
            bounce._add_data_to_tr_observations(
                xs_down=random_point_low.unsqueeze(0),
                xs_up=random_point_high.unsqueeze(0),
                fxs=random_fx
            )
            bounce._n_evals += 1
            
            print(f"   当前最佳值: {bounce.fx_tr.min().item():.6f}")
        
        print(f"\n📊 测试结果分析:")
        print(f"   总迭代次数: {iteration}")
        print(f"   总评估次数: {bounce._n_evals}")
        print(f"   GA代数历史: {ga_generations_history}")
        print(f"   最佳适应度历史: {best_fitness_history}")
        
        # 验证GA是否在每次迭代中都进化了
        if len(ga_generations_history) > 1:
            generation_increases = [
                ga_generations_history[i] > ga_generations_history[i-1] 
                for i in range(1, len(ga_generations_history))
            ]
            print(f"   GA代数是否递增: {generation_increases}")
            
            if all(generation_increases):
                print("✅ 验证通过：GA在每次迭代中都进行了进化")
            else:
                print("⚠️  警告：GA可能没有在每次迭代中都进化")
        
        # 显示最终的集成系统状态
        final_info = bounce.ga_tabpfn_integration.get_integration_info()
        print(f"\n🔍 最终GA-TabPFN状态:")
        print(f"   GA总代数: {final_info['ga_info']['current_generation']}")
        print(f"   TabPFN训练样本数: {final_info['surrogate_info']['n_train_samples']}")
        print(f"   TabPFN模型状态: {'已训练' if final_info['surrogate_info']['is_fitted'] else '未训练'}")
        
        return bounce


def test_ga_single_generation_evolution():
    """测试GA单代进化功能"""
    
    print("\n" + "=" * 60)
    print("🧬 测试GA单代进化功能")
    print("=" * 60)
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建简单基准
    class TestBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                Parameter(name="x2", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                Parameter(name="x3", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                Parameter(name="x4", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
            ]
            self.dim = 4
            self.representation_dim = 4
            self.lb_vec = torch.zeros(4, dtype=torch.float64)
            self.ub_vec = torch.ones(4, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = TestBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    config = GAConfig(population_size=8, max_generations=5)
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_instance.predict_proba.return_value = [[0.7, 0.3], [0.4, 0.6]]
        mock_classifier.return_value = mock_instance
        
        integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=config,
            ga_generations=1  # 每次只进化一代
        )
        
        # 模拟多次单代进化
        print("开始多次单代进化测试...")
        
        for i in range(5):
            print(f"\n--- 第{i+1}次单代进化 ---")
            
            # 生成一些模拟数据
            existing_X = torch.rand(6, 4, dtype=torch.float64)
            existing_y = torch.rand(6, dtype=torch.float64)
            
            # 执行单代进化
            best_low_dim, best_high_dim = integration.run_global_search(
                existing_X, existing_y
            )
            
            ga_info = integration.get_integration_info()['ga_info']
            print(f"   GA代数: {ga_info['current_generation']}")
            print(f"   最佳适应度: {ga_info['best_fitness']}")
            print(f"   最佳解(低维): {best_low_dim[:2].tolist()}")
        
        print("\n✅ 单代进化测试完成")


if __name__ == "__main__":
    try:
        # 测试每次迭代都进行GA的新模式
        bounce_result = test_every_iteration_ga_mode()
        
        # 测试GA单代进化功能
        test_ga_single_generation_evolution()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("=" * 60)
        
        print("\n📝 新模式特点:")
        print("1. ✅ 每次迭代都进行GA单代进化")
        print("2. ✅ TabPFN持续学习和预测最佳中心点")
        print("3. ✅ 全局搜索与局部搜索紧密结合")
        print("4. ✅ GA种群持续进化，不重新初始化")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        gin.clear_config()
