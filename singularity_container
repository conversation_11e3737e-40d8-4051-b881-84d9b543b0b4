Bootstrap: library
From: ubuntu:20.04

%environment
    export PATH="/root/.local/bin:$PATH"
    export PYTHONPATH=/bs/bounce

%post
    echo 'Acquire::ForceIPv4 "true";' > /etc/apt/apt.conf.d/99force-ipv4
    
    # 临时允许不安全的HTTP连接来安装基础证书
    sed -i 's|https://|http://|g' /etc/apt/sources.list
    echo 'Acquire::https::Verify-Peer "false";' >> /etc/apt/apt.conf.d/99force-ipv4
    echo 'Acquire::https::Verify-Host "false";' >> /etc/apt/apt.conf.d/99force-ipv4

    set -ex
    export DEBIAN_FRONTEND=noninteractive
    export TZ=Etc/UTC

    # 第一步：先安装最基础的ca-certificates
    apt-get update
    apt-get install -y --allow-unauthenticated ca-certificates curl

    # 第二步：现在有了证书，改回HTTPS源并恢复安全验证
    sed -i 's|http://|https://|g' /etc/apt/sources.list
    sed -i '/Verify-Peer/d' /etc/apt/apt.conf.d/99force-ipv4
    sed -i '/Verify-Host/d' /etc/apt/apt.conf.d/99force-ipv4

    # 第三步：用安全的HTTPS源安装其他软件
    apt-get update
    apt-get install -y \
        software-properties-common unzip git gnupg2

    # 使用新方法添加 deadsnakes PPA
    mkdir -p /etc/apt/keyrings
    curl -fsSL "https://keyserver.ubuntu.com/pks/lookup?op=get&search=0xba6932366a755776" | gpg --dearmor -o /etc/apt/keyrings/deadsnakes.gpg
    echo "deb [signed-by=/etc/apt/keyrings/deadsnakes.gpg] https://ppa.launchpadcontent.net/deadsnakes/ppa/ubuntu focal main" > /etc/apt/sources.list.d/deadsnakes.list

    # 更新包含新PPA的源列表
    apt-get update

    # 安装 Python 3.11 和 pip
    apt-get install -y python3.10 python3.10-venv python3.10-dev python3-pip

    # 设置 pip 全局源为清华
    mkdir -p /root/.pip
    cat > /root/.pip/pip.conf <<EOF
    [global]
    index-url = https://pypi.tuna.tsinghua.edu.cn/simple
    timeout = 100
    EOF

    # wenti
    pip install --upgrade pip setuptools wheel
    pip install rapidfuzz==3.9.7


    # 安装 Poetry
    pip install poetry

    # 创建工作目录
    mkdir -p /bs
    cd /bs

    #####################
    ## 克隆 bounce 项目
    #####################
    cp -r /mnt/g/bounce/bounce-main /bs/bounce
    cd bounce

    # 设置并安装依赖
    poetry env use python3.11
    poetry install

    # 下载 bounce 数据文件
    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/slice_localization_data.zip
    unzip slice_localization_data.zip && rm slice_localization_data.zip
    mv slice_localization_data.csv data/

    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/wms_crafted.tgz
    tar -xzf wms_crafted.tgz && rm wms_crafted.tgz
    mv wms_crafted/frb/frb10-6-4.wcnf data/maxsat/
    rm -rf wms_crafted

    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/mse18-new.zip
    unzip mse18-new.zip && rm mse18-new.zip
    mv mse18-new/cluster-expansion/benchmarks/IS1_5.*******.5_softer_periodic.wcnf.gz .
    rm -rf mse18-new
    gunzip IS1_5.*******.5_softer_periodic.wcnf.gz
    mv IS1_5.*******.5_softer_periodic.wcnf data/maxsat/cluster-expansion-IS1_5.*******.5_softer_periodic.wcnf

    cd /bs

    #####################
    ## 克隆 BenchSuite 项目
    #####################
    cp -r /mnt/g/bounce/BenchSuite-master /bs/BenchSuite
    cd BenchSuite
    git checkout no-mujoco

    # 解压数据（不安装依赖）
    cd data/svm
    gzip -d CT_slice_*
    cd ../..

%runscript
    echo "Running Bounce..."
    cd /bs/bounce
    poetry run python3 main.py "$@"

%labels
    Author <EMAIL>
    Version v0.0.3

%help
    This container runs Bounce from GitHub and includes required data
