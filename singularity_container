Bootstrap: library
From: ubuntu:20.04

%environment
    export PATH="/root/.local/bin:$PATH"
    export PYTHONPATH=/bs/bounce

%post
    set -ex

    export DEBIAN_FRONTEND=noninteractive
    export TZ=Etc/UTC

    apt-get update && apt-get install -y \
        software-properties-common curl unzip git gnupg2

    # 手动添加 deadsnakes PPA
    apt-key adv --keyserver keyserver.ubuntu.com --recv-keys BA6932366A755776
    echo "deb http://ppa.launchpad.net/deadsnakes/ppa/ubuntu focal main" > /etc/apt/sources.list.d/deadsnakes-ppa.list
    apt-get update

    # 安装 Python 3.11 和 pip
    apt-get install -y python3.10 python3.10-venv python3.10-dev python3-pip


    # 安装 Poetry
    pip install poetry

    # 创建工作目录
    mkdir -p /bs
    cd /bs

    #####################
    ## 克隆 bounce 项目
    #####################
    git clone https://github.com/LeoIV/bounce.git
    cd bounce

    # 设置并安装依赖
    poetry env use python3.11
    poetry install

    # 下载 bounce 数据文件
    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/slice_localization_data.zip
    unzip slice_localization_data.zip && rm slice_localization_data.zip
    mv slice_localization_data.csv data/

    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/wms_crafted.tgz
    tar -xzf wms_crafted.tgz && rm wms_crafted.tgz
    mv wms_crafted/frb/frb10-6-4.wcnf data/maxsat/
    rm -rf wms_crafted

    curl -O http://bounce-resources.s3-website-us-east-1.amazonaws.com/mse18-new.zip
    unzip mse18-new.zip && rm mse18-new.zip
    mv mse18-new/cluster-expansion/benchmarks/IS1_5.*******.5_softer_periodic.wcnf.gz .
    rm -rf mse18-new
    gunzip IS1_5.*******.5_softer_periodic.wcnf.gz
    mv IS1_5.*******.5_softer_periodic.wcnf data/maxsat/cluster-expansion-IS1_5.*******.5_softer_periodic.wcnf

    cd /bs

    #####################
    ## 克隆 BenchSuite 项目
    #####################
    git clone https://github.com/LeoIV/BenchSuite.git
    cd BenchSuite
    git checkout no-mujoco

    # 解压数据（不安装依赖）
    cd data/svm
    gzip -d CT_slice_*
    cd ../..

%runscript
    echo "Running Bounce..."
    cd /bs/bounce
    poetry run python3 main.py "$@"

%labels
    Author <EMAIL>
    Version v0.0.3

%help
    This container runs Bounce from GitHub and includes required data
