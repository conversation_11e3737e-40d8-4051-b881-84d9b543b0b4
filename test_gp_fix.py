#!/usr/bin/env python3
"""
测试GP全局代理模型的修复效果
验证GP模型能否正确处理高维数据（53维）而不会出现维度不匹配错误
"""

import torch
import numpy as np
import logging
import gin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# 创建模拟的Ackley53基准函数
class MockAckley53:
    def __init__(self):
        self.dim = 53
        self.representation_dim = 53
        self.parameters = []  # 简化版本
        self.lb_vec = torch.zeros(53)
        self.ub_vec = torch.ones(53)

# 创建模拟的AxUS投影
class MockAxUS:
    def __init__(self):
        self.target_dim = 5  # 当前目标维度

# 测试GP模型的修复
def test_gp_model_fix():
    print("🔧 测试GP全局代理模型修复...")
    
    # 导入修复后的GP模型
    from bounce.gp_surrogate import GPGlobalSurrogate
    
    # 创建模拟对象
    mock_benchmark = MockAckley53()
    mock_axus = MockAxUS()
    
    # 创建GP模型
    gp_model = GPGlobalSurrogate(mock_benchmark, mock_axus, device='cpu')
    
    # 🎯 关键测试：使用高维数据（53维）训练GP模型
    print(f"✅ 测试GP模型训练（高维数据: 53维）...")
    
    # 生成模拟的高维训练数据
    n_samples = 10
    X_high_dim = torch.rand(n_samples, 53, dtype=torch.float64)  # 53维高维数据
    y = torch.rand(n_samples, dtype=torch.float64)  # 目标值
    
    try:
        # 尝试训练GP模型
        gp_model.fit(X_high_dim, y)
        print(f"✅ GP模型训练成功！输入维度: {X_high_dim.shape[1]}, 样本数: {len(X_high_dim)}")
        
        # 测试预测
        X_test = torch.rand(5, 53, dtype=torch.float64)
        predictions = gp_model.predict(X_test)
        print(f"✅ GP模型预测成功！预测样本数: {len(X_test)}, 预测结果: {predictions.shape}")
        
        # 测试更新
        X_new = torch.rand(3, 53, dtype=torch.float64)
        y_new = torch.rand(3, dtype=torch.float64)
        gp_model.update(X_new, y_new)
        print(f"✅ GP模型更新成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ GP模型测试失败: {e}")
        return False

# 测试混合变量格式修复
def test_mixed_variable_format():
    print("\n🔧 测试混合变量格式修复...")
    
    # 导入修复后的GA集成类
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    
    # 创建模拟对象（简化版本）
    mock_benchmark = MockAckley53()
    
    # 创建一个模拟的AxUS对象，支持bins_and_indices_of_type
    class MockAxUSWithTypes:
        def __init__(self):
            self.target_dim = 5
            
        def bins_and_indices_of_type(self, param_type):
            from bounce.util.benchmark import ParameterType
            
            if param_type == ParameterType.BINARY:
                # 模拟50个二分变量映射到前3个低维变量
                return [(None, torch.tensor([0, 1, 2]))]
            elif param_type == ParameterType.CONTINUOUS:
                # 模拟3个连续变量映射到后2个低维变量
                return [(None, torch.tensor([3, 4]))]
            elif param_type == ParameterType.CATEGORICAL:
                # 无类别变量
                return []
            else:
                return []
    
    mock_axus = MockAxUSWithTypes()
    
    # 创建GA配置
    ga_config = GAConfig(
        population_size=10,
        max_generations=5,
        crossover_rate=0.8,
        mutation_rate=0.15
    )
    
    try:
        # 创建GA-TabPFN集成对象（使用GP作为全局模型）
        ga_integration = GATabPFNIntegration(
            axus=mock_axus,
            benchmark=mock_benchmark,
            ga_config=ga_config,
            tabpfn_n_bins=5,
            device='cpu',
            global_model_type='gp'
        )
        
        print(f"✅ GA-TabPFN集成系统创建成功，全局模型类型: GP")
        
        # 测试混合变量候选点生成
        candidates = ga_integration._generate_mixed_variable_candidate()
        print(f"✅ 混合变量候选点生成成功，维度: {candidates.shape}, 类型: {candidates.dtype}")
        print(f"   候选点示例: {candidates}")
        
        # 测试格式修复
        test_candidate = torch.rand(5, dtype=torch.float64) * 2 - 1  # [-1, 1]范围
        fixed_candidate = ga_integration._fix_mixed_variable_format(test_candidate)
        print(f"✅ 混合变量格式修复成功")
        print(f"   修复前: {test_candidate}")
        print(f"   修复后: {fixed_candidate}")
        
        return True
        
    except Exception as e:
        print(f"❌ 混合变量格式测试失败: {e}")
        return False

# 测试配置文件参数读取
def test_gin_config():
    print("\n🔧 测试Gin配置参数读取...")
    
    try:
        # 清除现有配置
        gin.clear_config()
        
        # 读取配置文件
        gin.parse_config_file('configs/default.gin')
        
        # 检查是否能正确读取配置
        print("✅ Gin配置文件读取成功")
        
        # 打印一些关键配置
        print(f"✅ 配置文件解析完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Gin配置测试失败: {e}")
        return False

def main():
    print("🚀 开始验证修复效果...\n")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: GP模型修复
    if test_gp_model_fix():
        success_count += 1
    
    # 测试2: 混合变量格式修复
    if test_mixed_variable_format():
        success_count += 1
    
    # 测试3: 配置文件参数读取
    if test_gin_config():
        success_count += 1
    
    print(f"\n📊 验证结果: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        print("🎉 所有修复验证通过！")
        print("\n✅ 修复总结:")
        print("   1. GP全局代理模型现在能正确处理高维数据（53维）")
        print("   2. TR中心点预测现在能正确处理混合变量格式")
        print("   3. 全局代理模型类型现在可以通过配置文件设置")
        print("\n🔧 使用方法:")
        print("   - 修改 configs/default.gin 中的 GATabPFNIntegration.global_model_type")
        print("   - 可选值: 'gp', 'rbf', 'tabpfn', 'xgboost'")
    else:
        print("❌ 部分测试失败，请检查修复的代码")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()