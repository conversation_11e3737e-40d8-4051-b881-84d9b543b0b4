#!/usr/bin/env python3
"""
测试设备调试信息脚本
运行简化的多代理模型集成，验证设备调试信息是否正常输出
"""

import torch
import logging
import sys
from bounce.benchmarks import MaxSat60
from bounce.projection import AxUS
from bounce.ga_tabpfn_integration import GATabPFNIntegration
from bounce.genetic_algorithm import GAConfig

# 设置详细的日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_device_debug_info():
    """测试设备调试信息输出"""
    print("=" * 80)
    print("🔍 设备调试信息测试")
    print("=" * 80)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    device = 'cuda' if cuda_available else 'cpu'
    
    print(f"CUDA可用: {cuda_available}")
    print(f"测试设备: {device}")
    
    # 创建测试组件
    benchmark = MaxSat60()
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建GA配置
    ga_config = GAConfig(population_size=10, max_generations=1)
    
    try:
        # 创建GA-TabPFN集成（集成模式）
        print("\n🚀 创建GA-TabPFN集成系统...")
        ga_integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=ga_config,
            device=device,
            enable_ensemble=True  # 启用集成模式
        )
        
        print(f"✅ GA-TabPFN集成系统创建成功")
        print(f"   设备: {device}")
        print(f"   集成模式: {ga_integration.enable_ensemble}")
        
        # 创建少量训练数据（足以触发全局搜索）
        print("\n📊 创建训练数据...")
        X_train = torch.rand(6, benchmark.representation_dim, dtype=torch.float64)
        y_train = torch.rand(6, dtype=torch.float64)
        
        if device == 'cuda':
            X_train = X_train.cuda()
            y_train = y_train.cuda()
        
        print(f"训练数据: X_train.device={X_train.device}, y_train.device={y_train.device}")
        
        # 测试最佳中心点预测（这应该会触发所有调试信息）
        print("\n🎯 开始测试最佳中心点预测...")
        print("⚠️  期望看到详细的设备调试信息...")
        
        predicted_center = ga_integration.predict_best_center_with_tabpfn(
            existing_X=X_train,
            existing_y=y_train,
            n_candidates=5
        )
        
        print(f"\n✅ 预测完成")
        print(f"   predicted_center设备: {predicted_center.device}")
        print(f"   predicted_center形状: {predicted_center.shape}")
        print(f"   predicted_center值: {predicted_center[:3]}...")
        
        # 检查设备一致性
        device_consistent = str(predicted_center.device).startswith(device) or (device == 'cpu' and predicted_center.device.type == 'cpu')
        print(f"   设备一致性: {'✅ 通过' if device_consistent else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🔍 多代理模型集成设备调试信息测试")
    
    success = test_device_debug_info()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 设备调试信息测试成功！")
        print("💡 如果看到大量 [DEVICE_DEBUG] 和 [ENSEMBLE_DEBUG] 信息，说明调试系统正常工作。")
    else:
        print("⚠️  设备调试信息测试失败！")
        print("💡 请检查上面的错误信息，定位具体的设备不一致问题。")
    print("=" * 80)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)