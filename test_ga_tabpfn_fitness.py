#!/usr/bin/env python3
"""
测试GA使用TabPFN预测适应度的功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ga_with_tabpfn_fitness():
    """测试GA使用TabPFN预测适应度"""
    
    print("🧬 测试GA使用TabPFN预测适应度")
    print("=" * 50)
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        # 模拟预测质量分数：返回不同的分数
        mock_instance.predict_proba.return_value = [
            [0.8, 0.2],  # 个体1：质量好（低分数）
            [0.3, 0.7],  # 个体2：质量差（高分数）
            [0.6, 0.4],  # 个体3：质量中等
        ]
        mock_classifier.return_value = mock_instance
        
        from bounce.genetic_algorithm import MixedVariableGA, GAConfig
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        from bounce.projection import AxUS
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 2
                self.representation_dim = 2
                self.lb_vec = torch.zeros(2, dtype=torch.float64)
                self.ub_vec = torch.ones(2, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        axus = AxUS(parameters=benchmark.parameters, n_bins=2)
        
        # 创建并训练TabPFN代理模型
        global_surrogate = TabPFNGlobalSurrogate(benchmark, n_bins=2)
        
        # 用一些训练数据训练TabPFN
        X_train = torch.rand(10, 2, dtype=torch.float64)
        y_train = torch.rand(10, dtype=torch.float64)
        global_surrogate.fit(X_train, y_train)
        
        print(f"TabPFN模型训练完成，训练样本数: {global_surrogate.get_model_info()['n_train_samples']}")
        
        # 创建GA，传入TabPFN代理模型
        config = GAConfig(population_size=6, max_generations=3)
        ga = MixedVariableGA(config, axus, benchmark, global_surrogate)
        
        print(f"GA初始化完成，种群大小: {config.population_size}")
        print(f"GA是否有全局代理模型: {ga.global_surrogate is not None}")
        print(f"全局代理模型是否已训练: {ga.global_surrogate.is_fitted if ga.global_surrogate else False}")
        
        # 初始化种群
        ga.initialize_population()
        print(f"种群初始化完成，个体数: {len(ga.population)}")
        
        # 检查初始适应度（应该都是None）
        initial_fitness = [ind.fitness for ind in ga.population]
        print(f"初始适应度: {initial_fitness}")
        
        # 评估种群（应该使用TabPFN）
        print("\n--- 使用TabPFN评估种群 ---")
        ga.evaluate_population()
        
        # 检查评估后的适应度
        evaluated_fitness = [ind.fitness for ind in ga.population]
        print(f"TabPFN评估后的适应度: {evaluated_fitness}")
        
        # 验证适应度不为None
        assert all(f is not None for f in evaluated_fitness), "所有个体都应该有适应度值"
        
        # 验证适应度是TabPFN预测的（不是真实函数值）
        print(f"最佳个体适应度: {ga.best_individual.fitness}")
        
        # 进化一代
        print("\n--- 进化一代 ---")
        pre_generation = ga.generation
        ga.evolve_generation()
        post_generation = ga.generation
        
        print(f"进化前代数: {pre_generation}, 进化后代数: {post_generation}")
        print(f"进化后最佳适应度: {ga.best_individual.fitness}")
        
        # 验证进化成功
        assert post_generation > pre_generation, "GA应该进化了一代"
        
        print("\n✅ GA使用TabPFN预测适应度测试通过！")


def test_ga_fallback_to_real_function():
    """测试GA在TabPFN不可用时回退到真实函数"""
    
    print("\n" + "=" * 50)
    print("🔄 测试GA回退到真实函数")
    print("=" * 50)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建简单基准
    class TestBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
            ]
            self.dim = 2
            self.representation_dim = 2
            self.lb_vec = torch.zeros(2, dtype=torch.float64)
            self.ub_vec = torch.ones(2, dtype=torch.float64)
            self.call_count = 0  # 记录真实函数调用次数
            
        def __call__(self, x):
            self.call_count += 1
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = TestBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=2)
    
    # 创建GA，不传入TabPFN代理模型（None）
    config = GAConfig(population_size=4, max_generations=2)
    ga = MixedVariableGA(config, axus, benchmark, global_surrogate=None)
    
    print(f"GA初始化完成，全局代理模型: {ga.global_surrogate}")
    
    # 初始化种群
    ga.initialize_population()
    
    # 评估种群（应该使用真实函数）
    print("--- 使用真实函数评估种群 ---")
    initial_call_count = benchmark.call_count
    ga.evaluate_population()
    final_call_count = benchmark.call_count
    
    function_calls = final_call_count - initial_call_count
    print(f"真实函数调用次数: {function_calls}")
    print(f"种群大小: {len(ga.population)}")
    
    # 验证真实函数被调用
    assert function_calls == len(ga.population), f"应该调用真实函数{len(ga.population)}次，实际调用{function_calls}次"
    
    # 验证所有个体都有适应度
    evaluated_fitness = [ind.fitness for ind in ga.population]
    assert all(f is not None for f in evaluated_fitness), "所有个体都应该有适应度值"
    
    print("✅ GA回退到真实函数测试通过！")


def test_fitness_comparison():
    """比较TabPFN预测适应度与真实适应度的差异"""
    
    print("\n" + "=" * 50)
    print("📊 比较TabPFN预测适应度与真实适应度")
    print("=" * 50)
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器，返回固定的预测结果
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_instance.predict_proba.return_value = [
            [0.9, 0.1],  # 预测质量很好
            [0.1, 0.9],  # 预测质量很差
        ]
        mock_classifier.return_value = mock_instance
        
        from bounce.genetic_algorithm import MixedVariableGA, GAConfig
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        from bounce.projection import AxUS
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建基准函数
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 1
                self.representation_dim = 1
                self.lb_vec = torch.zeros(1, dtype=torch.float64)
                self.ub_vec = torch.ones(1, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        axus = AxUS(parameters=benchmark.parameters, n_bins=1)
        
        # 创建并训练TabPFN
        global_surrogate = TabPFNGlobalSurrogate(benchmark, n_bins=2)
        X_train = torch.rand(5, 1, dtype=torch.float64)
        y_train = torch.rand(5, dtype=torch.float64)
        global_surrogate.fit(X_train, y_train)
        
        # 创建两个GA：一个用TabPFN，一个用真实函数
        config = GAConfig(population_size=2, max_generations=1)
        
        ga_with_tabpfn = MixedVariableGA(config, axus, benchmark, global_surrogate)
        ga_with_real = MixedVariableGA(config, axus, benchmark, None)
        
        # 使用相同的种群
        ga_with_tabpfn.initialize_population()
        ga_with_real.population = [ind.copy() for ind in ga_with_tabpfn.population]
        
        # 分别评估
        ga_with_tabpfn.evaluate_population()
        ga_with_real.evaluate_population()
        
        print("个体适应度比较:")
        for i in range(len(ga_with_tabpfn.population)):
            tabpfn_fitness = ga_with_tabpfn.population[i].fitness
            real_fitness = ga_with_real.population[i].fitness
            print(f"个体{i+1}: TabPFN预测={tabpfn_fitness:.6f}, 真实值={real_fitness:.6f}")
        
        print("✅ 适应度比较测试完成！")


if __name__ == "__main__":
    try:
        test_ga_with_tabpfn_fitness()
        test_ga_fallback_to_real_function()
        test_fitness_comparison()
        
        print("\n" + "=" * 50)
        print("🎉 所有GA-TabPFN适应度测试通过！")
        print("=" * 50)
        
        print("\n📝 修改总结:")
        print("1. ✅ GA现在使用TabPFN预测种群个体的适应度")
        print("2. ✅ 当TabPFN不可用时，自动回退到真实函数")
        print("3. ✅ TabPFN作为全局代理模型指导GA进化")
        print("4. ✅ 只有最终选出的最佳解才使用真实函数评估")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
