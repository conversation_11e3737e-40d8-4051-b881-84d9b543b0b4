2025-09-02 13:11:40,737 - INFO - 初始维度3维，算法目标维度3维
2025-09-02 13:11:40,737 - INFO - 🤖 <PERSON><PERSON><PERSON> will split at most 0 times.  每次分裂1个箱子
2025-09-02 13:11:40,738 - INFO - 🤖 初始3维，有15 次评估预算 
2025-09-02 13:11:40,742 - INFO - 每一个可能维度以及评估预算：{3: 15}；以及总预算为15次
2025-09-02 13:11:40,743 - INFO - 🔄 策略切换机制已启用，切换阈值: 3步
2025-09-02 13:11:40,743 - INFO - 🎯 局部搜索将使用TABPFN代理模型
2025-09-02 13:11:40,743 - INFO - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 3, 类别变量: 0
2025-09-02 13:11:40,744 - INFO - 创建TabPFN代理模型，n_bins: 5
2025-09-02 13:11:40,744 - INFO - 使用TABPFN作为全局代理模型
2025-09-02 13:11:40,744 - INFO - GP全局代理模型初始化完成，参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True}
2025-09-02 13:11:40,744 - INFO - 创建GP代理模型，参数: {}
2025-09-02 13:11:40,744 - INFO - ✅ GP模型初始化完成
2025-09-02 13:11:40,744 - INFO - RBF全局代理模型初始化完成，核函数: gaussian, epsilon: 1.0
2025-09-02 13:11:40,745 - INFO - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0
2025-09-02 13:11:40,745 - INFO - ✅ RBF模型初始化完成
2025-09-02 13:11:40,745 - INFO - ✅ TabPFN模型初始化完成
2025-09-02 13:11:40,745 - INFO - 🌟 集成代理模型管理器初始化完成
2025-09-02 13:11:40,745 - INFO -    支持模型: ['gp', 'rbf', 'tabpfn']
2025-09-02 13:11:40,745 - INFO -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333}
2025-09-02 13:11:40,745 - INFO - 🌟 集成代理模型系统已启用
2025-09-02 13:11:40,745 - INFO - GA-全局模型集成系统初始化完成
Traceback (most recent call last):
  File "/mnt/c/Users/<USER>/Desktop/论文/混合变量优化/2023+NIPS+Bounce+TR+分箱/bounce-main/test_bounce_ensemble.py", line 86, in test_bounce_with_ensemble
    bounce.run()
  File "/mnt/c/Users/<USER>/Desktop/论文/混合变量优化/2023+NIPS+Bounce+TR+分箱/bounce-main/./bounce/bounce.py", line 532, in run
    self.sample_init()
  File "/mnt/c/Users/<USER>/Desktop/论文/混合变量优化/2023+NIPS+Bounce+TR+分箱/bounce-main/./bounce/bounce.py", line 430, in sample_init
    for parameter_type in self.benchmark.unique_parameter_types:
AttributeError: 'SimpleBinaryBenchmark' object has no attribute 'unique_parameter_types'
🚀 开始测试Bounce算法与集成代理模型系统...
✅ 基准函数: SimpleBinary, 维度: 3
cpu
✅ Bounce算法初始化完成
   集成模式启用: True
   支持的模型: ['gp', 'rbf', 'tabpfn']

🏃‍♂️ 开始运行Bounce算法...
❌ Bounce算法运行失败: 'SimpleBinaryBenchmark' object has no attribute 'unique_parameter_types'

❌ 集成测试失败！
