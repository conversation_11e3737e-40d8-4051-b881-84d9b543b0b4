#!/usr/bin/env python3
"""
快速验证K-means聚类精英选择实现
"""

import logging
import numpy as np
import torch
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_kmeans_clustering():
    """测试K-means聚类功能"""
    print("🧪 测试K-means聚类功能")
    
    # 生成测试数据：模拟GA种群
    n_individuals = 50
    n_dimensions = 10
    
    # 创建3个明显的聚类
    np.random.seed(42)
    cluster1 = np.random.normal([1, 1, 1, 1, 1, -1, -1, -1, -1, -1], 0.2, (17, n_dimensions))
    cluster2 = np.random.normal([-1, -1, -1, 1, 1, 1, 1, -1, -1, -1], 0.2, (16, n_dimensions))
    cluster3 = np.random.normal([0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 0.3, (17, n_dimensions))
    
    # 合并数据
    population_genes = np.vstack([cluster1, cluster2, cluster3])
    
    print(f"   生成{len(population_genes)}个个体，每个{n_dimensions}维")
    
    # 模拟适应度（距离原点的距离）
    fitness_scores = [np.linalg.norm(genes) for genes in population_genes]
    
    # 执行K-means聚类
    n_clusters = 3
    elite_count = 20
    
    print(f"   执行K-means聚类: {n_clusters}个聚类")
    
    # 数据标准化
    scaler = StandardScaler()
    genes_scaled = scaler.fit_transform(population_genes)
    
    # K-means聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(genes_scaled)
    
    print(f"   聚类完成，标签分布: {np.bincount(cluster_labels)}")
    
    # 从每个聚类选择最优的个体
    selected_elites = []
    
    for cluster_id in range(n_clusters):
        # 获取当前聚类中的个体索引
        cluster_indices = np.where(cluster_labels == cluster_id)[0]
        
        if len(cluster_indices) == 0:
            continue
            
        # 获取聚类中个体的适应度
        cluster_fitness = [(i, fitness_scores[i]) for i in cluster_indices]
        cluster_fitness.sort(key=lambda x: x[1])  # 按适应度排序
        
        # 从每个聚类选择最优的2个个体
        elites_per_cluster = min(2, len(cluster_fitness))
        for j in range(elites_per_cluster):
            selected_elites.append(cluster_fitness[j])
            
        print(f"   聚类{cluster_id}: {len(cluster_indices)}个个体，选择{elites_per_cluster}个精英")
    
    print(f"   总共选择{len(selected_elites)}个精英个体")
    
    # 计算精英间的多样性
    elite_indices = [elite[0] for elite in selected_elites]
    elite_genes = population_genes[elite_indices]
    
    total_distance = 0.0
    count = 0
    for i in range(len(elite_genes)):
        for j in range(i + 1, len(elite_genes)):
            distance = np.linalg.norm(elite_genes[i] - elite_genes[j])
            total_distance += distance
            count += 1
    
    diversity = total_distance / count if count > 0 else 0.0
    print(f"   精英多样性指标: {diversity:.4f}")
    
    # 比较：传统精英选择的多样性
    sorted_indices = sorted(range(len(fitness_scores)), key=lambda i: fitness_scores[i])
    traditional_elites = population_genes[sorted_indices[:len(selected_elites)]]
    
    total_distance_trad = 0.0
    count_trad = 0
    for i in range(len(traditional_elites)):
        for j in range(i + 1, len(traditional_elites)):
            distance = np.linalg.norm(traditional_elites[i] - traditional_elites[j])
            total_distance_trad += distance
            count_trad += 1
    
    diversity_trad = total_distance_trad / count_trad if count_trad > 0 else 0.0
    print(f"   传统精英选择多样性: {diversity_trad:.4f}")
    
    improvement = ((diversity - diversity_trad) / diversity_trad * 100) if diversity_trad > 0 else 0
    print(f"   多样性提升: {improvement:.1f}%")
    
    return diversity > diversity_trad

def test_bounce_integration():
    """测试与Bounce算法的集成"""
    print("\\n🔗 测试与Bounce算法的集成")
    
    try:
        from bounce.genetic_algorithm import GAConfig, MixedVariableGA
        from bounce.projection import AxUS
        
        print("   ✅ 成功导入Bounce相关模块")
        
        # 测试GAConfig的新参数
        config = GAConfig(
            use_kmeans_elite_selection=True,
            elite_clusters_ratio=0.5,
            population_size=100,
            elitism_rate=0.2
        )
        
        print(f"   ✅ GAConfig创建成功:")
        print(f"      - K-means精英选择: {config.use_kmeans_elite_selection}")
        print(f"      - 聚类比例: {config.elite_clusters_ratio}")
        print(f"      - 精英比例: {config.elitism_rate}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 K-means聚类精英选择 - 快速验证")
    print("="*50)
    
    # 测试K-means聚类功能
    kmeans_success = test_kmeans_clustering()
    
    # 测试与Bounce的集成
    integration_success = test_bounce_integration()
    
    print("\\n📋 验证结果:")
    print(f"   K-means聚类功能: {'✅ 通过' if kmeans_success else '❌ 失败'}")
    print(f"   Bounce集成测试: {'✅ 通过' if integration_success else '❌ 失败'}")
    
    if kmeans_success and integration_success:
        print("\\n🎉 所有验证通过！K-means聚类精英选择机制已成功实现")
        print("   可以运行 run_kmeans_experiments.py 进行完整实验")
    else:
        print("\\n⚠️  存在问题，请检查实现")
    
    return kmeans_success and integration_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)