#!/usr/bin/env python3
"""
运行MaxSat60和Ackley53问题的K-means聚类精英选择实验
"""

import argparse
import logging
import pathlib
import time
import pandas as pd
import glob
import gin

from bounce.bounce import Bounce
from bounce.util.printing import BColors, BOUNCE_NAME

def run_experiment(config_file, experiment_name, n_repeat=5):
    """运行单个实验"""
    print(f"\n{'='*60}")
    print(f"🚀 开始运行实验: {experiment_name}")
    print(f"   配置文件: {config_file}")
    print(f"   重复次数: {n_repeat}")
    print(f"{'='*60}")
    
    results_list = []
    
    for i in range(n_repeat):
        print(f"\n🔄 第{i+1}/{n_repeat}次实验开始...")
        
        start_time = time.time()
        
        try:
            # 加载配置
            gin.clear_config()
            gin.parse_config_file(config_file)
            
            # 创建并运行Bounce算法
            bounce = Bounce()
            print(f"   基准函数: {bounce.benchmark.fun_name}")
            print(f"   最大评估次数: {bounce.maximum_number_evaluations}")
            print(f"   K-means精英选择: {bounce.ga_tabpfn_integration.ga_config.use_kmeans_elite_selection}")
            
            # 运行算法
            bounce.run()
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 获取最终结果
            final_best = bounce.fx_global.min().item()
            total_evaluations = len(bounce.fx_global)
            
            print(f"   ✅ 第{i+1}次实验完成!")
            print(f"   ⏱️  耗时: {duration:.2f}秒")
            print(f"   🎯 最优值: {final_best:.6f}")
            print(f"   📊 总评估次数: {total_evaluations}")
            
            # 记录结果
            results_list.append({
                'experiment': experiment_name,
                'run': i+1,
                'best_value': final_best,
                'total_evaluations': total_evaluations,
                'duration': duration
            })
            
        except Exception as e:
            print(f"   ❌ 第{i+1}次实验失败: {e}")
            results_list.append({
                'experiment': experiment_name,
                'run': i+1,
                'best_value': float('inf'),
                'total_evaluations': 0,
                'duration': 0,
                'error': str(e)
            })
        
        finally:
            gin.clear_config()
    
    # 计算统计结果
    successful_runs = [r for r in results_list if r['best_value'] != float('inf')]
    if successful_runs:
        best_values = [r['best_value'] for r in successful_runs]
        avg_best = sum(best_values) / len(best_values)
        std_best = (sum((x - avg_best)**2 for x in best_values) / len(best_values))**0.5
        min_best = min(best_values)
        
        print(f"\n📈 {experiment_name} 实验统计结果:")
        print(f"   成功运行: {len(successful_runs)}/{n_repeat}")
        print(f"   平均最优值: {avg_best:.6f} ± {std_best:.6f}")
        print(f"   最佳值: {min_best:.6f}")
        print(f"   平均耗时: {sum(r['duration'] for r in successful_runs)/len(successful_runs):.2f}秒")
    
    return results_list

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        prog="K-means精英选择实验",
        description="运行MaxSat60和Ackley53问题的K-means聚类精英选择实验"
    )
    
    parser.add_argument(
        "--n-repeat",
        type=int,
        default=5,
        help="每个问题的重复实验次数"
    )
    
    parser.add_argument(
        "--problems",
        nargs="+",
        choices=["maxsat60", "ackley53", "both"],
        default=["both"],
        help="要运行的问题"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format=f"{BColors.LIGHTGREY} %(levelname)s:%(asctime)s - (%(filename)s:%(lineno)d) - %(message)s {BColors.ENDC}",
    )
    
    print(BOUNCE_NAME)
    print("🧬 K-means聚类精英选择实验")
    
    start_time = time.time()
    all_results = []
    
    # 确定要运行的实验
    experiments = []
    if "both" in args.problems or "maxsat60" in args.problems:
        experiments.append(("configs/maxsat60_kmeans.gin", "MaxSat60_Kmeans"))
    if "both" in args.problems or "ackley53" in args.problems:
        experiments.append(("configs/ackley53_kmeans.gin", "Ackley53_Kmeans"))
    
    # 运行所有实验
    for config_file, experiment_name in experiments:
        try:
            results = run_experiment(config_file, experiment_name, args.n_repeat)
            all_results.extend(results)
        except Exception as e:
            print(f"❌ 实验 {experiment_name} 完全失败: {e}")
    
    # 保存结果
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_file = f"kmeans_elite_selection_results_{int(time.time())}.csv"
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        print(f"\n💾 实验结果已保存到: {results_file}")
        
        # 显示汇总统计
        print("\n📊 汇总统计:")
        for exp_name in results_df['experiment'].unique():
            exp_data = results_df[results_df['experiment'] == exp_name]
            successful = exp_data[exp_data['best_value'] != float('inf')]
            if len(successful) > 0:
                print(f"   {exp_name}:")
                print(f"     成功率: {len(successful)}/{len(exp_data)} ({100*len(successful)/len(exp_data):.1f}%)")
                print(f"     平均最优值: {successful['best_value'].mean():.6f}")
                print(f"     标准差: {successful['best_value'].std():.6f}")
                print(f"     最佳值: {successful['best_value'].min():.6f}")
    
    total_time = time.time() - start_time
    print(f"\n🎉 所有实验完成! 总耗时: {total_time:.2f}秒")

if __name__ == "__main__":
    main()