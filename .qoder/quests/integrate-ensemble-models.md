# 集成代理模型系统设计文档

## 概述

本设计旨在将现有的三个独立代理模型（GP、TabPFN、RBF）集成为一个统一的集成代理模型系统。该系统通过动态权重调整机制，使三种代理模型协同工作，提升TR中心点选择的准确性和稳定性。

### 核心目标
- 将独立的GP、TabPFN、RBF代理模型集成为协同工作的集成系统
- 在所有维度问题中都启用三个代理模型，确保充分利用各模型优势
- 实现基于预测准确度的动态权重调整机制
- 提供统一的TR中心点选择接口
- 保持与现有bounce算法架构的兼容性
- 针对Ackley53和MaxSat60等标准基准问题进行优化验证

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "集成代理模型系统"
        EM[EnsembleManager<br/>集成管理器] --> GP[GP Model<br/>高斯过程模型]
        EM --> TabPFN[TabPFN Model<br/>TabPFN模型]
        EM --> RBF[RBF Model<br/>RBF模型]
        
        EM --> WM[WeightManager<br/>权重管理器]
        WM --> PE[PredictionEvaluator<br/>预测评估器]
        
        EM --> CS[CandidateSelector<br/>候选点选择器]
    end
    
    subgraph "现有系统集成"
        GA[GATabPFNIntegration<br/>GA集成类] --> EM
        Bounce[Bounce Algorithm<br/>主算法] --> GA
    end
    
    subgraph "数据流"
        X[训练数据 X] --> EM
        Y[训练数据 y] --> EM
        Candidates[候选点集合] --> EM
        EM --> BestCenter[最佳TR中心]
    end
```

### 核心组件关系

```mermaid
classDiagram
    class EnsembleManager {
        -models: Dict[str, SurrogateModel]
        -weight_manager: WeightManager
        -candidate_selector: CandidateSelector
        +predict_best_center()
        +update_models()
        +get_ensemble_prediction()
    }
    
    class WeightManager {
        -weights: Dict[str, float]
        -performance_history: List
        -window_size: int
        +update_weights()
        +get_current_weights()
        +calculate_performance()
    }
    
    class CandidateSelector {
        -alpha: float (softmax温度)
        -top_k: int
        +score_candidates()
        +select_best_center()
        +normalize_predictions()
    }
    
    class PredictionEvaluator {
        +evaluate_prediction_accuracy()
        +calculate_mae()
        +update_performance_history()
    }
    
    EnsembleManager --> WeightManager
    EnsembleManager --> CandidateSelector
    WeightManager --> PredictionEvaluator
```

## 详细设计

### 1. 集成管理器 (EnsembleManager)

负责统一管理三个代理模型，提供集成预测和TR中心选择功能。注意：**候选点生成由GA系统负责**，集成管理器只负责评估和选择。

```mermaid
flowchart TD
    A[接收GA生成的候选点] --> B[训练/更新三个代理模型]
    B --> C[三个模型并行预测候选点]
    C --> D[归一化各模型预测结果]
    D --> E[基于权重计算加权得分]
    E --> F[选择Top-K候选点]
    F --> G[Softmax采样选择最终中心]
```

#### 核心方法设计

| 方法名 | 功能描述 | 输入参数 | 输出结果 |
|-------|----------|----------|----------|
| `predict_best_center()` | 从给定候选点中预测最佳TR中心 | candidates, existing_X, existing_y | 最佳中心点(低维) |
| `update_models()` | 更新所有代理模型 | X_new, y_new | None |
| `get_ensemble_prediction()` | 获取集成预测结果 | candidates | 加权预测结果 |
| `evaluate_candidates()` | 评估候选点质量 | candidates | 评分数组 |

### 2. 权重管理器 (WeightManager)

实现基于预测准确度的动态权重调整机制。

#### 权重更新策略

```mermaid
flowchart LR
    A[收集近期预测结果] --> B[计算各模型MAE]
    B --> C[应用指数衰减函数]
    C --> D[归一化权重]
    D --> E[应用最小权重保护]
    E --> F[更新权重字典]
```

#### 权重计算公式

```
# 所有维度都启用三个模型
active_models = ['gp', 'tabpfn', 'rbf']  # 始终为三个模型

# 性能评分计算
performance_score_i = exp(-β × MAE_i)
weight_i = performance_score_i / Σ(performance_score_j)  # j ∈ {gp, tabpfn, rbf}
final_weight_i = max(min_weight, weight_i)  # 最小权重保护

# 权重强制归一化（确保三个模型权重之和为1）
weight_sum = weight_gp + weight_tabpfn + weight_rbf
final_weight_gp = weight_gp / weight_sum
final_weight_tabpfn = weight_tabpfn / weight_sum  
final_weight_rbf = weight_rbf / weight_sum
```

参数说明：
- `β`: 温度参数，控制权重差异程度 (默认: 5.0)
- `MAE_i`: 模型i的平均绝对误差
- `min_weight`: 最小权重保护值 (默认: 0.1)

### 3. 候选点选择器 (CandidateSelector)

负责候选点评分、排序和最终选择。

#### 选择流程设计

```mermaid
sequenceDiagram
    participant CS as CandidateSelector
    participant Models as 代理模型集合
    participant WM as WeightManager
    
    CS->>Models: 获取每个模型的Top-K预测
    Models-->>CS: 返回预测结果
    CS->>CS: Min-Max归一化预测值
    CS->>WM: 获取当前权重
    WM-->>CS: 返回权重字典
    CS->>CS: 计算加权得分
    CS->>CS: 选择Top-5候选点
    CS->>CS: Softmax采样选择最终中心
```

#### 评分机制

| 步骤 | 操作 | 公式 |
|------|------|------|
| 1 | 收集三个模型预测 | `pred_gp, pred_tabpfn, pred_rbf = model_*.predict(candidates)` |
| 2 | Min-Max归一化 | `norm_pred_i = (pred_i - min_i) / (max_i - min_i)` |
| 3 | 加权求和 | `score = w_gp×norm_gp + w_tabpfn×norm_tabpfn + w_rbf×norm_rbf` |
| 4 | Softmax采样 | `prob_i = exp(α × score_i) / Σ(exp(α × score_j))` |

### 4. 预测评估器 (PredictionEvaluator)

评估各模型预测准确度，为权重更新提供依据。

#### 性能评估指标

```mermaid
graph LR
    A[真实评估结果] --> B[计算预测误差]
    B --> C[更新滑动窗口]
    C --> D[计算MAE/RMSE]
    D --> E[记录性能历史]
```

## 数据流设计

### 训练数据流

```mermaid
flowchart TD
    A[历史评估数据<br/>existing_X, existing_y] --> B[数据预处理<br/>格式转换、维度检查]
    B --> C{数据足够训练?}
    C -->|是| D[并行训练三个模型]
    C -->|否| E[使用默认权重 1/3]
    D --> F[模型训练完成]
    E --> F
    F --> G[准备接收GA候选点]
```

### 预测数据流

```mermaid
flowchart TD
    A[GA提供候选点集合] --> B[GP模型预测]
    A --> C[TabPFN模型预测]
    A --> D[RBF模型预测]
    
    B --> E[归一化GP预测]
    C --> F[归一化TabPFN预测]
    D --> G[归一化RBF预测]
    
    E --> H[加权求和]
    F --> H
    G --> H
    
    H --> I[Top-K选择]
    I --> J[Softmax采样]
    J --> K[返回最佳中心给GA]
```

## 集成策略

### 职责分工明确

| 组件 | 主要职责 | 不负责 |
|------|----------|--------|
| **遗传算法(GA)** | 候选点生成、种群进化 | 最终TR中心选择 |
| **集成代理模型** | 候选点评估、TR中心选择 | 候选点生成 |
| **单个代理模型** | 预测候选点质量 | 权重管理、集成决策 |

### 数据流设计

### 系统集成流程

```mermaid
sequenceDiagram
    participant GA as 遗传算法
    participant EM as 集成管理器
    participant GP as GP模型
    participant TF as TabPFN模型
    participant RBF as RBF模型
    
    GA->>GA: 生成候选点集合
    GA->>EM: 传递候选点candidates
    EM->>GP: 预测candidates
    EM->>TF: 预测candidates  
    EM->>RBF: 预测candidates
    GP-->>EM: 返回GP预测值
    TF-->>EM: 返回TabPFN预测值
    RBF-->>EM: 返回RBF预测值
    EM->>EM: 归一化+加权+Top-K选择
    EM-->>GA: 返回最佳TR中心
```

| 条件 | GP模型 | TabPFN模型 | RBF模型 | 权重调整 |
|------|--------|------------|---------|----------|
| 所有维度问题 | ✓启用 | ✓启用 | ✓启用 | 动态权重调整 |
| 数据不足 (<10点) | ✓启用 | ✓启用 | ✓启用 | 均分权重(1/3) |
| 充足数据 (≥10点) | ✓启用 | ✓启用 | ✓启用 | 基于性能调整 |
| 高维问题 (>30维) | ✓启用 | ✓启用 | ✓启用 | 减少候选点数量（由GA负责） |

## 性能优化

### 计算效率优化

| 优化点 | 策略 | 预期收益 |
|-------|------|----------|
| 模型训练 | 避免重复训练，复用已训练模型 | 减少50%训练时间 |
| 预测批处理 | 批量预测候选点，减少单次调用开销 | 提升30%预测效率 |
| 权重缓存 | 缓存权重计算结果，避免重复计算 | 减少20%计算时间 |
| 三模型并行 | 高维问题保持三模型工作，调整候选点数量 | 保持预测质量 |

### 内存管理

```mermaid
flowchart LR
    A[数据滑动窗口] --> B[限制历史数据大小]
    B --> C[及时清理临时变量]
    C --> D[模型状态检查点]
    D --> E[内存使用监控]
```

## 配置参数

### 集成系统参数

| 参数名 | 类型 | 默认值 | 描述 |
|-------|------|--------|------|
| `enable_ensemble` | bool | True | 是否启用集成模式 |
| `ensemble_models` | List[str] | ['gp', 'tabpfn', 'rbf'] | 参与集成的模型列表 |
| `weight_window_size` | int | 50 | 权重计算滑动窗口大小 |
| `min_weight_protection` | float | 0.1 | 最小权重保护值 |
| `softmax_temperature` | float | 5.0 | Softmax采样温度参数 |
| `top_k_candidates` | int | 5 | 最终候选点数量 |

### 性能调优参数

| 参数名 | 类型 | 默认值 | 描述 |
|-------|------|--------|------|
| `high_dim_threshold` | int | 30 | 高维问题阈值 |
| `high_dim_candidates` | int | 35 | 高维问题候选点数 |
| `performance_beta` | float | 5.0 | 性能评估温度参数 |
| `min_training_points` | int | 10 | 最小训练数据量 |
| `always_use_three_models` | bool | True | 强制启用三个模型 |

## 测试策略

### 单元测试覆盖

```mermaid
mindmap
  root)测试覆盖(
    集成管理器
      模型初始化测试
      预测接口测试
      错误处理测试
    权重管理器
      权重计算测试
      性能评估测试
      边界条件测试
    候选点选择器
      评分机制测试
      采样策略测试
      归一化测试
    系统集成测试
      端到端流程测试
      性能基准测试
      配置参数测试
```

### 集成测试场景

| 测试场景 | 测试目的 | 验证指标 |
|----------|----------|----------|
| Ackley53基准测试 | 验证混合变量优化性能(53维) | 收敛速度、最终精度 |
| MaxSat60基准测试 | 验证纯二进制优化性能(60维) | 收敛速度、最终精度 |
| 权重动态调整测试 | 验证三模型权重更新机制 | 权重变化趋势、稳定性 |
| 模型协同工作测试 | 验证三模型集成效果 | 预测准确度、互补性 |

## 实现计划

### 开发阶段

```mermaid
gantt
    title 集成代理模型实现时间线
    dateFormat  X
    axisFormat %d
    
    section 核心组件开发
    EnsembleManager实现    :active, a1, 0, 3
    WeightManager实现      :a2, after a1, 2
    CandidateSelector实现  :a3, after a2, 2
    
    section 系统集成
    GATabPFN集成适配       :b1, after a3, 2
    Bounce主算法集成       :b2, after b1, 1
    
    section 测试验证
    单元测试开发           :c1, after a1, 8
    集成测试开发           :c2, after b2, 3
    性能基准测试           :c3, after c2, 2
```

### 实现优先级

| 优先级 | 组件 | 实现复杂度 | 关键路径 |
|-------|------|-----------|----------|
| P0 | EnsembleManager核心接口 | 中 | ✓ |
| P0 | 基础权重管理 | 低 | ✓ |
| P1 | 候选点选择器 | 中 | ✓ |
| P1 | GATabPFN集成适配 | 高 | ✓ |
| P2 | 性能优化机制 | 中 | - |
| P2 | 高级配置选项 | 低 | - |