# Bounce算法内存优化设计文档

## 1. 概述

本设计文档旨在解决Bounce算法在处理高维优化问题时出现的显存爆炸问题。通过分析算法流程和内存使用模式，提出针对性的优化方案，在不改变算法逻辑的前提下，有效降低内存占用。

### 1.1 问题背景

在运行高维优化问题（如MaxSat125，维度为125）时，算法在CUDA设备上出现显存爆炸现象，导致程序崩溃。主要表现为：

- 显存使用量随迭代次数急剧增长
- 大量中间张量未及时释放
- 代理模型训练和预测过程中内存累积
- 遗传算法种群大小随维度增长而急剧增加

### 1.2 优化目标

1. 降低显存峰值使用量
2. 及时释放无用的中间张量
3. 优化代理模型内存使用
4. 控制遗传算法种群大小
5. 保持算法逻辑和性能不变

## 2. 架构分析

### 2.1 算法核心组件

```mermaid
graph TD
    A[Bounce主循环] --> B[初始化阶段]
    A --> C[全局搜索阶段]
    A --> D[局部优化阶段]
    A --> E[数据管理阶段]
    
    B --> B1[采样初始点]
    B --> B2[数据添加]
    
    C --> C1[GA-TabPFN集成]
    C --> C2[集成代理模型管理]
    
    D --> D1[信任区域优化]
    D --> D2[候选点生成]
    
    E --> E1[数据存储管理]
    E --> E2[张量设备管理]
```

### 2.2 内存使用热点分析

1. **遗传算法模块**：种群大小动态增长，个体基因存储
2. **集成代理模型**：多个模型并行训练，历史数据存储
3. **张量操作**：大量中间张量未及时释放
4. **数据存储**：全局和局部数据重复存储

## 3. 内存优化方案

### 3.1 遗传算法内存优化

#### 3.1.1 动态种群大小控制

当前问题：种群大小随维度线性增长，高维问题时种群过大导致内存爆炸

优化方案：
```python
# 原配置
population_scale_factor: float = 2.0  # 种群缩放因子
max_population_size: int = 500  # 最大种群大小

# 优化后配置
population_scale_factor: float = 1.0  # 降低缩放因子
max_population_size: int = 200  # 降低最大种群大小
```

#### 3.1.2 种群基因存储优化

优化方案：
1. 及时清理已评估个体的高维基因缓存
2. 使用就地操作减少基因复制

### 3.2 集成代理模型内存优化

#### 3.2.1 模型训练数据管理

当前问题：所有历史数据都用于模型训练，数据量随迭代增长

优化方案：
1. 采用滑动窗口机制，只保留最近N个数据点用于训练
2. 实现增量训练而非全量重训练

#### 3.2.2 权重历史记录优化

当前问题：权重管理器存储大量历史性能数据

优化方案：
1. 限制性能历史记录窗口大小
2. 及时清理过期的历史记录

### 3.3 张量内存管理优化

#### 3.3.1 中间张量及时释放

优化方案：
1. 使用`torch.no_grad()`上下文减少梯度计算内存
2. 及时调用`torch.cuda.empty_cache()`释放CUDA缓存
3. 使用就地操作减少临时张量创建

#### 3.3.2 设备内存统一管理

优化方案：
1. 统一使用设备管理器处理张量设备转换
2. 避免不必要的设备间数据传输

### 3.4 数据存储优化

#### 3.4.1 全局数据去重存储

当前问题：全局数据和局部数据分别存储，存在重复

优化方案：
1. 建立统一的数据存储管理器
2. 通过引用而非复制存储数据

#### 3.4.2 候选点生成优化

优化方案：
1. 批量生成候选点而非一次性生成大量候选点
2. 及时清理已处理的候选点数据

## 4. 详细实现方案

### 4.1 遗传算法优化实现

#### 4.1.1 修改GAConfig配置

```python
@dataclass
class GAConfig:
    # 原配置
    # population_scale_factor: float = 2.0
    # max_population_size: int = 500
    
    # 优化后配置
    population_scale_factor: float = 1.0  # 降低缩放因子
    max_population_size: int = 200       # 降低最大种群大小
    
    # 新增内存优化配置
    enable_memory_optimization: bool = True  # 启用内存优化
    gene_cache_size: int = 50               # 基因缓存大小限制
```

#### 4.1.2 实现基因缓存清理机制

```python
class Individual:
    def clear_high_dim_cache(self):
        """清理高维基因缓存以释放内存"""
        self.high_dim_genes = None
        
    def to_high_dim(self, lb: torch.Tensor, ub: torch.Tensor) -> torch.Tensor:
        # 在计算完成后，根据配置决定是否保留缓存
        if not getattr(self.axus, 'enable_memory_optimization', False):
            # 保留缓存以提高重复访问性能
            pass
        else:
            # 不保留缓存以节省内存
            result = self._compute_high_dim(lb, ub)
            return result
```

### 4.2 集成代理模型优化实现

#### 4.2.1 实现滑动窗口训练数据管理

```python
class EnsembleManager:
    def __init__(self, ..., max_training_samples: int = 200):
        # 新增最大训练样本数限制
        self.max_training_samples = max_training_samples
        
    def fit(self, X: torch.Tensor, y: torch.Tensor) -> None:
        # 限制训练数据大小
        if len(X) > self.max_training_samples:
            # 随机采样或选择最近的数据
            indices = torch.randperm(len(X))[:self.max_training_samples]
            X = X[indices]
            y = y[indices]
        # 继续原有训练逻辑
```

#### 4.2.2 优化权重历史记录

```python
class WeightManager:
    def __init__(self, ..., max_history_size: int = 30):
        # 限制历史记录大小
        self.max_history_size = max_history_size
        
    def record_prediction(self, model_name: str, predicted: float, actual: float) -> None:
        # 添加历史记录后检查大小
        history = self.performance_history[model_name]
        if len(history) > self.max_history_size:
            # 移除最旧的记录
            history.pop(0)
```

### 4.3 张量内存管理优化实现

#### 4.3.1 实现内存优化上下文管理器

```python
class MemoryOptimizationContext:
    def __init__(self, device_manager):
        self.device_manager = device_manager
        
    def __enter__(self):
        # 启用内存优化模式
        torch.set_grad_enabled(False)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 清理内存
        if self.device_manager.is_cuda:
            torch.cuda.empty_cache()
```

#### 4.3.2 在关键位置插入内存清理代码

```python
# 在Bounce主循环的关键位置
def run(self):
    # ... 原有代码 ...
    
    # 在每次迭代后清理内存
    if self.device_manager.is_cuda:
        torch.cuda.empty_cache()
        
    # 在模型预测后清理中间张量
    del candidates_high_dim, predictions_dict
    if self.device_manager.is_cuda:
        torch.cuda.empty_cache()
```

### 4.4 数据存储优化实现

#### 4.4.1 实现统一数据存储管理器

```python
class DataManager:
    def __init__(self):
        self._data_store = {}
        self._references = {}
        
    def store_data(self, key, data):
        """存储数据并管理引用计数"""
        self._data_store[key] = data
        self._references[key] = 1
        
    def get_data(self, key):
        """获取数据并增加引用计数"""
        if key in self._references:
            self._references[key] += 1
        return self._data_store.get(key)
        
    def release_data(self, key):
        """释放数据引用"""
        if key in self._references:
            self._references[key] -= 1
            if self._references[key] <= 0:
                del self._data_store[key]
                del self._references[key]
```

## 5. 性能监控与验证

### 5.1 内存使用监控

实现内存使用监控工具：
```python
def monitor_memory_usage(device_manager):
    """监控当前内存使用情况"""
    if device_manager.is_cuda:
        allocated = torch.cuda.memory_allocated(device_manager.device)
        reserved = torch.cuda.memory_reserved(device_manager.device)
        logging.info(f"GPU内存使用: 已分配 {allocated / 1024**2:.2f} MB, "
                    f"已保留 {reserved / 1024**2:.2f} MB")
```

### 5.2 优化效果验证

1. 运行高维优化问题（如MaxSat125）前后对比内存使用情况
2. 验证算法收敛性和性能是否保持不变
3. 测试不同维度问题的内存使用情况

## 6. 风险评估与回退方案

### 6.1 潜在风险

1. **性能下降风险**：内存优化可能导致计算性能轻微下降
2. **收敛性影响**：减少训练数据可能影响模型收敛性
3. **兼容性问题**：修改可能影响其他模块的正常运行

### 6.2 回退方案

1. 提供配置开关，可动态启用/禁用内存优化
2. 保留原有实现作为备选方案
3. 实现渐进式优化，分阶段应用优化措施

## 7. 实施计划

### 7.1 第一阶段：基础优化（1-2天）

1. 修改GAConfig配置参数
2. 实现基本的内存清理机制
3. 添加内存使用监控

### 7.2 第二阶段：深度优化（2-3天）

1. 实现滑动窗口训练数据管理
2. 优化权重历史记录管理
3. 实现统一数据存储管理器

### 7.3 第三阶段：测试验证（1-2天）

1. 运行测试用例验证功能正确性
2. 对比优化前后的内存使用情况
3. 验证算法性能是否保持不变

## 8. 结论

通过以上优化方案，可以在不改变算法逻辑的前提下，有效解决Bounce算法在高维优化问题中的显存爆炸问题。关键优化点包括控制遗传算法种群大小、优化代理模型训练数据管理、及时清理中间张量以及统一数据存储管理。实施过程中需要密切监控性能变化，确保优化不会影响算法的收敛性和准确性。