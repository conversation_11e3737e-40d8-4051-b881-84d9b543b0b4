#!/usr/bin/env python3
"""
测试TabPFN训练样本数增长是否正常
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_tabpfn_data_growth():
    """测试TabPFN训练数据增长是否合理"""
    
    print("🔍 测试TabPFN训练样本数增长")
    print("=" * 50)
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_instance.predict_proba.return_value = [[0.7, 0.3], [0.4, 0.6]]
        mock_classifier.return_value = mock_instance
        
        from bounce.ga_tabpfn_integration import GATabPFNIntegration
        from bounce.genetic_algorithm import GAConfig
        from bounce.projection import AxUS
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.BINARY, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 2
                self.representation_dim = 2
                self.lb_vec = torch.zeros(2, dtype=torch.float64)
                self.ub_vec = torch.ones(2, dtype=torch.float64)
                
            def __call__(self, x):
                if x.dim() == 1:
                    x = x.unsqueeze(0)
                return torch.sum(x**2, dim=1)
        
        benchmark = TestBenchmark()
        axus = AxUS(parameters=benchmark.parameters, n_bins=2)
        
        config = GAConfig(population_size=6, max_generations=3)
        integration = GATabPFNIntegration(
            axus=axus,
            benchmark=benchmark,
            ga_config=config,
            ga_generations=1
        )
        
        print("开始模拟数据增长测试...")
        
        # 模拟逐步增加的数据
        data_sizes = [3, 5, 7, 9, 12]  # 模拟真实评估数据的增长
        
        for i, size in enumerate(data_sizes):
            print(f"\n--- 第{i+1}次更新 (数据大小: {size}) ---")
            
            # 生成累积的数据（模拟真实的全局数据）
            existing_X = torch.rand(size, 2, dtype=torch.float64)
            existing_y = torch.rand(size, dtype=torch.float64)
            
            print(f"输入数据大小: {len(existing_X)}")
            
            # 记录更新前的状态
            pre_history_len = len(integration.global_X_history)
            pre_surrogate_samples = integration.global_surrogate.get_model_info()['n_train_samples']
            
            print(f"更新前 - 历史记录数: {pre_history_len}, TabPFN样本数: {pre_surrogate_samples}")
            
            # 执行更新
            try:
                predicted_center = integration.predict_best_center_with_tabpfn(
                    existing_X, existing_y, n_candidates=3
                )
                
                # 记录更新后的状态
                post_history_len = len(integration.global_X_history)
                post_surrogate_samples = integration.global_surrogate.get_model_info()['n_train_samples']
                
                print(f"更新后 - 历史记录数: {post_history_len}, TabPFN样本数: {post_surrogate_samples}")
                
                # 计算增长量
                history_growth = post_history_len - pre_history_len
                samples_growth = post_surrogate_samples - pre_surrogate_samples
                
                print(f"增长量 - 历史记录: +{history_growth}, TabPFN样本: +{samples_growth}")
                
                # 验证增长是否合理
                if i == 0:
                    # 首次应该等于输入大小
                    expected_growth = size
                    print(f"✅ 首次训练，预期样本数: {expected_growth}, 实际: {post_surrogate_samples}")
                else:
                    # 后续应该只增加新数据
                    expected_new_data = size - data_sizes[i-1]
                    print(f"📊 预期新增数据: {expected_new_data}, 实际TabPFN增长: {samples_growth}")
                    
                    if samples_growth <= expected_new_data * 2:  # 允许一些容差
                        print("✅ 数据增长合理")
                    else:
                        print("⚠️  数据增长过快")
                
            except Exception as e:
                print(f"❌ 更新失败: {e}")
        
        print(f"\n📊 最终统计:")
        final_info = integration.get_integration_info()
        print(f"   最终TabPFN样本数: {final_info['surrogate_info']['n_train_samples']}")
        print(f"   历史记录数: {len(integration.global_X_history)}")
        print(f"   最后一次数据大小: {data_sizes[-1]}")
        
        # 验证最终样本数是否合理
        if final_info['surrogate_info']['n_train_samples'] <= data_sizes[-1] * 2:
            print("✅ 最终样本数合理")
        else:
            print("⚠️  最终样本数过多")


def test_tabpfn_surrogate_directly():
    """直接测试TabPFN代理模型的数据处理"""
    
    print("\n" + "=" * 50)
    print("🔍 直接测试TabPFN代理模型")
    print("=" * 50)
    
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        mock_classifier.return_value = mock_instance
        
        from bounce.tabpfn_surrogate import TabPFNGlobalSurrogate
        from bounce.util.benchmark import Parameter, ParameterType
        
        # 创建简单基准
        class TestBenchmark:
            def __init__(self):
                self.parameters = [
                    Parameter(name="x1", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                    Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0, upper_bound=1),
                ]
                self.dim = 2
        
        benchmark = TestBenchmark()
        surrogate = TabPFNGlobalSurrogate(benchmark, n_bins=3)
        
        print("测试TabPFN代理模型的增量更新...")
        
        # 第一次训练
        X1 = torch.rand(5, 2, dtype=torch.float64)
        y1 = torch.rand(5, dtype=torch.float64)
        
        print(f"\n第1次训练 - 数据大小: {len(X1)}")
        surrogate.fit(X1, y1)
        print(f"训练后样本数: {surrogate.get_model_info()['n_train_samples']}")
        
        # 第二次更新（模拟新增2个数据点）
        X2 = torch.rand(2, 2, dtype=torch.float64)
        y2 = torch.rand(2, dtype=torch.float64)
        
        print(f"\n第2次更新 - 新增数据大小: {len(X2)}")
        pre_samples = surrogate.get_model_info()['n_train_samples']
        surrogate.update_model(X2, y2)
        post_samples = surrogate.get_model_info()['n_train_samples']
        
        print(f"更新前样本数: {pre_samples}")
        print(f"更新后样本数: {post_samples}")
        print(f"增长量: {post_samples - pre_samples}")
        
        if post_samples - pre_samples == len(X2):
            print("✅ 增量更新正确")
        else:
            print("⚠️  增量更新有问题")


if __name__ == "__main__":
    try:
        test_tabpfn_data_growth()
        test_tabpfn_surrogate_directly()
        
        print("\n" + "=" * 50)
        print("🎉 TabPFN数据增长测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
