import logging
import math
# 用于快速定义数据类 Bin；@dataclass 是 Python 的语法糖，让你不用手写 __init__；
from dataclasses import dataclass, field

# Python 枚举类支持
from enum import Enum

# 类型注解支持；
from typing import Optional

# 用于配置管理 @gin.configurable表示这个类的参数可以在 .gin 文件中动态设置。
import gin
import numpy as np
import torch

from bounce.util.benchmark import Parameter, ParameterType

# 用于提取当前参数中涉及哪些类型
from bounce.util.data_handling import parameter_types


class BinSizing(Enum):
    """
    The way to bin the parameters. If "min", ordinal and categorical parameters use a bin with min(p_1, ..., p_n)
    realizations. If "max", ordinal and categorical parameters use a bin with max(p_1, ..., p_n) realizations.
    """

    MIN = "min"
    MAX = "max"


@dataclass
class Bin:
    """
    A bin is a collection of parameters that are binned together. Parameters within a bin are assumed to be of
    the same type. The bin is responsible for projecting a tensor of shape (n_samples, dims_required_for_bin) to a
    tensor of shape (n_samples, sum(bins_required_for_parameter(p) for p in parameters)).

    Args:
        parameters: the parameters to bin
        bin_sizing: the way to bin the parameters. If "min", ordinal and categorical parameters use a bin with min(p_1, ..., p_n)
            realizations. If "max", ordinal and categorical parameters use a bin with max(p_1, ..., p_n) realizations.
        forced_n_dims: if not None, the number of dimensions required for the bin. If None, the number of dimensions
            required for the bin is determined by the parameter type and bin_sizing.
        parameter_type: the type of the parameters in the bin. This is set automatically and should not be set manually.
    """

    # 这个 bin 包含了哪些变量
    parameters: list[Parameter]

    # bin中的变量类型是什么。自动由 parameters[0].type 推断，不让用户手动传；
    parameter_type: ParameterType = field(init=False)
    # the way to bin the parameters. if "min", ordinal and categorical parameters use a bin with min(p_1, ..., p_n)
    # realizations. if "max", ordinal and categorical parameters use a bin with max(p_1, ..., p_n) realizations.
    bin_sizing: BinSizing = BinSizing.MIN
    forced_n_dims: Optional[int] = None

    def __post_init__(self):
        # assert all parameters have the same type
        # 所有传进来的参数必须是同一种类型
        assert (
            len(set([p.type for p in self.parameters])) == 1
        ), "all parameters must have the same type"
        
        self.parameter_type = self.parameters[0].type

    @property
    def dims_required(self) -> int:
        """
        返回 bin 所需的维度数（min和max策略）
        Returns the number of dimensions required for the bin.
        """

        # 判断是否“强制指定维度”了
        if self.forced_n_dims is not None:
            return self.forced_n_dims
        match self.parameter_type:
            case ParameterType.CATEGORICAL:
                match self.bin_sizing:
                    case BinSizing.MIN:
                        f = min
                    case BinSizing.MAX:
                        f = max
                    case _:
                        raise ValueError(f"Unknown bin sizing {self.bin_sizing}")
                return f([p.dims_required for p in self.parameters])
            case ParameterType.CONTINUOUS:
                return 1
            case ParameterType.ORDINAL:
                return 1
            case ParameterType.BINARY:
                return 1
            case _:
                raise ValueError(f"Unknown parameter type {self.parameter_type}")


    """
        将一个 bin 的低维向量（比如 3 维）还原/投影回它对应的高维表示
        如果这个 bin 包含了两个 categorical 参数（分别是 3 类和 5 类），
        然后这个 bin 在低维空间只用了 3 个维度来表示这两个变量，
        那么：这个方法就是用这 3 维的数据，恢复出它们原来的形式（one-hot 编码），总维度是 3 + 5 = 8。

        x：输入的x是所有变量，形状（采样数量，当前bin要求的维度数）
        low_sequency：是否禁用随机扰动，默认是 False，会引入 sign 翻转等扰动
    """
    def project_up(self, x: torch.Tensor, low_sequency: bool = False) -> torch.Tensor:
        """
        Projects a tensor of shape (n_samples, dims_required_for_bin) to a tensor of shape
        (n_samples, sum(bins_required_for_parameter(p) for p in parameters))

        Args:
            x: the tensor to project
            low_sequency: if True, the projection does not use random signs

        Returns:
            the projected tensor

        """
        # assert x is 2d
        # 必须是一个二维张量：(n_samples, dims_required)
        assert len(x.shape) == 2, "x must be 2d"

        # assert x has the correct number of dimensions
        # 确保这个输入向量的列数 = 当前 bin 要求的维度数（否则无法恢复）。
        assert (
            x.shape[1] == self.dims_required
        ), "x must have the correct number of dimensions"

        # define output tensor
        # torch.ones(...) 创建一个全为 1 的张量，添加一个‘-’号，则全为-1的张量
        output = -torch.ones(
            (x.shape[0], sum(parameter.dims_required for parameter in self.parameters)), dtype=x.dtype
        )

        # fill output tensor
        # start：这是用于往 output 中逐段插入恢复值的位置标记。
        start = 0
        for parameter in self.parameters:
            # 控制取几列 start:end 控制了从张量 x 中取出多少列
            end = start + parameter.dims_required
            match parameter.type:
                # 如果是 continuous 或 binary：就直接复制这段低维输入；并乘上一个「±1」的扰动（默认是随机生成的）；
                case ParameterType.CONTINUOUS:
                    # for continuous parameters, we just copy the input and possibly flip the sign
                    output[:, start:end] = x * (
                        1 if low_sequency else parameter.random_sign
                    )
                case ParameterType.BINARY:
                    # for binary parameters, we just copy the input and possibly flip the sign
                    output[:, start:end] = x * (
                        1 if low_sequency else parameter.random_sign
                    )
                case ParameterType.CATEGORICAL:
                    # assert only one non-``zero'' index per sample
                    # one-hot 只能有一个不为-1(转化到[-1,1]上了)
                    assert (
                        torch.sum(x != -1, dim=1).max() == 1
                    ), "Exactly one non-``zero'' index per sample is required"

                    # get indices of non-zero elements
                    # 找到最大的一维
                    k = torch.argmax(x, dim=1)

                    # get output indices
                    # 
                    v = torch.ceil(k * parameter.dims_required / self.dims_required).to(
                        dtype=torch.long
                    )
                    # add random sign unless low_sequency
                    v = (
                        v + (0 if low_sequency else parameter.random_sign)
                    ) % parameter.dims_required
                    # create output tensor
                    output[torch.arange(output.shape[0]), start + v] = 1
                case ParameterType.ORDINAL:
                    raise NotImplementedError("ordinal parameters not yet implemented")
            start = end
        return output

    """
        将一个  Bin 分割成多个新的  Bin。这是 Bounce 算法中动态调整维度的关键部分。
        Args:
            n_new_bins: 要创建的新 bin 的数量（整数）
        Returns：
            返回一个  Bin 对象的列表，包含分割后的所有 bin（原始 bin 和新创建的 bin）
    """
    def split(self, n_new_bins: int) -> list["Bin"]:
        """
        Splits the bin into 1+new_bins bins.

        Args:
            n_new_bins: the number of new bins to create
        """
        # assert n_new_bins > 0
        assert n_new_bins > 0, "n_new_bins must be positive"

        # assert n_new_bins is an integer
        assert n_new_bins == int(n_new_bins), "n_new_bins must be an integer"

        # assert n_new_bins is not larger than the number of parameters
        assert n_new_bins < len(
            self.parameters
        ), "n_new_bins must be smaller than the number of parameters"

        # 创建一个数组包含所有参数的索引（0 到 参数数量-1）
        parameter_indices = np.arange(len(self.parameters))

        # 随机打乱这些所以，这样参数会被随机分配到新的bin中
        np.random.shuffle(parameter_indices)

        # split into n_new_bins + 1 bins
        # np.array_split() 函数将数组拆分成多个（n_new_bins + 1个）子数组。
        bins = np.array_split(parameter_indices, n_new_bins + 1)

        # create new bins
        # 这里的 new_bins 是一个列表，包含了新创建的 Bin 对象。对于每组索引，创建一个新的  Bin 对象
        new_bins = [Bin([self.parameters[i] for i in b]) for b in bins]

        # 如果参数类型是分类或序数，我们需要为新 bin 设置维度数
        if (
            self.parameter_type == ParameterType.CATEGORICAL
            or self.parameter_type == ParameterType.ORDINAL
        ):
            # if the parameter type is categorical or ordinal, we need to set the number of dimensions for the new bins
            # to keep the same mapping as before
            # 为每个新 bin 设置  forced_n_dims 属性，使其等于原始 bin 的  dims_required。
            # 这确保了新 bin 使用与原始 bin 相同的维度数，保持映射一致性。
            for bi in new_bins:
                bi.forced_n_dims = self.dims_required

        return new_bins

"""
     AxUS 类是 Bounce 算法的核心组件，负责管理高维到低维的映射。
"""
@gin.configurable
class AxUS:
    def __init__(
        self,
        # 这是要优化的参数列表
        parameters: list[Parameter],

        # 要使用的 bin 数量，也是低维空间的初始维度
        n_bins: int,

        # 决定如何为分类和序数参数确定 bin 大小
        bin_sizing: BinSizing = BinSizing.MIN,

        # 是否禁用随机扰动
        low_sequency: bool = False,
    ):
        """
        Initializes the AxUS class.

        Args:
            parameters:the parameters to use
            n_bins: the number of bins to use
            bin_sizing: the way to bin the parameters. if "min", ordinal and categorical parameters use a bin with
                min(p_1, ..., p_n), if "max", ordinal and categorical parameters use a bin with max(p_1, ..., p_n)
                realizations.
            low_sequency: if True, bins do not use random signs
        """
        self.parameters = parameters
        self.n_bins = n_bins
        self.bin_sizing = bin_sizing
        self.low_sequency = low_sequency

        # 初始化一个空的  bins 列表。
        self.bins: list[Bin] = []

        # 这行代码确保 bin 的数量至少等于参数类型的数量
        assert n_bins >= len(
            parameter_types(parameters)
        ), "n_bins must be at least as large as the number of parameter types"

        # 这行调用 _reset() 方法，该方法负责创建和初始化 bin。我们稍后会详细讨论这个方法。
        self._reset()

        # 创建了一个字典 将每个参数名映射到其在高维空间中的索引范围。
        self._param_indices = dict()
        start = 0
        for i, p in enumerate(self.parameters):
            end = start + p.dims_required
            self._param_indices[p.name] = torch.arange(start, end)
            start = end

    """
        这个方法非常简单，它接受一个 Parameter 对象，并返回该参数在高维空间中的索引。
        它直接从初始化时创建的 self._param_indices 字典中查找。
        例如，如果我们有参数 x1（连续型，需要1维）和 x2（分类型，3类别，需要3维），那么：
            parameter_indices(x1) 返回 tensor([0])
            parameter_indices(x2) 返回 tensor([1, 2, 3])
    """
    def parameter_indices(self, parameter: Parameter) -> torch.Tensor:
        """
        这个方法非常简单，它接受一个 Parameter 对象，并返回该参数在高维空间中的索引。
        它直接从初始化时创建的 self._param_indices 字典中查找。
        
        
        Returns the indices of the parameters in the input tensor.

        Args:
            parameter: the parameter to get the indices for

        Returns:
            the indices of the parameter in the input tensor

        """
        return self._param_indices[parameter.name]
    
    """
        这个属性返回每个 bin 在低维空间中的索引范围。
    """
    @property
    def bin_indices(self) -> list[torch.Tensor]:
        """
        这个属性返回每个 bin 在低维空间中的索引范围。
        Returns:
            a list of tensors containing the indices of the parameters in each bin

        """
        bin_indices = []
        start = 0
        for bin in self.bins:
            end = start + bin.dims_required
            bin_indices.append(torch.arange(start, end))
            start = end
        return bin_indices


    """
        这个属性计算   高维  空间的总维度，即所有参数所需的维度总和。
        例如，如果我们有：
            2个连续型参数（各需要1维）
            1个二进制型参数（需要1维）
            1个分类型参数（3类别，需要3维）
            那么  input_dim 将返回 2+1+3 = 6。
    """
    @property
    def input_dim(self) -> int:
        """

        Returns:
            the number of dimensions required for the input tensor

        """
        return sum(p.dims_required for p in self.parameters)


    """
        这个属性计算  低维  空间的总维度，即所有 bin 所需的维度总和。
        继续上面的例子，如果我们有 3 个 bin：
            Bin 1：包含2个连续型参数，需要1维
            Bin 2：包含1个二进制型参数，需要1维
            Bin 3：包含1个分类型参数，需要3维
            那么  target_dim 将返回 1+1+3 = 5。
    """
    @property
    def target_dim(self) -> int:
        """

        Returns:
            the number of dimensions required for the output tensor

        """
        assert len(self.bins) > 0, "bins have not been initialized yet"
        return sum([b.dims_required for b in self.bins])

    """
        这是初始化 bin 结构的核心方法。
        这个方法负责创建和初始化 bin。它在  AxUS 类初始化时被调用，也可以在需要重置 bin 结构时手动调用。
    """
    def _reset(self):
        """
        Resets the bins to the initial state.

        Returns:
            None

        """
        # find indices for every parameter type
        """
            self.bins 被重置为空列表
            parameter_type_indices 是一个字典，将参数类型映射到该类型的参数索引
            bins_per_type 是一个字典，将参数类型映射到该类型应分配的 bin 数量
        """
        self.bins = []
        parameter_type_indices = dict()
        bins_per_type = dict()

        # 对于每个参数类型，我们计算该类型应分配的 bin 数量
        for parameter_type in ParameterType:

            # _parameter_type_indices 是一个列表，包含特定类型的参数索引
            _parameter_type_indices = [
                i for i, p in enumerate(self.parameters) if p.type == parameter_type
            ]
            # if there are parameters of this type, add them to the dictionary containing the indices for every type
            if len(_parameter_type_indices) > 0:

                # 将其索引添加到 parameter_type_indices 字典
                parameter_type_indices[parameter_type] = _parameter_type_indices

                # max(1, floor((参数类型的参数数量 / 总参数数量) * 总bin数量))
                bins_per_type[parameter_type] = max(
                    1,
                    math.floor(
                        (len(_parameter_type_indices) / (len(self.parameters)))
                        * self.n_bins
                    ),
                )

        # 如果总数小于 self.n_bins，随机选择一个参数类型并增加其 bin 数量
        # TODO 此处“if”应该“改为while”
        if sum(bins_per_type.values()) < self.n_bins:
            # increase the number of bins for a random parameter type
            random_parameter_type = np.random.choice(
                [t for t in parameter_type_indices.keys()]
            )
            bins_per_type[random_parameter_type] += 1

        # TODO 补充bin数量时，可能出现bin数量大于变量数量，如此的话，可能会出现空bin
        while sum(bins_per_type.values()) > self.n_bins:
            # decrease the number of bins for a random parameter type
            random_parameter_type = np.random.choice(
                [t for t in parameter_type_indices.keys() if bins_per_type[t] > 1]
            )
            bins_per_type[random_parameter_type] -= 1

        # find the number of bins for every parameter type
        # 这段代码为每种参数类型创建 bin：
        # 拿到参数类型和参数索引
        for parameter_type, _parameter_type_indices in parameter_type_indices.items():

            # 拿到参数类型的bin数量
            n_bins = bins_per_type[parameter_type]
            logging.debug(
                f"Parameter type {parameter_type} gets {n_bins}/{self.n_bins} bins."
            )

            if (
                parameter_type == ParameterType.CONTINUOUS
                or parameter_type == ParameterType.BINARY
            ):
                # 随机打乱该类型的参数索引
                index_permutation = np.random.permutation(
                    torch.tensor(_parameter_type_indices),
                )
            elif (
                parameter_type == ParameterType.CATEGORICAL
                or parameter_type == ParameterType.ORDINAL
            ):
                # TODO this should be changed for benchmarks with varying number of categories
                # so that variables with similar number of categories are in the same bin
                index_permutation = np.random.permutation(
                    torch.tensor(_parameter_type_indices),
                )
            else:
                raise ValueError(f"Unknown parameter type {parameter_type}")
            index_permutation = torch.tensor(index_permutation, dtype=torch.long)
            
            # 将索引分成  n_bins 组
            input_dim_bins = torch.tensor_split(index_permutation, n_bins)

            # 为每组创建一个  Bin 对象，并将对应的参数传递给它
            for input_dim_bin in input_dim_bins:
                self.bins.append(
                    Bin(
                        parameters=[self.parameters[i] for i in input_dim_bin],
                        bin_sizing=self.bin_sizing,
                    )
                )
        logging.debug(f"Total number of bins: {len(self.bins)}")

    """
        这是将低维空间中的点投影到高维空间的核心方法。
        方法接受一个低维空间中的张量 x，并返回其在高维空间中的投影。
    """
    def project_up(self, x: torch.Tensor) -> torch.Tensor:
        """
        Projects a tensor of shape (n_samples, dims_required_for_bin) to a tensor of shape
        (n_samples, sum(bins_required_for_parameter(p) for p in parameters))

        Returns:
            the projected tensor

        """

        # 这段代码确保输入是二维张量：
        if len(x.shape) == 1:
            # 如果 x 是一维的，将其转换为二维（添加一个维度）
            x = x.unsqueeze(1)
        # assert x is 2d
        assert len(x.shape) == 2, "x must be 2d"

        # 转置 x，使其形状变为 (n_samples, target_dim)
        x = x.t()

        # assert x has the correct number of dimensions
        # 这行代码检查输入的维度是否与低维空间的维度匹配。
        assert (
            x.shape[1] == self.target_dim
        ), "x must have the correct number of dimensions"

        # define output tensor
        # 创建一个形状为 (n_samples, input_dim) 的零张量，用于存储高维空间中的投影结果。
        output = torch.zeros((x.shape[0], self.input_dim), dtype=x.dtype)

        # fill output tensor
        # input_start 和 input_end 跟踪当前 bin 在低维空间中的索引范围
        input_start = 0
        for bin in self.bins:
            input_end = input_start + bin.dims_required

            #  indices 是当前 bin 中所有参数在高维空间中的索引
            indices = torch.concat([self.parameter_indices(p) for p in bin.parameters])
            bp = bin.project_up(
                x[:, input_start:input_end], low_sequency=self.low_sequency
            )
            output[:, indices] = bp
            input_start = input_end
        return output.t()

    """
        返回特定类型的 bin 数量
    """
    def n_bins_of_type(self, parameter_type: ParameterType) -> int:
        """
        Returns the number of bins of a certain type.

        Args:
            parameter_type: the type of the parameters

        Returns:
            the number of bins of that type

        """
        return sum([1 for b in self.bins if b.parameters[0].type == parameter_type])

    """
        返回特定类型的 bin 列表
    """
    def bins_of_type(self, parameter_type: ParameterType) -> list[Bin]:
        """
        Returns the bins of a certain type.

        Args:
            parameter_type: the type of the parameters

        Returns:
            a list of bins

        """
        return [b for b in self.bins if b.parameters[0].type == parameter_type]

    """
        返回特定类型的 bin 及其索引的元组列表
        例如，如果我们调用 bins_and_indices_of_type(ParameterType.CONTINUOUS)，
        它将返回所有包含连续型参数的 bin 及其索引，如 [(Bin(...), tensor([0]))]。
    """
    def bins_and_indices_of_type(
        self, parameter_type: ParameterType
    ) -> list[tuple[Bin, torch.Tensor]]:
        """

        返回特定类型的 bin 及其索引的元组列表
        例如，如果我们调用 bins_and_indices_of_type(ParameterType.CONTINUOUS)，
        它将返回所有包含连续型参数的 bin 及其索引，如 [(Bin(...), tensor([0]))]。

        Returns the bins and their indices of a certain type.

        Args:
            parameter_type: the type of the parameters

        Returns:
            a list of tuples containing the bins and their indices

        """
        return [
            (b, i)
            for b, i in zip(self.bins, self.bin_indices)
            if b.parameters[0].type == parameter_type
        ]

    def split(self, n_new_bins: int) -> dict[torch.Tensor, list[torch.Tensor]]:
        """
        这个方法接受一个参数 n_new_bins，表示要创建的新 bin 的数量，
        并返回一个字典，将旧的 bin 索引映射到新的 bin 索引。
        Splits the bins into n_new_bins bins.

        Returns:
            a dictionary that maps old (and current) bin indices to new bin indices

        """
        # assert n_new_bins is an integer
        assert n_new_bins == int(n_new_bins), "n_new_bins must be an integer"

        # b_old：用于存储保留的旧 bin
        # b_new：用于存储新创建的 bin
        b_old = []
        b_new = []

        # define a mapping that maps old bin indices to new bin indices
        # 用于映射旧 bin 索引到新 bin 索引
        index_mapping: dict[torch.Tensor, list[torch.Tensor]] = dict()

        # keep track of the number of new indices added
        # 跟踪添加的新索引数量
        indices_added = 0

        # target dim before splitting
        # 记录分割前的目标维度
        target_dim_old = self.target_dim

        # 每次迭代取出bin以及对应的低纬空间的索引
        for bin, bin_indcs in zip(self.bins, self.bin_indices):
            # 这确保了每个 bin 至少保留一个参数。
            n_new = min(n_new_bins, len(bin.parameters) - 1)
            # 如果 n_new 为 0（即 bin 只包含一个参数），则不分割该 bin，将其添加到 b_old 中，
            # 并在  index_mapping 中为其设置一个空列表。
            if n_new == 0:
                b_old.append(bin)
                logging.debug(
                    f"Bin of type {bin.parameter_type} is not split because it only contains one parameter."
                )
                index_mapping[bin_indcs] = []
                continue
            # 否则，调用 bin 的  split 方法创建新的 bin。
            # bs[0] 是原始 bin（保留部分参数），bs[1:] 是新创建的 bin。
            bs = bin.split(n_new)
            b_old.append(bs[0])
            b_new.extend(bs[1:])
            index_mapping[bin_indcs] = []

            # 对于每个新 bin，计算其在低维空间中（分裂之后）的索引范围，并将其添加到 index_mapping 中。
            for _new in range(n_new):
                index_mapping[bin_indcs].append(
                    torch.arange(
                        target_dim_old + indices_added,
                        target_dim_old + indices_added + bs[_new + 1].dims_required,
                    )
                )
                indices_added += bs[_new + 1].dims_required
        self.bins = b_old + b_new
        self.n_bins = len(self.bins)
        return index_mapping
