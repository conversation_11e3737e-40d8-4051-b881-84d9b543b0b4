import logging
import lzma
import math
import os.path
import zlib
from datetime import datetime
from typing import Optional, Union

import gin
import numpy as np
import torch
from botorch.acquisition import ExpectedImprovement
from botorch.sampling import SobolQMCNormalSampler
from torch import Size
from tqdm import tqdm

from bounce import settings
from bounce.benchmarks import Benchmark
from bounce.candidates import create_candidates_continuous, create_candidates_discrete
from bounce.gaussian_process import fit_mll, get_gp
from bounce.projection import AxUS, Bin
from bounce.trust_region import TrustRegion, update_tr_state
from bounce.util.benchmark import ParameterType
from bounce.util.data_handling import (
    construct_mixed_point,
    from_1_around_origin,
    join_data,
    sample_binary,
    sample_categorical,
    sample_continuous,
)
from bounce.util.printing import BColors
'''
    标准库：logging, lzma, math等

    第三方库：gin(配置管理), numpy, torch, botorch(Bayesian优化库), tqdm(进度条)

    项目内部模块：benchmarks, candidates, gaussian_process等
'''

# 使用@gin.configurable装饰器使Bounce类可以通过gin配置框架进行配置。
@gin.configurable 
class Bounce:
    """
    Bounce class: implements the Bounce algorithm.

    The main method is `run()` which runs the algorithm.
        benchmark: 要优化的基准问题

    number_initial_points: 初始采样点数

    initial_target_dimensionality: 初始目标维度

    number_new_bins_on_split: 分裂时创建的新bin数量

    maximum_number_evaluations: 最大评估次数

    batch_size: 批量大小

    results_dir: 结果保存目录

    其他可选参数控制算法行为
    """

    def __init__(
            self,
            benchmark: Benchmark,
            number_initial_points: int,
            initial_target_dimensionality: int,
            number_new_bins_on_split: int,
            maximum_number_evaluations: int,
            batch_size: int,
            results_dir: str,
            desired_final_dimensionality: Optional[int] = None,
            maximum_number_evaluations_until_input_dim: Optional[int] = None,
            max_cholesky_size: int = 1000,
            device: str = "cuda" if torch.cuda.is_available() else "cpu",
            dtype: Optional[str] = None,
            use_scipy_lbfgs: bool = True,
            max_lbfgs_iters: Optional[int] = None,
            min_cuda: int = 10,
            n_interleaved: int = 5,
    ):
        """
        Init

        Args:
            benchmark: the benchmark to be used
            number_initial_points: the number of initial points to be sampled
            initial_target_dimensionality: the dimensionality in which `Bounce` starts the optimization
            number_new_bins_on_split: the number of new bins to be created on each split (if applicable)
            maximum_number_evaluations: the maximum number of function evaluations
            batch_size: the batch size to be used
            results_dir: the directory where the results will be stored
            desired_final_dimensionality: the dimensionality in which `Bounce` terminates the optimization
            maximum_number_evaluations_until_input_dim: the maximum number of function evaluations until the input
            max_cholesky_size: the maximum size of the Cholesky decomposition
            device: the device to be used (cpu or cuda)
            dtype: the dtype to be used (float32 or float64)
            use_scipy_lbfgs: whether to use scipy's LBFGS implementation or the backup Adam optimizer for the GP fitting
            max_lbfgs_iters: maximum iterations until we run LBFGS, after that use Adam
            min_cuda: the minimum number of data points to use cuda
            n_interleaved: the number of interleaved steps when optimizing mixed benchmarks

        """
        self.benchmark = benchmark
        """
        The benchmark to be used
        # 存储要优化的基准问题对象，包含目标函数和参数空间定义
        """
        self.number_initial_points = number_initial_points
        """
        the number of initial points to be sampled
        # 初始采样点的数量，用于构建初始高斯过程模型
        """
        self.initial_target_dimensionality = initial_target_dimensionality
        """
        the dimensionality in which `Bounce` starts the optimization
        # 算法开始时的目标维度(低维嵌入空间的维度)
        """
        self.number_new_bins_on_split = number_new_bins_on_split
        """
        the number of new bins to be created on each split (if applicable)
        # 当信任区域分裂时，每个维度创建的新bin数量
        """
        self.maximum_number_evaluations = maximum_number_evaluations
        """
        the maximum number of function evaluations
        # 最大函数评估次数，算法终止条件之一
        """
        self.batch_size = batch_size
        """
        the batch size to be used
        # 每次迭代评估的候选点数量(并行评估数)
        """
        self.max_cholesky_size = max_cholesky_size
        """
        the maximum size of the Cholesky decomposition
        # Cholesky分解的最大尺寸限制，用于控制矩阵运算的内存使用
        """
        self.use_scipy_lbfgs = use_scipy_lbfgs
        """
        whether to use scipy's LBFGS implementation or the backup Adam optimizer for the GP fitting
        # 布尔值，指示是否使用scipy的L-BFGS优化器来拟合高斯过程
        """
        self.device = device
        print(self.device)
        """
        the device to be used (cpu or cuda)
        """
        self.min_cuda = min_cuda
        """
        the minimum number of data points to use cuda
        # 使用CUDA的最小数据点数，小于此值可能使用CPU更高效
        """
        self.max_lbfgs_iters = max_lbfgs_iters  # maximum iterations until we run LBFGS, after that use Adam
        """
        maximum iterations until we run LBFGS, after that use Adam
        # L-BFGS优化器的最大迭代次数，None表示无限制
        """
        self.n_interleaved = n_interleaved
        """
        the number of interleaved steps when optimizing mixed benchmarks
        # 在处理混合类型参数时，连续和离散参数交替优化的轮数
        """
        self.dtype = None
        """
        the dtype to be used (float32 or float64)
        # 初始化dtype为None，表示尚未确定具体数据类型
        """
        if dtype is None:
            self.dtype = torch.float64
            # 如果用户未指定dtype参数（None），默认使用torch.float64（双精度浮点数）
        else:
        # 如果用户指定了dtype参数
            match dtype:
                case "float32":
                    self.dtype = torch.float32
                case "float64":
                    self.dtype = torch.float64
                case _:
                # 当dtype为其他未定义字符串时
                    raise ValueError(f"Unknown dtype {dtype}")
                    # 抛出明确错误，防止无效类型传入

        # defining results directory
        # 获取当前系统时间，用于生成唯一实验ID
        now = datetime.now()
        # 将gin配置转换为字符串，用于后续的哈希计算
        gin_config_str = gin.config_str()

        # 计算配置字符串的Adler-32哈希值（轻量级校验和）
        # 作用：相同配置的实验会产生相同的哈希值，方便归类
        adler = zlib.adler32(gin_config_str.encode("utf-8"))

        # 生成时间戳格式的文件名，精确到微秒保证唯一性
        # 格式示例：07-15-2023-14:30:45:123456
        fname = now.strftime("%m-%d-%Y-%H:%M:%S:%f")

        # 构建结果目录路径，层级结构：
        # results_dir / 配置哈希值 / 时间戳文件夹
        self.results_dir = os.path.join(results_dir, str(adler), fname)
       
        """
        the directory where the results will be stored
        """
        # 递归创建目录（exist_ok=True避免重复创建报错）
        os.makedirs(self.results_dir, exist_ok=True)    

        # save gin config to file
        # 将gin配置保存到文件，确保实验参数可追溯
        with open(os.path.join(self.results_dir, "gin_config.txt"), "w") as f:
            f.write(gin.config_str())# 写入完整的配置字符串

        # 设置最终目标维度（用户指定或默认使用benchmark的原始维度）
        self.desired_final_dimensionality = None
        """
        the dimensionality in which `Bounce` terminates the optimization
        """
        if desired_final_dimensionality is None:
            # 默认使用问题原始维度
            self.desired_final_dimensionality = self.benchmark.dim
        else:
            # 使用用户指定值
            self.desired_final_dimensionality = desired_final_dimensionality

        # 设置达到输入维度前的最大评估次数
        self.maximum_number_evaluations_until_input_dim = None
        """
        the maximum number of function evaluations until the input dimensionality is reached
        """
        # 默认使用总评估次数（即不单独限制低维阶段）
        if maximum_number_evaluations_until_input_dim is None:
            self.maximum_number_evaluations_until_input_dim = (
                self.maximum_number_evaluations
            )
        else:
            # 验证用户输入值合法性
            assert (
                    maximum_number_evaluations_until_input_dim
                    <= self.maximum_number_evaluations
            )
            self.maximum_number_evaluations_until_input_dim = (
                maximum_number_evaluations_until_input_dim
            )

        # 已执行的分裂次数计数器
        self.tr_splits = 0
        """
        the number of splits that have been performed (the target dimensionality has been increased)
        """

        # PRIVATE ATTRIBUTES


        # 总函数评估次数计数器
        self._n_evals = 0

        # 使用公式赋值，计算需要分裂多少次达到目标维度
        self._n_splits = math.ceil(
            math.log(
                self.desired_final_dimensionality / self.initial_target_dimensionality,
                self.number_new_bins_on_split + 1,
            )
        )

        # 允许用户通过配置开关控制是否启用自动bin数量调整
        # 多出来的维度会被忽略（或者没有实际作用），不会映射到原始问题空间中。
        if settings.ADJUST_NUMBER_OF_NEW_BINS:
            self._adjust_number_bins_on_split()
        logging.info(f"🤖 {settings.NAME} will split at most {self._n_splits} times.  每次分裂{self.number_new_bins_on_split}个箱子")

        # 根据初始维度算出这一阶段的评估预算（即最多评估多少个点）。
        self.split_budget = self._split_budget(self.initial_target_dimensionality)
        logging.info(f"🤖 初始{self.initial_target_dimensionality}维，有{self.split_budget} 次评估预算 ")
        """
        the budget for the current target dimensionality
        """

        # 把高维原始变量空间（比如 60维 MaxSAT）映射到一个低维的 target 空间中。
        self.random_embedding = AxUS(
            # parameters 描述每个参数的类型（连续/离散）和边界
            parameters=self.benchmark.parameters,
            # n_bins 初始划分的维度数（即初始低维空间维度）
            n_bins=self.initial_target_dimensionality,
        )
        """
        the random embedding used for the low-dimensional target space (only `AxUS` used)
        """
        # 表示当前信赖域的维度（即你在 target 空间中要优化的空间维度是多少）
        self.trust_region = TrustRegion(dimensionality=self.random_embedding.target_dim)
        """
        a `TrustRegion` instance to model the trust region
        """

        # saving global data
        # 创建一个空的张量（相当于二维数组了，0行 ，dim列），目的是记录“所有评估过的点在嵌入空间里的坐标”。
        self.x_global = torch.empty(
            0, 
            # 表示 target 空间的维度，即嵌入空间的维度
            self.random_embedding.target_dim, 
            dtype=self.dtype
        )
        """
        the input points in the low-dimensional target space
        """
        # 创建一个空的张量（相当于二维数组了，0行 ，dim列），目的是记录“所有评估点在原始问题空间里的真实表示”
        self.x_up_global = torch.empty(
            0, 
            # 表示原始变量的维度，比如 MaxSAT60 就是 60。
            self.benchmark.representation_dim, 
            dtype=self.dtype
        )
        """
        the input points in the high-dimensional representation space
        """
        # 存储每个点对应的函数值 f(x) 这是优化的目标值，也就是 benchmark 的输出
        self.fx_global = torch.empty(0, dtype=self.dtype)
        """
        the function values at the input points
        """

        # tr local data
        # Local 是当前这轮迭代所用的“有效样本”
        self._reset_local_data()

        # 列出所有可能的嵌入维度（target dimension），从起始维度开始，每次 split 增加维度。
        all_target_dims = self.initial_target_dimensionality * torch.pow(
            1 + self.number_new_bins_on_split, torch.arange(0, self._n_splits + 1)
        )
        # 对上面每一个 target 维度，调用 _split_budget(dim) 函数来计算评估预算（比如给 5 维 30次评估，15 维 60次评估...）
        # TODO好像有问题，预算怎么好像超出总预算了？
        self._all_split_budgets = {
            i.item(): self._split_budget(i.item()) for i in all_target_dims
        }

    # 这是一个类的私有方法（下划线开头表明“内部用”）
    def _reset_local_data(
            self
    ):
        # saving tr local data
        self.x_tr = torch.empty(0, self.random_embedding.target_dim, dtype=self.dtype)
        self.x_up_tr = torch.empty(
            0, self.benchmark.representation_dim, dtype=self.dtype
        )
        self.fx_tr = torch.empty(0, dtype=self.dtype)

    # 生成可能的bin增量候选集（从1到log2(目标维度)的整数）
    def _adjust_number_bins_on_split(
            self
    ):
        """
        Adjusts the number of new bins on split to the number of new bins that minimizes the difference between the
        true final target dimensionality and the desired final dimensionality.

        Returns:
            None

        """
        possible_bin_sizes = torch.arange(                                      # 得到一个张量列表
            1,                                                                  # 最小值：至少新增1个bin
            torch.floor(                                                        # 最大值：不超过对数增长上限
                torch.log2(torch.tensor(self.desired_final_dimensionality))     # 先转化为张量，再转化为标量
            ).item(),
        )

        # 返回张量中元素的总个数（number of elements）
        if possible_bin_sizes.numel() == 0:
            # 如果没有元素的话，每次分裂给1个bin。也就是torch.arange(1,0)或者torch.arange(1,1)
            possible_bin_sizes = torch.tensor([1])

        # 计算最优bin增量：使最终维度最接近目标值（之前已经用公式计算过需要分裂几次）
        best_bin_size = (
                # 下面方法返回最小的索引，然后+1就表示具体的值，因为前面生成使用arange生成的张量
                torch.argmin(               
                    torch.abs(
                        self.initial_target_dimensionality
                        * (1 + possible_bin_sizes) ** self._n_splits
                        - self.desired_final_dimensionality
                    )
                )
                + 1
        )
        # 如果计算值与原值不同，则更新并重新计算分裂次数
        if best_bin_size != self.number_new_bins_on_split:
            logging.debug(
                f"Updating number of new bins from {self.number_new_bins_on_split} to {best_bin_size}"
            )
            self.number_new_bins_on_split = best_bin_size.item()
            # TODO 为什么要向上取整，这么分裂的话最后维度不是超过期望维度了么（已解决） 多余箱子为空箱子，空箱子中没有变量。
            self._n_splits = math.ceil(
                # math.log(a,b)表示log 以a为底
                math.log(
                    self.desired_final_dimensionality
                    / self.initial_target_dimensionality,
                    self.number_new_bins_on_split + 1,
                )
            )

    # 用 _split_budget() 函数来决定这一阶段能用多少预算（评估次数）。
    def _split_budget(
            self,
            target_dimensionality: int
    ) -> int:
        """
        Calculates the number of evaluations to be used for the split with target_dimensionality.

        Args:
            target_dimensionality: the target dimensionality of the split

        Returns:
            the number of evaluations to be used for the split with target_dimensionality

        """
        total_budget = (
                self.maximum_number_evaluations_until_input_dim - self.number_initial_points
        )

        if target_dimensionality >= self.benchmark.dim:
            return min(
                10 * target_dimensionality,
                self.maximum_number_evaluations - self._n_evals,
            )
        split_budget = round(
            -(self.number_new_bins_on_split * total_budget * target_dimensionality)
            / (
                    self.initial_target_dimensionality
                    * (1 - (self.number_new_bins_on_split + 1) ** (self._n_splits + 1))
            )
        )
        return min(2 ** target_dimensionality, split_budget)

    # 在嵌入空间中采样一些初始点，映射到原始空间中，然后评估这些点在 benchmark 上的函数值，并记录这些结果，结果保存到之前创建的局部数据存放点中。
    def sample_init(
            self
    ):
        """
        Samples the initial points, evaluates them, and adds them to the observations.
        Increases the number of evaluations by the number of initial points.

        Returns:
            None

        """
        # 创建一个名为 types_points_and_indices 的字典 key是变量类型 value是一个二元组（值，该类型在嵌入空间的那几个维度）
        types_points_and_indices = {pt: (None, None) for pt in ParameterType}
        # sample initial points for each parameter type present in the benchmark
        # 遍历当前 benchmark 中出现的所有变量类型
        for parameter_type in self.benchmark.unique_parameter_types:

            # find number of parameters of type parameter_type
            # bins_of_type ： 列表，存放的是当前这个类型的所有 bin
            bins_of_type: list[Bin] = self.random_embedding.bins_of_type(parameter_type)
            
            # 创建一个大的 一维 Tensor，把所有索引拼接起来，就是当前类型（二分，连续，分类）所涉及的维度
            indices_of_type = torch.concat(
                [   
                    self.random_embedding.bins_and_indices_of_type(parameter_type)[i][1]
                    for i in range(len(bins_of_type))
                ]
            )
            # 根据类型调用不同采样函数
            match parameter_type:
                # 如果是二分类，会在每个 bin 生成 0 或 1
                case ParameterType.BINARY:
                    _x_init = sample_binary(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.CONTINUOUS:
                    _x_init = sample_continuous(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.CATEGORICAL:
                    _x_init = sample_categorical(
                        number_of_samples=self.number_initial_points,
                        bins=bins_of_type,
                    )
                case ParameterType.ORDINAL:
                    raise NotImplementedError(
                        "Ordinal parameters are not supported yet."
                    )
                case _:
                    raise ValueError(f"Unknown parameter type {parameter_type}.")
            # 保存采样结果和索引位置。_x_init是一个二维张量（采样点，每个维度的取值）
            types_points_and_indices[parameter_type] = (_x_init, indices_of_type)

        # 组合所有变量类型的结果成一个完整点          _x_init是一个二维张量（采样点，每个维度的取值）
        x_init = construct_mixed_point(
            # 指定生成几个点，例如你一开始想采样 10 个点
            size=self.number_initial_points,

            # 表示 binary 类型变量所在的 target 向量位置（维度索引）
            binary_indices=types_points_and_indices[ParameterType.BINARY][1],
            # 表示 binary 类型变量在这些位置上的值
            x_binary=types_points_and_indices[ParameterType.BINARY][0],

            # continuous 类型变量的嵌入索引位置
            continuous_indices=types_points_and_indices[ParameterType.CONTINUOUS][1],
            # continuous 类型变量在这些位置的值
            x_continuous=types_points_and_indices[ParameterType.CONTINUOUS][0],
            
            categorical_indices=types_points_and_indices[ParameterType.CATEGORICAL][1],
            x_categorical=types_points_and_indices[ParameterType.CATEGORICAL][0],
            
            ordinal_indices=types_points_and_indices[ParameterType.ORDINAL][1],
            x_ordinal=types_points_and_indices[ParameterType.ORDINAL][0],
        )

        # 将低维嵌入空间的点映射回原始问题空间的取值范围。
        x_init_up = from_1_around_origin(
            # .T 是 PyTorch / NumPy 中的 转置操作，把维度 [n, d] ↔ [d, n]
            x=self.random_embedding.project_up(x_init.T).T,
            lb=self.benchmark.lb_vec,
            ub=self.benchmark.ub_vec,
        )

        # 将转化之后的x送进benchmark函数，评估函数值
        fx_init = self.benchmark(x_init_up)

        # 保存结果到局部数据
        self._add_data_to_tr_observations(
            xs_down=x_init,
            xs_up=x_init_up,
            fxs=fx_init,
        )

        # 更新评估计数器
        self._n_evals += self.number_initial_points

    def run(
            self
    ):
        """
        Runs the algorithm.

        Returns:
            None

        """
        # 采样 TODO 采样的局部数据存放不是每次迭代都刷新嘛？
        self.sample_init()

        # 主循环
        # 每一轮循环执行一次 :  GP 拟合 + 新点采样 + 函数评估 + 数据更新
        while self._n_evals < self.maximum_number_evaluations:
            # 获取当前嵌入结构
            axus = self.random_embedding

            # 信赖域中的样本
            x = self.x_tr

            # 及其函数值（fx）
            fx = self.fx_tr

            # normalize data
            # 分别计算函数值 fx 的均值（mean）和标准差（std）
            mean = torch.mean(fx)
            std = torch.std(fx)
            # 避免标准差为0，除0会发生错误
            if std == 0:
                std += 1
            
            # 函数值归一化，用于训练 GP 模型
            fx_scaled = (fx - mean) / std
            # 将 target 向量从 [-1, 1] 映射到 [0, 1]
            x_scaled = (x + 1) / 2

            # 如果启用了 CUDA（GPU），把数据移到显存上，以支持 GPU 加速 GP 模型拟合
            if self.device == "cuda":
                fx_scaled = fx_scaled.to(self.device)
                x_scaled = x_scaled.to(self.device)

            # Select the kernel
            # get_gp(...) 拿到的是一个 “还没有训练好的 GP 模型”   只是把你要用的输入点和目标值先“注册”进去了，但 参数还没有拟合（fit）！
            # 这就像你开了一台洗衣机，已经放进去了衣服和水（train_x 和 train_fx），但你还没按下“启动键”——这活由 fit_mll() 干。
            model, train_x, train_fx = get_gp(
                axus=axus,
                x=x_scaled,
                fx=-fx_scaled,
            )

            # 这行代码决定是否用 scipy 库的 L-BFGS 优化器        L-BFGS 优化器主要用于优化模型的超参数
            use_scipy_lbfgs = self.use_scipy_lbfgs and (
                    # L-BFGS 优化器如果是无限次或者数据小于优化器最大迭代次数  就可以使用L-BFGS 优化器
                    self.max_lbfgs_iters is None or len(train_x) <= self.max_lbfgs_iters
            )

            # 训练GP 这一步才是真正的训练GP模型
            fit_mll(
                model=model,
                train_x=train_x,
                train_fx=-train_fx,
                max_cholesky_size=self.max_cholesky_size,
                use_scipy_lbfgs=use_scipy_lbfgs,
            )

            # 初始化两个变量，用于后续设置采集函数（acquisition）和采样器。
            acquisition_function = None
            sampler = None

            # 决定如何选择下一个评估点
            if self.batch_size > 1:
                # we don't set the acquisition function here, because it needs to be redefined
                # for each batch item to be able to condition on the earlier batch items
                # note that this is the only place where we don't use the acquisition function
                # 这是使用 Sobol 序列 + QMC 方法 来从 GP 模型的分布中采样。
                sampler = SobolQMCNormalSampler(Size([1024]), seed=self._n_evals)
            else:
                # use analytical EI for batch size 1
                acquisition_function = ExpectedImprovement(
                    model=model, best_f=(-fx_scaled).max().item()
                )

            #  如果是 纯离散空间
            if self.benchmark.is_discrete:
                # 该函数会在离散变量的信赖域中，搜索（或随机+局部）找到 batch_size 个提升潜力最大的点，并返回：
                # x_best: 找到的最好的点
                # fx_best: 这些点在 GP 模型中的预测值
                # tr_state: 当前信赖域的内部状态（记录是否需要扩张/缩小/终止等）
                x_best, fx_best, tr_state = create_candidates_discrete(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    model=model,
                    axus=axus,
                    trust_region=self.trust_region,
                    device=self.device,
                    batch_size=self.batch_size,
                    acquisition_function=acquisition_function,
                    sampler=sampler,
                )
                fx_best = fx_best * std + mean
            # 如果是 纯连续空间
            elif self.benchmark.is_continuous:
                x_best, fx_best, tr_state = create_candidates_continuous(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    acquisition_function=acquisition_function,
                    model=model,
                    axus=axus,
                    trust_region=self.trust_region,
                    device=self.device,
                    batch_size=self.batch_size,
                    sampler=sampler,
                )
                fx_best = fx_best * std + mean
            # TODO don't use elif True here but check for the exact type
            # 这个分支走的是 交替优化 1:每次先在离散部分做优化；2:然后在选出的点上，把连续变量部分继续优化；3:迭代几次，以达到更好的局部最优
            elif True:
                # Scale the function values
                # 提取出哪些维度是连续变量
                continuous_indices = torch.tensor(
                    [
                        i
                        for b, i in axus.bins_and_indices_of_type(
                        ParameterType.CONTINUOUS
                    )
                    ]
                )

                # 表示我们目前找到的“最优候选点”
                x_best = None

                # tqdm(...) 是一个进度条，可以看到循环进度
                for _ in tqdm(range(self.n_interleaved), desc="☯ Interleaved steps"):
                    
                    x_best, fx_best, tr_state = create_candidates_discrete(
                        x_scaled=x_scaled,
                        fx_scaled=fx_scaled,
                        axus=axus,
                        model=model,
                        trust_region=self.trust_region,
                        device=self.device,
                        batch_size=self.batch_size,
                        x_bests=x_best,  # expects [-1, 1],
                        acquisition_function=acquisition_function,
                        sampler=sampler,
                    )

                    # 将 x_best 转换为一个二维张量，每行的长度是 target_dim，方便后续作为输入使用 "-1"这个维度的大小表示由 PyTorch 自动推断出来
                    # 但是执行结果应该已经是一个二维张量了，所以相当于“确认一下形状
                    x_best = x_best.reshape(-1, axus.target_dim)

                    # 拿到最好的一个x作为TR的中心点
                    true_center = x[fx.argmin()]

                    # x_best[:, continuous_indices]: 拿到所有新候选点的连续部分
                    # true_center[continuous_indices]: 用历史上最好的真实解来“填充”这些连续变量
                    # .to(...): 确保它们在相同设备上（GPU / CPU）一致
                    x_best[:, continuous_indices] = true_center[continuous_indices].to(
                        device=x_best.device
                    )

                    # 连续变量优化
                    x_best, fx_best, tr_state = create_candidates_continuous(
                        x_scaled=x_scaled,
                        fx_scaled=fx_scaled,
                        axus=axus,
                        trust_region=self.trust_region,
                        device=self.device,
                        indices_to_optimize=continuous_indices,
                        x_bests=x_best,  # expects [-1, 1]
                        acquisition_function=acquisition_function,
                        model=model,
                        batch_size=self.batch_size,
                        sampler=sampler,
                    )

                    # 因为之前我们把函数值归一化了，现在还原回实际单位
                    fx_best = fx_best * std + mean
                    # 再次 reshape，保证格式一致
                    x_best = x_best.reshape(-1, axus.target_dim)

                x_best = x_best
                ''' 简单举一个例子
                batch_size = 2：一次要选两个点；
                axus.target_dim = 5：嵌入空间的维度为 5；
                continuous_indices = [1, 3]：连续变量是第 1 和第 3 维；
                

                离散优化得到的x_best：x_best =
                                [[-0.3, -1.0, 0.7, -1.0, 0.1],
                                [ 0.6, -1.0, 0.2, -1.0, -0.4]]

                reshape就是确实是否是二维张量 所以功能相当于审计了

                连续变量由 true_center 填入

                更新之后的 x_best =
                            [[-0.3, 0.6, 0.7, 0.9, 0.1],
                            [ 0.6, 0.6, 0.2, 0.9, -0.4]]

                假设优化器调整连续变量为：[0.61, 0.85], [0.59, 0.87]

                最后的 x_best =
                        [[-0.3, 0.61, 0.7, 0.85, 0.1],
                        [ 0.6, 0.59, 0.2, 0.87, -0.4]]

                第二次 reshape（再次保证形状）    
                '''
            else:
                raise NotImplementedError(
                    "Only binary and continuous benchmarks are supported."
                )
            
            # step 6
            # get the GP hyperparameters as a dictionary
            # 把当前信任区域 tr_state 保存一下（比如保存当前最优长度、维度等）；
            self.save_tr_state(tr_state)

            # 拷贝当前找到的最优点和预测值（从 GPU -> CPU）
            minimum_xs = x_best.detach().cpu()
            minimum_fxs = fx_best.detach().cpu()

            # fx_batches 初始化（用于找最小值）
            fx_batches = minimum_fxs

            # 创建空张量来放最终将被评估的高维输入点，形状为 [batch_size, 原始高维输入维度] 的空数组
            cand_batch = torch.empty(
                (self.batch_size, self.benchmark.representation_dim), dtype=self.dtype
            )

            # 准备两个列表收集低维 / 高维的观察点 xs_low_dim：记录我们在低维空间里选中的点；xs_high_dim：记录我们升维之后的点。
            xs_low_dim = list()
            xs_high_dim = list()
            
            #  下面是一个循环：每次从 x_best 中挑出一个最好的点（按 fx），投影升维后加入候选集
            for batch_index in range(self.batch_size):
                # Find the row (tr index) and column (batch index) of the minimum
                # ----- 选出当前最优值的下标 ----- fx_batches是一维张量，fx_batches.min()得到那个值，但是where()方法返回的也是一个张量，[0]就是一个标量了
                col = torch.where(fx_batches == fx_batches.min())[0]

                # Find the point that gave the minimum
                # 取出这个点
                x_elect = minimum_xs[col[0]]

                # 若 x_elect 是 1 维，则扩展维度。我们希望它是 (1, target_dim)（二维）（TODO，为什么希望是2维 ）
                if len(x_elect.shape) == 1:
                    # avoid transpose warnings
                    # 执行 x_elect.unsqueeze(0) 后，张量的内容不变，例如x_elect= [1, 2, 3, 4]，执行之后只是多了一层包裹，变成了 [[1, 2, 3, 4]]
                    x_elect = x_elect.unsqueeze(0)

                # Add the point to the lower-dimensional observations
                # 把这个低维点保存起来
                xs_low_dim.append(x_elect)

                # Project the point up to the high dimensional space
                # 投影回原始高维空间（升维），这之后的代码做了两件事：
                # 1、将低维点从 target_dim 投影回 representation_dim（原始空间）   project_up（）
                # 2、把投影后的点从 [-1, 1] 空间映射到原始变量空间                 from_1_around_origin（）
                x_elect_up = from_1_around_origin(
                    # 将低维点从 target_dim 投影回 representation_dim
                    self.random_embedding.project_up(x_elect.T).T,
                    lb=self.benchmark.lb_vec,
                    ub=self.benchmark.ub_vec,
                )

                # Add the point to the high-dimensional observations
                # 把高维点保存下来
                xs_high_dim.append(x_elect_up)

                # Add the point to the batch to be evaluated
                # squeeze() 去掉额外维度（比如 (1, 100) 变成 (100,)）；放到 cand_batch 的第 batch_index 行
                cand_batch[batch_index, :] = x_elect_up.squeeze()

                # Set the value of the minimum to infinity so that it is not selected again
                # 把已经选过的点的值设置为 inf，防止重复选择
                fx_batches[col[0]] = torch.inf

            # Sample on the candidate points
            # 正式调用黑盒函数评估 返回每个点的目标值（真实的，不是 GP 预测的了！）
            y_next = self.benchmark(cand_batch)

            # 下面的代码目的：记录本轮是否找到新的更优函数值
            best_fx = self.fx_tr.min()
            if torch.min(y_next) < best_fx:
                logging.info(
                    f"✨ Iteration {self._n_evals}: {BColors.OKGREEN}New incumbent function value {y_next.min().item():.3f}{BColors.ENDC}"
                )
            else:
                logging.debug(
                    f"🚀 Iteration {self._n_evals}: No improvement. Best function value {best_fx.item():.3f}"
                )

            # Calculate the estimated trust region dimensionality
            # 取出当前信赖域的“维度等级”（用于分预算）
            # (TODO)为什么不直接用target_dim? _forecasted_tr_dim 和 target_dim 不一定是相同的！ 为什么调用的方法没加小括号
            #  Bounce 的核心设计之一是：提前为某个维度维持一个“动态预算”系统，作为信赖域迭代分配依据
            tr_dim = self._forecasted_tr_dim

            # Number of times this trust region has been selected
            # Remaining budget for this trust region
            # 获取这个维度下还剩多少预算，不能超过总剩余评估次数，也不能为 0（否则计算 log 时会炸）。
            remaining_budget = self._all_split_budgets[tr_dim]
            remaining_budget = min(
                remaining_budget, self.maximum_number_evaluations - self._n_evals
            )
            remaining_budget = max(remaining_budget, 1)

            # 拿到当前信赖域对象
            tr = self.trust_region

            # 计算信赖域更新因子
            factor = (tr.length_min_discrete / tr.length_discrete_continuous) ** (
                    1 / remaining_budget
            )
            factor **= self.batch_size
            factor = np.clip(factor, a_min=1e-10, a_max=None)

            # 打印调整信息
            logging.debug(
                f"🔎 Adjusting trust region by factor {factor.item():.3f}. Remaining budget: {remaining_budget}"
            )

            # 更新信赖域的内部状态；作用：对比本轮评估点的最小函数值（fx_next）和历史最优（fx_incumbent）；
            # 如果没有提升，则会收缩信赖域；
            # 如果有提升，可能会扩大信赖域；
            update_tr_state(
                trust_region=self.trust_region,
                fx_next=y_next.min(),
                fx_incumbent=self.fx_tr.min(),
                adjustment_factor=factor,
            )

            # 输出信赖域当前长度信息
            logging.debug(
                f"📏 Trust region has length {tr.length_discrete_continuous:.3f} and minium l {tr.length_min_discrete:.3f}"
            )

            # 更新每个维度的预算以及执行过程中真实函数评估次数
            self._all_split_budgets[tr_dim] = (
                    self._all_split_budgets[tr_dim] - self.batch_size
            )
            self._n_evals += self.batch_size

            # 把“新获得的数据”添加到 局部数据集中
            self._add_data_to_tr_observations(
                xs_down=torch.vstack(xs_low_dim),
                xs_up=torch.vstack(xs_high_dim),
                fxs=y_next.reshape(-1),
            )

            # Splitting trust regions that terminated
            # 判断当前信赖域是否“终止”
            if self.trust_region.terminated:
                # 当前还没达到最大维度，就准备 split
                if self.random_embedding.target_dim < self.benchmark.representation_dim:
                    # Full dim is not reached yet
                    # 进入 Split 模式（增加 target_dim）
                    logging.info(f"✂️ Splitting trust region")

                    #  调用 random_embedding.split(...) 增加 target_dim
                    index_mapping = self.random_embedding.split(
                        self.number_new_bins_on_split
                    )

                    # move data to higher-dimensional space
                    # 把旧数据转换到新维度
                    self.x_tr = join_data(self.x_tr, index_mapping)
                    self.x_global = join_data(self.x_global, index_mapping)

                    # 创建一个新的信赖域对象
                    self.trust_region = TrustRegion(
                        dimensionality=self.random_embedding.target_dim
                    )

                    if self.tr_splits < self._n_splits:
                        self.tr_splits += 1

                    # 更新新的 split 预算
                    self.split_budget = self._split_budget(
                        self.initial_target_dimensionality
                        * (self.number_new_bins_on_split + 1) ** self.tr_splits
                    )
                # 当前维度 >= 最大维度，就不能split了，但是预算还没花光，所以再多迭代一次
                else:
                    # Full dim is reached
                    logging.info(
                        f"🏁 Reached full dimensionality. Restarting with new random samples."
                    )
                    # 分配预算
                    self.split_budget = self._split_budget(
                        self.random_embedding.input_dim
                    )
                    # Reset the last split budget
                    # 当前维度的预算重新设置一下
                    self._all_split_budgets[self._forecasted_tr_dim] = self.split_budget

                    # empty tr data, does not delete the global data
                    # 清空局部数据（但保留全局数据）
                    self._reset_local_data()

                    # reset the trust region
                    # 重置信赖域（但不变维度）
                    self.trust_region.reset()

                    self.sample_init()
        with lzma.open(os.path.join(self.results_dir, f"results.csv.xz"), "w") as f:
            np.savetxt(
                f,
                np.hstack(
                    (
                        self.x_up_global.detach().cpu().numpy(),
                        self.fx_global.detach().cpu().numpy().reshape(-1, 1),
                    )
                ),
                delimiter=",",
            )
    # 预测下一次split的维度
    @property
    def _forecasted_tr_dim(
            self
    ) -> int:
        """
        Calculate the estimated trust region dimensionality.

        Returns:
            the estimated trust region dimensionality

        """

        return self.initial_target_dimensionality * (
                1 + self.number_new_bins_on_split
        ) ** (self.tr_splits)

    def _add_data_to_tr_observations(
            self,
            xs_down: torch.Tensor,
            xs_up: torch.Tensor,
            fxs: torch.Tensor,
    ):
        """
        Add data to the tr local observations and save the selected trust regions to disk.

        Args:
            xs_down: the low-dimensional points that were evaluated in the trust regions
            xs_up:  the high-dimensional points that were evaluated in the trust regions
            fxs:  the function values of the high-dimensional points that were evaluated in the trust regions

        Returns:
            None

        """
        # 添加目标函数值到局部 fx_tr 中； 这是局部 GP 模型下次训练所需的目标值数据。
        self.fx_tr = torch.cat(
            (
                self.fx_tr,
                fxs.reshape(-1).detach().cpu(),
            )
        )

        # 添加低维点到局部 x_tr 中
        self.x_tr = torch.vstack(
            (
                self.x_tr,
                xs_down.detach().cpu(),
            )
        )

        # 添加升维后的点到局部 x_up_tr 中：
        self.x_up_tr = torch.vstack(
            (
                self.x_up_tr,
                xs_up.detach().cpu(),
            )
        )

        # 进入下一步，把同样的数据也追加到全局数据集中。
        self._add_data_to_global_observations(
            xs_down=xs_down,
            xs_up=xs_up,
            fxs=fxs,
        )

    def _add_data_to_global_observations(
            self,
            xs_down: torch.Tensor,
            xs_up: torch.Tensor,
            fxs: torch.Tensor,
    ):
        """
        Add data to the global observations and save the selected trust regions to disk.

        Args:
            xs_down: the low-dimensional points that were evaluated in the trust regions
            xs_up:  the high-dimensional points that were evaluated in the trust regions
            fxs:  the function values of the high-dimensional points that were evaluated in the trust regions

        Returns:
            None

        """

        self.fx_global = torch.cat(
            (
                self.fx_global,
                fxs.reshape(-1).detach().cpu(),
            )
        )
        self.x_global = torch.vstack(
            (
                self.x_global,
                xs_down.detach().cpu(),
            )
        )
        self.x_up_global = torch.vstack(
            (
                self.x_up_global,
                xs_up.detach().cpu(),
            )
        )

    def save_tr_state(
            self,
            tr_state: dict[str, Union[float, np.ndarray]],
    ):
        """
        Save the trust region state to disk.

        Args:
            tr_state: the trust region state

        Returns:
            None

        """
        for key, value in tr_state.items():
            with lzma.open(os.path.join(self.results_dir, f"{key}.csv.xz"), "a") as f:
                np.savetxt(f, value, delimiter=",")
