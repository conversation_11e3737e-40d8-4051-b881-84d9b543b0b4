import logging
from typing import Optional

import gin

# 基于PyTorch的高斯过程库
import gpytorch
import numpy as np
import torch
from botorch import fit_gpytorch_mll                    # BoTorch中用于拟合高斯过程的函数
from botorch.exceptions import ModelFittingError        # BoTorch中的模型拟合错误
from botorch.models import SingleTaskGP                 # BoTorch中的单任务高斯过程模型
from gpytorch import ExactMarginalLogLikelihood         # GPyTorch中的精确边缘似然
from gpytorch.kernels import ScaleKernel                # GPyTorch中的缩放核
from gpytorch.likelihoods import GaussianLikelihood     # GPyTorch中的高斯似然
from torch import Tensor

from bounce import settings
from bounce.kernel.categorical_mixture import MixtureKernel
from bounce.projection import AxUS
from bounce.util.benchmark import ParameterType


@gin.configurable
def get_gp(
        axus: AxUS,
        x: Tensor,
        fx: Tensor,
        lengthscale_prior_shape: float = 1.5,
        lengthscale_prior_rate: float = 0.1,
        outputscale_prior_shape: float = 1.5,
        outputscale_prior_rate: float = 0.5,
        noise_prior_shape: float = 1.1,
        noise_prior_rate: float = 0.05,
        lamda: Optional[float] = None,
        discrete_ard: bool = False,
        continuous_ard: bool = True,
) -> tuple[SingleTaskGP, Tensor, Tensor]:
    # 箭头是 类型提示，表示这个函数的返回值类型【一个单任务的高斯过程模型，归一化时用到的均值，归一化时用到的标准差】
    """
    Define the GP model.

    Args:

        axus: AxUS：
            AxUS（Axis-aligned Uniform Subspace）对象，用于处理高维空间到低维空间的映射
            包含了参数类型信息（连续、离散等）和维度信息
        x: Tensor：
            输入点的张量，形状为 [n_points, dim]
            这些点是在低维嵌入空间中的坐标
        fx: Tensor：
            在输入点处的函数值，形状为 [n_points]
            这些是我们要建模的目标值
        先验参数：
            lengthscale_prior_shape, lengthscale_prior_rate：控制核函数中长度尺度的伽马先验
            outputscale_prior_shape, outputscale_prior_rate：控制核函数输出尺度的伽马先验
            noise_prior_shape, noise_prior_rate：控制观测噪声的伽马先验
            这些先验参数影响GP模型的平滑度和灵活性
        lamda: Optional[float]：
            混合核中的权重参数，控制离散核和连续核的相对重要性
            如果为None，则这个参数是可训练的
        discrete_ard: bool：
            是否对离散参数使用自动相关性确定（ARD）
            ARD允许每个维度有不同的长度尺度，增加模型灵活性
        continuous_ard: bool：
            是否对连续参数使用ARD
            默认为True，表示连续参数总是使用ARD
        axus: the AxUS object
        x: the input points
        fx: the function values at the input points
        lengthscale_prior_shape: the shape parameter of the lengthscale prior
        lengthscale_prior_rate: the rate parameter of the lengthscale prior
        outputscale_prior_shape: the shape parameter of the outputscale prior
        outputscale_prior_rate: the rate parameter of the outputscale prior
        noise_prior_shape: the shape parameter of the noise prior
        noise_prior_rate: the rate parameter of the noise prior
        lamda: the parameter for the weighted average in the mixturekernel. trainable if set to None
        discrete_ard: whether to use ARD for discrete parameters
        continuous_ard: whether to use ARD for continuous parameters

    Returns:
        the GP model, the input points, and the function values at the input points

    """

    assert not discrete_ard, "ARD for discrete parameters is not supported yet"
    assert continuous_ard, "ARD for continuous parameters is always used"

    # 找到连续类型的 bin 对应的列索引 TODO b（bin对象）没用上
    continuous_dims = np.asarray(
        [i.item() for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)]
    )

    # 用 全集 - 连续维度 得到离散维度索引
    discrete_dims = np.setdiff1d(np.arange(axus.target_dim), continuous_dims)

    """
        如果只有连续维度：使用Matern核（nu=2.5）与ARD
        如果只有离散维度：使用Matern核（nu=2.5）但不使用ARD
        如果既有连续又有离散维度：使用自定义的MixtureKernel
    """
    if len(discrete_dims) == 0:
        kernel = gpytorch.kernels.MaternKernel(
            nu=2.5,
            ard_num_dims=axus.target_dim,
            lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            # botorch 3,6
        )
    elif len(continuous_dims) == 0:
        kernel = gpytorch.kernels.MaternKernel(
            nu=2.5,
            ard_num_dims=None,
            lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            # botorch 3,6
        )
    else:
        kernel = MixtureKernel(
            discrete_dims=discrete_dims.tolist(),
            continuous_dims=continuous_dims.tolist(),
            discrete_lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            continuous_lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            lamda=lamda,
        )

    # 构建协方差模块（加权缩放 outputscale_prior）添加输出尺度先验
    # 控制变化的“快慢” →  lengthscale
    # 控制变化的“大小” →  outputscale

    covar_module = ScaleKernel(
        # Use the same lengthscale prior as in the TuRBO paper
        kernel,
        outputscale_prior=gpytorch.priors.GammaPrior(
            outputscale_prior_shape, outputscale_prior_rate
        ),
        # 1.5, 1, botorch: 2, 0.15
    )
    # detach（）不带梯度信息
    train_x = x.detach().clone()
    # fx[:, None] 的作用是把一维张量变成二维列向量。
    train_fx = fx[:, None].detach().clone()

    # Define the model
    # 定义似然函数：创建GaussianLikelihood对象，添加噪声先验
    likelihood = GaussianLikelihood(
        noise_prior=gpytorch.priors.GammaPrior(noise_prior_shape, noise_prior_rate)
    )

    # 使用训练数据、协方差模块和似然函数创建SingleTaskGP模型
    model = SingleTaskGP(
        train_X=train_x,
        train_Y=train_fx,
        covar_module=covar_module,
        likelihood=likelihood,
    )
    return model, train_x, train_fx


def fit_mll(
        model: SingleTaskGP,
        train_x: Tensor,
        train_fx: Tensor,
        max_cholesky_size: int = 1000,
        use_scipy_lbfgs: bool = True,
) -> None:
    """
    Fit the GP model. If the LBFGS optimizer fails, use the Adam optimizer.

    Args:

        model: SingleTaskGP：
            要训练的高斯过程模型，是BoTorch库中的SingleTaskGP类的实例
            这个模型包含了核函数、似然函数等组件，但其参数尚未优化
        train_x: Tensor：
            训练数据的输入点，形状为 [n_points, dim]
            这些是模型将学习的样本点
        train_fx: Tensor：
            训练数据的函数值，形状为 [n_points, 1] 或 [n_points]
            这些是模型将拟合的目标值
        max_cholesky_size: int = 1000：
            Cholesky分解的最大矩阵大小
            Cholesky分解用于计算协方差矩阵的逆，是GP训练的关键步骤
            当数据点较多时，限制Cholesky分解的大小可以提高计算效率
        use_scipy_lbfgs: bool = True：
            是否使用scipy的L-BFGS优化器
            L-BFGS是一种拟牛顿法，通常比梯度下降法更高效
            如果设为False或L-BFGS失败，将使用Adam优化器作为备选

        model: the GP model
        train_x: the input points
        train_fx: the function values at the input points
        max_cholesky_size: the maximum size of the Cholesky decomposition
         use_scipy_lbfgs: whether to use the scipy LBFGS optimizer, otherwise use the Adam optimizer

    Returns:
        None

    """
    # Set model to training mode
    # 这两行代码将模型和似然函数设置为训练模式，这在PyTorch中是常见的做法。训练模式会启用梯度计算和参数更新。
    model.train()
    model.likelihood.train()
    # ExactMarginalLogLikelihood是GPyTorch中的一个类，用于计算精确的边缘对数似然。
    # 在GP中，我们通过最大化边缘似然来优化模型参数。
    mll = ExactMarginalLogLikelihood(model.likelihood, model)

    # 这行代码使用上下文管理器设置Cholesky分解的最大矩阵大小，这有助于控制内存使用和计算效率。
    with gpytorch.settings.max_cholesky_size(max_cholesky_size):
        # 如果 use_scipy_lbfgs为True，函数会尝试使用BoTorch的fit_gpytorch_mll函数，该函数内部使用scipy的L-BFGS优化器。
        # 如果优化失败（抛出ModelFittingError异常），则标记lbgs_failed为True。
        lbgs_failed = False
        if use_scipy_lbfgs:
            try:
                fit_gpytorch_mll(
                    mll=mll,
                    model=model,
                    train_x=train_x,
                    train_fx=train_fx,
                )
                model.eval()
            except ModelFittingError:
                lbgs_failed = True

        # 如果L-BFGS优化器失败或未使用，函数会使用PyTorch的Adam优化器作为备选。这部分代码：
        # 创建Adam优化器，学习率为0.1 
        # 循环settings.MLL_FITTING_ITERATIONS次（在 settings.py中定义，通常为100次） 在每次迭代中：
            # 清除之前的梯度
            # 使用模型对训练输入进行预测
            # 计算负边缘对数似然作为损失函数（负号是因为我们要最大化似然，而优化器是最小化损失）
            # 反向传播计算梯度
            # 更新模型参数
        if not use_scipy_lbfgs or lbgs_failed:
            if lbgs_failed:
                logging.warning(
                    "⚠ Failed to fit GP using LBFGS, using backup Adam optimizer"
                )
            optimizer = torch.optim.Adam([{"params": model.parameters()}], lr=0.1)

            for _ in range(settings.MLL_FITTING_ITERATIONS):
                optimizer.zero_grad()
                output = model(train_x)
                loss = -mll(output, train_fx.flatten())
                loss.backward()
                optimizer.step()

    model.eval()
    model.likelihood.eval()
