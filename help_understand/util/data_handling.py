# Optional[X] 表示某个参数可以是 X 类型，也可以是 None；
from typing import Optional

import numpy as np
import torch

# BoTorch 内置的归一化工具函数 
# normalize(x, bounds): 将 x 映射到 [0,1] 区间；
# unnormalize(x, bounds): 将 x 从 [0,1] 区间映射回原始区间 bounds。
from botorch.utils.transforms import normalize, unnormalize
from numpy.random import RandomState

# PyTorch 提供的 Sobol 序列采样器（低差异序列） Bounce 用它来采 continuous 变量
from torch.quasirandom import SobolEngine

from bounce.util.benchmark import Parameter, ParameterType

"""

该文件的作用就是归一化、随机采样、拼装成完整变量、判断benchmark涉及的变量类型



"""




# 这是 split 发生后调用的函数，用来 把旧的低维数据复制到新扩展出的高维空间中。
def join_data(
        x: torch.Tensor,                                       # 原始数据张量
        index_mapping: dict[torch.Tensor, list[torch.Tensor]]  # [旧列索引(key) → 新列索引列表的映射(value)]
) -> torch.Tensor:
    """
    Update data after increasing the target dimensionality.

    Args:
        x: data
        index_mapping: dictionary of indices before splitting and indices of equal data after splitting

    Returns:

    """

    # find max index before splitting
    # 找出旧数据中使用的最大索引 目的：为新张量分配空间时知道原始数据用了多少列。
    max_index_before_splitting = max(torch.concat(list(index_mapping.keys())))

    # find non-empty index lists
    # 过滤出非空的“新列索引组”；
    index_lists_after_splitting = [index_list_after_splitting for index_list_after_splitting in
                                   index_mapping.values() if len(index_list_after_splitting) > 0]
    
    # find max index after splitting, set to max index before splitting if no splitting was done
    # 提取所有新索引，并找到最大值；为新数据 new_x 分配正确的总维度。
    # 如果存在“分割后的索引列表”（即 index_lists_after_splitting 不为空），
    # 就从这些列表中找出最大的索引值；否则就使用之前的最大索引值 max_index_before_splitting。
    max_index_after_splitting = max(
        torch.cat([torch.cat(index_list_after_splitting) for index_list_after_splitting in index_lists_after_splitting])
    ) if len(index_lists_after_splitting) > 0 else max_index_before_splitting

    # create new tensor with correct shape
    # 创建新的张量容器,二维张量 形状((x.shape[0], max_index_after_splitting + 1))
    new_x = torch.empty((x.shape[0], max_index_after_splitting + 1), dtype=x.dtype)

    # copy old data
    # 将原始数据复制到新张量的前面部分
    new_x[:, :max_index_before_splitting + 1] = x

    # fill new data with equal data
    # 执行复制（按映射将旧列复制给新列）
    for indcs_old, indcss_new in index_mapping.items():
        _x = x[:, indcs_old]
        for indcs_new in indcss_new:
            new_x[:, indcs_new] = _x

    return new_x
"""
举例：
step1：
                x = tensor([            表示有两个样本、3 个特征维度。
                    [0.1, 0.2, 0.3],
                    [0.4, 0.5, 0.6]
                ])  # shape = (2, 3)

                我们构造如下 index_mapping：（假设每个维度都复制成两个新维度）
                index_mapping = {
                    tensor([0]): [tensor([0, 3])],
                    tensor([1]): [tensor([1, 4])],
                    tensor([2]): [tensor([2, 5])]
                }

step2：         找最大旧索引
                max_index_before_splitting = 2

step3：         找到最大新索引
                index_lists_after_splitting = [
                    [tensor([0, 3])],
                    [tensor([1, 4])],
                    [tensor([2, 5])]
                ]
                # 扁平展开所有新索引：
                new_indices = torch.cat([
                    torch.cat([tensor([0, 3])]),
                    torch.cat([tensor([1, 4])]),
                    torch.cat([tensor([2, 5])])
                ])
                # new_indices = tensor([0,3,1,4,2,5])
                max_index_after_splitting = max(new_indices) = 5

step4:          创建新张量
                new_x = torch.empty((2, 6), dtype=x.dtype)  # shape = (2, 6)

step5:          复制旧数据到新张量
                new_x[:, :3] = x  # 复制原始数据到新张

                此时：
                new_x =
                [
                    [0.1, 0.2, 0.3, ?, ?, ?],
                    [0.4, 0.5, 0.6, ?, ?, ?]
                ]

step6：         执行复制
                [
                [0.1, 0.2, 0.3, 0.1, ?, ?],
                [0.4, 0.5, 0.6, 0.4, ?, ?]
                ]

                [
                [0.1, 0.2, 0.3, 0.1, 0.2, ?],
                [0.4, 0.5, 0.6, 0.4, 0.5, ?]
                ]

                [
                [0.1, 0.2, 0.3, 0.1, 0.2, 0.3],
                [0.4, 0.5, 0.6, 0.4, 0.5, 0.6]
                ]
"""
# 归一化到 [0, 1]^d 
def to_unit_cube(
        x,
        lb,
        ub
):
    """Project to [0, 1]^d from hypercube with bounds lb and ub"""
    assert lb.ndim == 1 and ub.ndim == 1 and x.ndim == 2
    # dim=0 表示按行堆叠
    # dim=1 表示按列堆叠
    xx = normalize(x, torch.stack([lb, ub], dim=0))
    return xx

# 先将 x 映射到 [0,1]，再线性变换到 [-1,1]
# 使得模型输入更中心对称（更易于训练）
def to_1_around_origin(
        x,
        lb,
        ub
):
    """Project to [-1, 1]^d from hypercube with bounds lb and ub"""
    assert lb.ndim == 1 and ub.ndim == 1 and x.ndim == 2
    x = to_unit_cube(x, lb, ub)
    xx = x * 2 - 1
    # xx = (x - lb) / (ub - lb)
    return xx

# 将 [0,1] 区间中的向量 反归一化 回原始的空间（实际变量范围）
def from_unit_cube(
        x,
        lb,
        ub
):
    """Project from [0, 1]^d to hypercube with bounds lb and ub"""
    assert lb.ndim == 1 and ub.ndim == 1 and x.ndim == 2
    # dim=0 表示按行堆叠
    # dim=1 表示按列堆叠
    xx = unnormalize(x, torch.stack([lb, ub], dim=0))
    return xx

# 先把 [-1, 1] 映射回 [0, 1]，再映射回原始变量范围。
def from_1_around_origin(
        x,
        lb,
        ub
):
    xx = (x + 1) / 2
    return from_unit_cube(xx, lb, ub)

# x_init.shape = (初始采样点个数, binary 占的维度数)
def sample_binary(
        number_of_samples: int,                 # 样本数量（即 batch size）
        bins: list['Bin'],                      # 当前维度的结构描述（一个二元变量）
        dtype: torch.dtype = torch.double,
        seed: Optional[int] = None,
):
    """

    Args:
        number_of_samples: the number of samples
        bins: the dimensionality of the samples
        dtype: the dtype of the samples
        seed: the seed for the random number generator

    Returns:
        the samples

    """

    for bin in bins:
        assert bin.parameter_type == ParameterType.BINARY, f"Parameter {bin.parameter} is not binary."
    if seed is None:
        seed = np.random.randint(0, 2 ** 32 - 1)
    
    # 创建一个 RandomState(seed) 对象，具有可控性 ；从 [-1, 1] 中随机选值，表示每个变量是 -1 或 +1；
    # 生成一个形状为 (number_of_samples, len(bins)) 的矩阵。
    x_init = RandomState(seed).choice(
        [-1, 1],
        size=(number_of_samples, len(bins))
    )
    x_init = torch.tensor(x_init, dtype=dtype)
    return x_init

# 连续采样
def sample_continuous(
        number_of_samples: int,
        bins: list['Bin'],
        dtype: torch.dtype = torch.double,
        seed: Optional[int] = None,
):
    """

    Args:
        number_of_samples: the number of samples
        bins: the dimensionality of the samples
        dtype: the dtype of the samples
        seed: the seed for the random number generator

    Returns:
        the samples

    """
    for bin in bins:
        assert bin.parameter_type == ParameterType.CONTINUOUS, f"Parameter {bin.parameter} is not continuous."
    # PyTorch 的 SobolEngine 只能原生生成 [0, 1]^d 上的样本。
    sobol = SobolEngine(len(bins), scramble=True, seed=seed)

    # 然后映射到[-1,1]上
    x_init = sobol.draw(number_of_samples).to(dtype=dtype) * 2 - 1
    return x_init

# 类别采样
def sample_categorical(
        number_of_samples: int,
        bins: list['Bin'],
        dtype: torch.dtype = torch.double,
        seed: Optional[int] = None,
):
    """

    Args:
        number_of_samples: the number of samples
        bins: the dimensionality of the samples
        dtype: the dtype of the samples
        seed: the seed for the random number generator

    Returns:
        the samples

    """
    for bin in bins:
        assert bin.parameter_type == ParameterType.CATEGORICAL, f"Parameter {bin.parameter} is not categorical."

    if seed is None:
        seed = np.random.randint(0, 2 ** 32 - 1)

    # 计算总输出维度，统计所有的类别变量的维度
    dim = sum(bin.dims_required for bin in bins)

    # 创建空张量（样本数量，总维度）
    x_init = torch.zeros((number_of_samples, dim), dtype=dtype)
    start = 0

    # 遍历一个叫 bins 的列表（或其它可迭代对象），同时获取每个元素的索引 i 和对应的值 bin。
    for i, bin in enumerate(bins):
        # 当前变量所需维度：bin.dims_required； start是该维度的起点，end是该维度的终点；需要end-start + 1列来one-hot编码
        end = start + bin.dims_required

        # 选择哪一个为1呗，one-hot编码嘛,总是需要一个1的。（可选维度，采样数量）一次性生成所有样本的结果
        idxs = RandomState(seed + i).choice(
            bin.dims_required,
            size=number_of_samples
        )

        # 创建一个全0的mask，然后把选中的位置（idxs）设为1
        mask = torch.zeros((number_of_samples, bin.dims_required), dtype=x_init.dtype)
        mask[torch.arange(number_of_samples), idxs] = 1

        # 把 one-hot 填入总张量中的对应列；
        x_init[:, start:end] = mask
        start = end

    return x_init * 2 - 1


def construct_mixed_point(
        size: int,                                              # 样本数量（行数）
        binary_indices: Optional[list] = None,                  # *_indices  每种变量在最终输出中的列位置（用于插入）
        continuous_indices: Optional[list] = None,              # x_*	     已采样好的变量数据张量，shape = (size, 变量维度数)
        categorical_indices: Optional[list] = None,
        ordinal_indices: Optional[list] = None,
        x_binary: Optional[torch.Tensor] = None,
        x_continuous: Optional[torch.Tensor] = None,
        x_categorical: Optional[torch.Tensor] = None,
        x_ordinal: Optional[torch.Tensor] = None,
) -> torch.Tensor:
    """
    Construct a mixed point from the different types of points

    Args:
        size: the number of points
        binary_indices: indices of the binary parameters
        continuous_indices: indices of the continuous parameters
        categorical_indices: indices of the categorical parameters
        ordinal_indices: indices of the ordinal parameters
        x_binary: the binary point
        x_continuous: the continuous point
        x_categorical: the categorical point
        x_ordinal: the ordinal point

    Returns:
        the mixed point

    """

    # any of the x_* can be None, but not all of them
    # 防止全部输入为空；
    assert x_binary is not None or x_continuous is not None or x_categorical is not None or x_ordinal is not None, 'All x_* are None'
    
    # 检查每个输入张量的行数和 size 是否一致
    if x_binary is not None:
        assert x_binary.size(0) == size, 'x_binary has wrong size'
    if x_continuous is not None:
        assert x_continuous.size(0) == size, 'x_continuous has wrong size'
    if x_categorical is not None:
        assert x_categorical.size(0) == size, 'x_categorical has wrong size'
    if x_ordinal is not None:
        assert x_ordinal.size(0) == size, 'x_ordinal has wrong size'

    # 检查每种变量列数是否与 *_indices 匹配：
    # 就是你要插入的位置（假设是1号位置和3号位置，有两哥位置）要和已经采样好的值（-1,1  第一个位置-1，第二个位置1）二者数量要一样
    if x_binary is not None:
        assert len(binary_indices) > 0, 'Benchmark does not have binary parameters but x_binary is not None'
        assert x_binary.size(1) == len(binary_indices), 'x_binary has wrong size'
    if x_continuous is not None:
        assert len(continuous_indices) > 0, 'Benchmark does not have continuous parameters but x_continuous is not None'
        assert x_continuous.size(1) == len(continuous_indices), 'x_continuous has wrong size'
    if x_categorical is not None:
        assert len(
            categorical_indices
        ) > 0, 'Benchmark does not have categorical parameters but x_categorical is not None'
        assert x_categorical.size(1) == len(categorical_indices), 'x_categorical has wrong size'
    if x_ordinal is not None:
        assert len(ordinal_indices) > 0, 'Benchmark does not have ordinal parameters but x_ordinal is not None'
        assert x_ordinal.size(1) == len(ordinal_indices), 'x_ordinal has wrong size'

    # 计算总参数维度
    total_n_params = sum(
        [
            len(binary_indices) if binary_indices is not None else 0,
            len(continuous_indices) if continuous_indices is not None else 0,
            len(categorical_indices) if categorical_indices is not None else 0,
            len(ordinal_indices) if ordinal_indices is not None else 0,
        ]
    )

    # 创建目标张量 x
    x = torch.zeros((size, total_n_params), dtype=torch.double)

    # 按索引填入各类变量
    if x_binary is not None:
        x[:, binary_indices] = x_binary
    if x_continuous is not None:
        x[:, continuous_indices] = x_continuous
    if x_categorical is not None:
        x[:, categorical_indices] = x_categorical
    if x_ordinal is not None:
        x[:, ordinal_indices] = x_ordinal

    return x


# 提取参数类型工具函数 很多模块都需要知道当前 benchmark 涉及哪些类型：
def parameter_types(
        parameters: list[Parameter]
) -> list[ParameterType]:
    """

        Args:
            parameters: the parameters

        Returns:
            the unique parameter types

        """
    # 获得当前 benchmark 的参数类型列表
    return list(set([p.type for p in parameters]))
