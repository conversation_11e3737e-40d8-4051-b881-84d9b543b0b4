import pandas as pd
import glob
import pathlib

# 递归查找所有实验的eval_history.csv
result_files = sorted(glob.glob("results/MaxSat125/*/*/eval_history.csv"), key=lambda x: pathlib.Path(x).stat().st_mtime)
all_histories = []
all_steps = []

for f in result_files:
    df = pd.read_csv(f)
    all_histories.append(df["当前最优值"].values)
    all_steps.append(df["评估次数"].values)

# 🔧 修复：使用最小长度而不是最大长度，确保所有实验在相同评估次数下比较
min_len = min(len(h) for h in all_histories)
max_len = max(len(h) for h in all_histories)

print(f"发现长度不一致：最小长度={min_len}, 最大长度={max_len}")
if min_len != max_len:
    print(f"⚠️  检测到长度不一致的实验数据，将截断到最小长度 {min_len} 以确保公平比较")

    # 统计不同长度的文件数量
    length_counts = {}
    for i, h in enumerate(all_histories):
        length = len(h)
        if length not in length_counts:
            length_counts[length] = []
        length_counts[length].append(result_files[i])

    print("长度分布:")
    for length, files in sorted(length_counts.items()):
        print(f"  长度 {length}: {len(files)} 个文件")
        if length != min_len:
            print(f"    异常文件示例: {files[0]}")

# 截断所有序列到最小长度
aligned = []
for h in all_histories:
    aligned.append(h[:min_len])  # 截断到最小长度

aligned = pd.DataFrame(aligned).T
aligned.columns = [f"实验{i+1}" for i in range(len(result_files))]

# 使用第一个文件的步数列（截断到最小长度）
step_col = all_steps[0][:min_len]
aligned.insert(0, "评估次数", step_col)
aligned["均值"] = aligned.iloc[:, 1:].mean(axis=1)
aligned.to_csv("eval_history.csv", index=False, float_format="%.8f", encoding="utf-8-sig")
print(f"合并完成，共{len(result_files)}个实验，统一长度为{min_len}，结果已保存到eval_history.csv")
