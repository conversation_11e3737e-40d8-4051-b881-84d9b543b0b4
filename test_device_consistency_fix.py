#!/usr/bin/env python3
"""
测试设备一致性修复效果
"""

import torch
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_calculate_weighted_scores_fix():
    """测试 calculate_weighted_scores 修复效果"""
    print("🔧 测试 calculate_weighted_scores 设备一致性修复...")
    
    from bounce.ensemble_manager import CandidateSelector
    
    # 创建选择器
    selector = CandidateSelector(top_k=3, softmax_temperature=5.0)
    
    # 模拟在不同设备上的预测结果
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"测试设备: {device}")
    
    # 创建模拟数据（在CUDA上）
    predictions_dict = {
        'gp': torch.tensor([-60.6399, -60.6399, -60.6399, -60.6398, -60.6400], device=device, dtype=torch.float64),
        'rbf': torch.tensor([-60.6401, -60.6401, -60.6401, -60.6399, -60.6402], device=device, dtype=torch.float64),
        'tabpfn': torch.tensor([-78.8389, -71.1235, -71.1235, -70.5000, -69.8000], device=device, dtype=torch.float64)
    }
    
    weights = {'gp': 0.333, 'rbf': 0.333, 'tabpfn': 0.334}
    
    try:
        # 测试加权分数计算
        weighted_scores = selector.calculate_weighted_scores(predictions_dict, weights)
        
        print(f"✅ 加权分数计算成功")
        print(f"   输出设备: {weighted_scores.device}")
        print(f"   输出形状: {weighted_scores.shape}")
        print(f"   输出值: {weighted_scores[:3]}...")
        
        # 检查设备一致性
        expected_device = list(predictions_dict.values())[0].device
        device_consistent = weighted_scores.device == expected_device
        print(f"   设备一致性: {'✅ 通过' if device_consistent else '❌ 失败'}")
        
        return device_consistent
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_normalize_predictions_fix():
    """测试 normalize_predictions 修复效果"""
    print("\n🔧 测试 normalize_predictions 设备一致性修复...")
    
    from bounce.ensemble_manager import CandidateSelector
    
    selector = CandidateSelector()
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 测试不同情况的预测值
    test_cases = [
        ("正常范围", torch.tensor([-60.6399, -70.1235, -65.8000], device=device, dtype=torch.float64)),
        ("相同值", torch.tensor([-60.6399, -60.6399, -60.6399], device=device, dtype=torch.float64)),
        ("单个值", torch.tensor([-60.6399], device=device, dtype=torch.float64))
    ]
    
    for case_name, predictions in test_cases:
        try:
            normalized = selector.normalize_predictions(predictions)
            
            print(f"✅ {case_name} 归一化成功")
            print(f"   输入设备: {predictions.device}")
            print(f"   输出设备: {normalized.device}")
            print(f"   设备一致性: {'✅ 通过' if normalized.device == predictions.device else '❌ 失败'}")
            print(f"   归一化范围: [{normalized.min():.4f}, {normalized.max():.4f}]")
            
        except Exception as e:
            print(f"❌ {case_name} 测试失败: {e}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("🔍 设备一致性修复验证测试")
    print("=" * 60)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    # 执行测试
    test_results = []
    
    # 1. 测试加权分数计算修复
    test_results.append(test_calculate_weighted_scores_fix())
    
    # 2. 测试归一化修复
    test_results.append(test_normalize_predictions_fix())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有设备一致性修复测试通过！")
        print("💡 现在可以运行主程序测试实际效果。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)