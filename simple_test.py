#!/usr/bin/env python3
"""
最简单的K-means测试
"""

import gin
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

def simple_test():
    print("🧬 简单K-means测试")
    
    try:
        # 导入必要模块
        from bounce.genetic_algorithm import GAConfig
        print("✅ GAConfig导入成功")
        
        # 测试配置
        config = GAConfig(use_kmeans_elite_selection=True, elite_clusters_ratio=0.5)
        print(f"✅ GAConfig创建成功: K-means={config.use_kmeans_elite_selection}")
        
        # 加载gin配置
        gin.clear_config()
        gin.parse_config_file("configs/default.gin")
        print("✅ 配置文件加载成功")
        
        # 创建Bounce
        from bounce.bounce import Bounce
        bounce = Bounce()
        print(f"✅ Bounce创建成功")
        print(f"   基准函数: {bounce.benchmark.fun_name}")
        print(f"   K-means精英选择: {bounce.ga_tabpfn_integration.ga_config.use_kmeans_elite_selection}")
        print(f"   精英比例: {bounce.ga_tabpfn_integration.ga_config.elitism_rate}")
        print(f"   聚类比例: {bounce.ga_tabpfn_integration.ga_config.elite_clusters_ratio}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print("🎉 K-means集成测试通过！")
    else:
        print("❌ 测试失败")