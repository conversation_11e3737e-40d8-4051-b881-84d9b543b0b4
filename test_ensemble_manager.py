#!/usr/bin/env python3
"""
集成代理模型测试脚本
测试EnsembleManager的基本功能，包括三模型协同工作和权重管理
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import numpy as np
from bounce.ensemble_manager import EnsembleManager, WeightManager, CandidateSelector
from bounce.projection import AxUS
from bounce.util.benchmark import Parameter, ParameterType

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MockBenchmark:
    """简单的模拟基准函数"""
    
    def __init__(self, dim=4):
        self.dim = dim
        self.representation_dim = dim
        # 创建混合变量参数：2个二进制 + 2个连续变量
        self.parameters = [
            Parameter(name="x0", type=ParameterType.BINARY),
            Parameter(name="x1", type=ParameterType.BINARY),
            Parameter(name="x2", type=ParameterType.CONTINUOUS, lower_bound=0.0, upper_bound=1.0),
            Parameter(name="x3", type=ParameterType.CONTINUOUS, lower_bound=0.0, upper_bound=1.0),
        ]
        self.lb_vec = torch.zeros(dim, dtype=torch.float64)
        self.ub_vec = torch.ones(dim, dtype=torch.float64)
        
    def __call__(self, x):
        """简单的测试函数：Sphere函数"""
        if x.dim() == 1:
            x = x.unsqueeze(0)
        return torch.sum(x**2, dim=1)


def test_weight_manager():
    """测试权重管理器"""
    print("\n🧪 测试WeightManager...")
    
    weight_manager = WeightManager(['gp', 'rbf', 'tabpfn'])
    
    # 初始权重应该均分
    weights = weight_manager.get_weights()
    print(f"初始权重: {weights}")
    assert abs(weights['gp'] - 1/3) < 0.01
    assert abs(weights['rbf'] - 1/3) < 0.01
    assert abs(weights['tabpfn'] - 1/3) < 0.01
    
    # 记录一些预测结果
    weight_manager.record_prediction('gp', 1.0, 0.9)  # 好的预测
    weight_manager.record_prediction('rbf', 1.0, 1.5)  # 差的预测
    weight_manager.record_prediction('tabpfn', 1.0, 1.1)  # 中等预测
    
    # 更新权重
    weight_manager.update_weights()
    updated_weights = weight_manager.get_weights()
    print(f"更新后权重: {updated_weights}")
    
    # GP应该有最高权重（预测最准确）
    assert updated_weights['gp'] > updated_weights['rbf']
    assert updated_weights['gp'] > updated_weights['tabpfn']
    
    print("✅ WeightManager测试通过")


def test_candidate_selector():
    """测试候选点选择器"""
    print("\n🧪 测试CandidateSelector...")
    
    selector = CandidateSelector(top_k=3, softmax_temperature=5.0)
    
    # 创建模拟预测结果
    candidates = torch.rand(10, 4)
    predictions_dict = {
        'gp': torch.rand(10),
        'rbf': torch.rand(10),
        'tabpfn': torch.rand(10)
    }
    weights = {'gp': 0.5, 'rbf': 0.3, 'tabpfn': 0.2}
    
    # 选择最佳候选点
    best_candidate = selector.select_best_candidate(candidates, predictions_dict, weights)
    
    print(f"候选点形状: {candidates.shape}")
    print(f"选择的最佳候选点: {best_candidate}")
    assert best_candidate.shape == (4,)
    
    print("✅ CandidateSelector测试通过")


def test_ensemble_manager():
    """测试集成管理器"""
    print("\n🧪 测试EnsembleManager...")
    
    # 创建模拟对象
    benchmark = MockBenchmark(dim=4)
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    # 创建集成管理器
    ensemble_manager = EnsembleManager(
        benchmark=benchmark,
        axus=axus,
        device='cpu',
        model_types=['gp', 'rbf', 'tabpfn'],
        weight_window_size=10,
        min_weight_protection=0.1,
        top_k_candidates=3,
        softmax_temperature=2.0
    )
    
    # 检查初始化
    info = ensemble_manager.get_ensemble_info()
    print(f"集成信息: {info}")
    assert info['model_types'] == ['gp', 'rbf', 'tabpfn']
    assert not info['is_fitted']
    
    # 准备训练数据
    X_train = torch.rand(15, 4, dtype=torch.float64)
    y_train = torch.rand(15, dtype=torch.float64)
    
    # 训练集成模型
    print("训练集成模型...")
    ensemble_manager.fit(X_train, y_train)
    
    # 检查训练状态
    info = ensemble_manager.get_ensemble_info()
    print(f"训练后集成信息: {info}")
    assert info['is_fitted']
    
    # 生成候选点并预测最佳中心
    candidates = torch.rand(8, 4) * 2 - 1  # [-1, 1]范围
    
    print("预测最佳中心点...")
    best_center = ensemble_manager.predict_best_center(
        candidates=candidates,
        existing_X=None,  # 已经训练过了
        existing_y=None
    )
    
    print(f"候选点形状: {candidates.shape}")
    print(f"最佳中心点: {best_center}")
    assert best_center.shape == (4,)
    
    # 测试权重更新
    print("测试权重更新...")
    predictions_dict = ensemble_manager.predict_all_models(X_train[:5])
    ensemble_manager.record_performance(predictions_dict, y_train[:5])
    
    updated_weights = ensemble_manager.weight_manager.get_weights()
    print(f"权重更新后: {updated_weights}")
    
    print("✅ EnsembleManager测试通过")


def test_ensemble_integration():
    """测试集成模式与GATabPFNIntegration的整合"""
    print("\n🧪 测试集成模式整合...")
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    
    # 创建模拟对象
    benchmark = MockBenchmark(dim=4)
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    ga_config = GAConfig(
        population_size=10,
        max_generations=3,
        crossover_rate=0.8,
        mutation_rate=0.15
    )
    
    # 测试集成模式
    print("创建集成模式GA集成...")
    ga_integration_ensemble = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        ga_config=ga_config,
        device='cpu',
        enable_ensemble=True  # 启用集成模式
    )
    
    assert ga_integration_ensemble.enable_ensemble == True
    assert ga_integration_ensemble.ensemble_manager is not None
    assert ga_integration_ensemble.global_surrogate is None
    
    # 测试单一模式
    print("创建单一模式GA集成...")
    ga_integration_single = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        ga_config=ga_config,
        device='cpu',
        global_model_type='gp',
        enable_ensemble=False  # 禁用集成模式
    )
    
    assert ga_integration_single.enable_ensemble == False
    assert ga_integration_single.ensemble_manager is None
    assert ga_integration_single.global_surrogate is not None
    
    print("✅ 集成模式整合测试通过")


def test_ensemble_prediction():
    """测试集成预测流程"""
    print("\n🧪 测试集成预测流程...")
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    
    # 创建对象
    benchmark = MockBenchmark(dim=4)
    axus = AxUS(parameters=benchmark.parameters, n_bins=4)
    
    ga_config = GAConfig(population_size=8, max_generations=2)
    
    ga_integration = GATabPFNIntegration(
        axus=axus,
        benchmark=benchmark,
        ga_config=ga_config,
        device='cpu',
        enable_ensemble=True
    )
    
    # 准备数据
    X_existing = torch.rand(12, 4, dtype=torch.float64)
    y_existing = torch.rand(12, dtype=torch.float64)
    
    # 测试预测
    print("运行集成预测...")
    best_center = ga_integration.predict_best_center_with_tabpfn(
        existing_X=X_existing,
        existing_y=y_existing,
        n_candidates=10
    )
    
    print(f"集成预测的最佳中心: {best_center}")
    assert best_center.shape == (4,)
    
    # 获取集成信息
    if hasattr(ga_integration, 'ensemble_manager'):
        ensemble_info = ga_integration.ensemble_manager.get_ensemble_info()
        print(f"集成模型信息: {ensemble_info}")
    
    print("✅ 集成预测流程测试通过")


if __name__ == "__main__":
    try:
        print("🚀 开始集成代理模型测试...")
        
        test_weight_manager()
        test_candidate_selector()
        test_ensemble_manager()
        test_ensemble_integration()
        test_ensemble_prediction()
        
        print("\n" + "=" * 60)
        print("🎉 所有集成代理模型测试通过！")
        print("✅ WeightManager - 权重管理正常")
        print("✅ CandidateSelector - 候选点选择正常") 
        print("✅ EnsembleManager - 集成管理正常")
        print("✅ GATabPFNIntegration - 集成模式正常")
        print("✅ 集成预测流程正常")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()