#!/usr/bin/env python3
"""
验证混合变量修复效果
"""

import torch
import logging
import sys
import os

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_ackley53_ga_initialization():
    """测试Ackley53问题的GA初始化"""
    print("🧪 测试Ackley53问题GA初始化修复效果")
    print("=" * 60)
    
    from bounce.benchmarks import Ackley53
    from bounce.projection import AxUS
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.util.benchmark import ParameterType
    
    # 创建Ackley53基准（50维二进制 + 3维连续）
    benchmark = Ackley53()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    print(f"基准函数: Ackley53")
    print(f"总维度: {len(benchmark.parameters)}")
    print(f"AxUS目标维度: {axus.target_dim}")
    
    # 获取变量类型索引
    binary_indices = []
    continuous_indices = []
    
    for _, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
        binary_indices.extend(indices)
    
    for _, indices in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
        continuous_indices.extend(indices)
    
    print(f"二分变量索引数量: {len(binary_indices)}")
    print(f"连续变量索引数量: {len(continuous_indices)}")
    print(f"二分变量索引: {binary_indices[:10]}...")  # 显示前10个
    print(f"连续变量索引: {continuous_indices}")
    
    # 创建GA
    ga_config = GAConfig(population_size=10, max_generations=1)
    ga = MixedVariableGA(ga_config, axus, benchmark)
    
    print(f"\nGA变量索引分析:")
    print(f"   GA二分变量索引: {ga.binary_indices[:10]}...")
    print(f"   GA连续变量索引: {ga.continuous_indices}")
    print(f"   GA类别变量索引: {ga.categorical_indices}")
    
    # 初始化种群
    ga.initialize_population()
    
    print(f"\n🔍 检查初始化种群...")
    all_valid = True
    
    for i, individual in enumerate(ga.population[:3]):  # 检查前3个个体
        genes = individual.genes
        print(f"\n个体{i+1}:")
        print(f"   基因形状: {genes.shape}")
        print(f"   前10个基因: {genes[:10]}")
        print(f"   后5个基因: {genes[-5:]}")
        
        # 检查二分变量
        if len(ga.binary_indices) > 0:
            binary_genes = genes[ga.binary_indices]
            unique_binary = torch.unique(binary_genes)
            is_binary_discrete = torch.all(torch.isin(unique_binary, torch.tensor([-1.0, 1.0])))
            
            print(f"   二分变量唯一值: {unique_binary}")
            print(f"   二分变量是否离散: {is_binary_discrete}")
            
            if not is_binary_discrete:
                all_valid = False
                print(f"   ❌ 二分变量包含非{-1,1}值!")
            else:
                print(f"   ✅ 二分变量完全离散")
        
        # 检查连续变量
        if len(ga.continuous_indices) > 0:
            continuous_genes = genes[ga.continuous_indices]
            in_range = torch.all((continuous_genes >= -1) & (continuous_genes <= 1))
            
            print(f"   连续变量范围: [{continuous_genes.min():.4f}, {continuous_genes.max():.4f}]")
            print(f"   连续变量范围正确: {in_range}")
            
            if not in_range:
                all_valid = False
                print(f"   ❌ 连续变量超出[-1,1]范围!")
            else:
                print(f"   ✅ 连续变量范围正确")
    
    return all_valid

def test_ackley53_ga_evolution():
    """测试Ackley53问题的GA进化过程"""
    print("\n🔄 测试Ackley53问题GA进化过程")
    print("=" * 60)
    
    from bounce.benchmarks import Ackley53
    from bounce.projection import AxUS
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    
    # 创建Ackley53基准
    benchmark = Ackley53()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    # 创建GA
    ga_config = GAConfig(population_size=8, max_generations=2)
    ga = MixedVariableGA(ga_config, axus, benchmark)
    
    # 初始化种群
    ga.initialize_population()
    
    print(f"初始种群大小: {len(ga.population)}")
    
    # 进化几代并检查离散性
    all_generations_valid = True
    
    for generation in range(2):
        print(f"\n第{generation+1}代进化:")
        
        # 进化一代
        ga.evolve_generation()
        
        # 检查进化后的离散性
        generation_valid = True
        for i, individual in enumerate(ga.population[:2]):  # 检查前2个个体
            genes = individual.genes
            
            # 检查二分变量
            if len(ga.binary_indices) > 0:
                binary_genes = genes[ga.binary_indices]
                unique_binary = torch.unique(binary_genes)
                is_binary_discrete = torch.all(torch.isin(unique_binary, torch.tensor([-1.0, 1.0])))
                
                print(f"   个体{i+1} 二分变量是否离散: {is_binary_discrete}")
                print(f"   个体{i+1} 二分变量唯一值: {unique_binary}")
                
                if not is_binary_discrete:
                    generation_valid = False
                    print(f"   ❌ 个体{i+1}包含非离散的二分变量!")
                
            # 检查连续变量
            if len(ga.continuous_indices) > 0:
                continuous_genes = genes[ga.continuous_indices]
                in_range = torch.all((continuous_genes >= -1) & (continuous_genes <= 1))
                
                print(f"   个体{i+1} 连续变量范围正确: {in_range}")
                
                if not in_range:
                    generation_valid = False
                    print(f"   ❌ 个体{i+1}连续变量超出范围!")
        
        if generation_valid:
            print(f"   ✅ 第{generation+1}代所有变量保持正确性")
        else:
            all_generations_valid = False
            print(f"   ❌ 第{generation+1}代存在变量类型错误")
    
    return all_generations_valid

def main():
    """主测试函数"""
    print("🔍 混合变量修复验证测试")
    print("=" * 80)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    # 执行测试
    test_results = []
    
    # 1. 测试GA初始化
    test_results.append(test_ackley53_ga_initialization())
    
    # 2. 测试GA进化
    test_results.append(test_ackley53_ga_evolution())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有混合变量修复测试通过！")
        print("💡 现在Ackley53问题的GA应该能正确处理混合变量了。")
        print("🔧 二分变量将严格保持{-1,1}值，连续变量保持连续性。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    print("=" * 80)
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
    sys.exit(0 if success else 1)