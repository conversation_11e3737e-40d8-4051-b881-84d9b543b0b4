#!/usr/bin/env python3
"""
测试K-means聚类精英选择机制
"""

import logging
import gin
import torch
import numpy as np
from bounce.bounce import <PERSON><PERSON><PERSON>

def test_kmeans_elite_selection():
    """测试K-means精英选择机制"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🧬 测试K-means聚类精英选择机制")
    
    # 测试MaxSat60问题
    print("\n=== 测试MaxSat60问题 ===")
    gin.clear_config()
    gin.parse_config_file("configs/maxsat60_kmeans.gin")
    
    bounce_maxsat = Bounce()
    print(f"✅ MaxSat60配置加载成功")
    print(f"   - 基准函数: {bounce_maxsat.benchmark.fun_name}")
    print(f"   - 维度: {bounce_maxsat.benchmark.dim}")
    print(f"   - GA配置: 种群大小={bounce_maxsat.ga_tabpfn_integration.ga_config.population_size}")
    print(f"   - K-means精英选择: {bounce_maxsat.ga_tabpfn_integration.ga_config.use_kmeans_elite_selection}")
    print(f"   - 精英比例: {bounce_maxsat.ga_tabpfn_integration.ga_config.elitism_rate}")
    print(f"   - 聚类比例: {bounce_maxsat.ga_tabpfn_integration.ga_config.elite_clusters_ratio}")
    
    # 运行几步验证K-means功能
    print("\\n🔍 初始化并测试GA的K-means精英选择...")
    bounce_maxsat.sample_init()
    
    # 运行一步全局搜索
    if len(bounce_maxsat.x_up_global) >= 5:
        try:
            best_low_dim, best_high_dim = bounce_maxsat.ga_tabpfn_integration.run_global_search(
                existing_X=bounce_maxsat.x_up_global,
                existing_y=bounce_maxsat.fx_global
            )
            print(f"✅ MaxSat60的K-means精英选择运行成功")
            
            # 获取GA种群的多样性信息
            ga = bounce_maxsat.ga_tabpfn_integration.ga
            diversity = ga.get_population_diversity()
            print(f"   - 种群多样性: {diversity:.4f}")
            print(f"   - 当前代数: {ga.generation}")
            print(f"   - 最佳适应度: {ga.best_individual.fitness:.6f}")
            
        except Exception as e:
            print(f"❌ MaxSat60测试失败: {e}")
    
    gin.clear_config()
    
    # 测试Ackley53问题
    print("\\n=== 测试Ackley53问题 ===")
    gin.parse_config_file("configs/ackley53_kmeans.gin")
    
    bounce_ackley = Bounce()
    print(f"✅ Ackley53配置加载成功")
    print(f"   - 基准函数: {bounce_ackley.benchmark.fun_name}")
    print(f"   - 维度: {bounce_ackley.benchmark.dim}")
    print(f"   - GA配置: 种群大小={bounce_ackley.ga_tabpfn_integration.ga_config.population_size}")
    print(f"   - K-means精英选择: {bounce_ackley.ga_tabpfn_integration.ga_config.use_kmeans_elite_selection}")
    print(f"   - 精英比例: {bounce_ackley.ga_tabpfn_integration.ga_config.elitism_rate}")
    print(f"   - 聚类比例: {bounce_ackley.ga_tabpfn_integration.ga_config.elite_clusters_ratio}")
    
    # 运行几步验证K-means功能
    print("\\n🔍 初始化并测试GA的K-means精英选择...")
    bounce_ackley.sample_init()
    
    # 运行一步全局搜索
    if len(bounce_ackley.x_up_global) >= 5:
        try:
            best_low_dim, best_high_dim = bounce_ackley.ga_tabpfn_integration.run_global_search(
                existing_X=bounce_ackley.x_up_global,
                existing_y=bounce_ackley.fx_global
            )
            print(f"✅ Ackley53的K-means精英选择运行成功")
            
            # 获取GA种群的多样性信息
            ga = bounce_ackley.ga_tabpfn_integration.ga
            diversity = ga.get_population_diversity()
            print(f"   - 种群多样性: {diversity:.4f}")
            print(f"   - 当前代数: {ga.generation}")
            print(f"   - 最佳适应度: {ga.best_individual.fitness:.6f}")
            
        except Exception as e:
            print(f"❌ Ackley53测试失败: {e}")
    
    gin.clear_config()
    print("\\n🎉 K-means聚类精英选择机制测试完成！")

if __name__ == "__main__":
    test_kmeans_elite_selection()