#!/usr/bin/env python3
"""
快速测试TabPFN修复效果
"""

import torch
import logging
from bounce.benchmarks import MaxSat60
from bounce.projection import AxUS
from bounce.ensemble_manager import EnsembleManager

# 设置日志级别为INFO以查看关键信息
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

print('🔍 测试集成管理器修复效果...')

# 创建小规模测试
benchmark = MaxSat60()
axus = AxUS(parameters=benchmark.parameters, n_bins=4)

ensemble_manager = EnsembleManager(
    benchmark=benchmark,
    axus=axus,
    device='cpu',
    model_types=['gp', 'rbf', 'tabpfn'],
    weight_window_size=5,
    min_weight_protection=0.1,
    top_k_candidates=3
)

# 创建有差异的训练数据
X_train = torch.zeros(8, benchmark.representation_dim, dtype=torch.float64)
for i in range(8):
    X_train[i, :20] = torch.rand(20)  # 只让前20维有变化
    X_train[i, 20:] = i / 8.0  # 后面维度有系统性差异

y_train = torch.linspace(-150, -50, 8, dtype=torch.float64)  # 系统性函数值
print(f'训练数据: X形状={X_train.shape}, y范围=[{y_train.min():.2f}, {y_train.max():.2f}]')

# 测试训练
ensemble_manager.fit(X_train, y_train)

# 创建有差异的测试数据
X_test = torch.zeros(3, benchmark.representation_dim, dtype=torch.float64)
for i in range(3):
    X_test[i, :20] = torch.rand(20)
    X_test[i, 20:] = (i + 0.5) / 3.0

# 预测测试
predictions_dict = ensemble_manager.predict_all_models(X_test)
print(f'获得预测: {list(predictions_dict.keys())}')

# 检查TabPFN预测值范围
if 'tabpfn' in predictions_dict:
    tabpfn_pred = predictions_dict['tabpfn']
    print(f'TabPFN预测范围: [{tabpfn_pred.min():.4f}, {tabpfn_pred.max():.4f}]')
    print(f'TabPFN预测值类型: 预测值还是质量分数？')
    if tabpfn_pred.min() < -10:  # 如果是负值，说明是转换后的预测值
        print('✅ TabPFN输出转换后的预测值')
    else:
        print('❌ TabPFN输出仍然是质量分数')
else:
    print('⚠️ TabPFN未参与预测')

# 测试性能记录
y_actual = torch.tensor([-100, -80, -60], dtype=torch.float64)
print(f'\n真实值: {y_actual}')

# 记录性能前的权重
weights_before = ensemble_manager.weight_manager.get_weights()
print(f'记录前权重: {weights_before}')

# 记录性能
ensemble_manager.record_performance(predictions_dict, y_actual)

# 记录性能后的权重
weights_after = ensemble_manager.weight_manager.get_weights()
print(f'记录后权重: {weights_after}')

# 检查各模型的MAE
for model_name in ensemble_manager.model_types:
    mae = ensemble_manager.weight_manager.calculate_mae(model_name)
    history_count = len(ensemble_manager.weight_manager.performance_history[model_name])
    print(f'{model_name.upper()} MAE: {mae:.4f}, 历史数据: {history_count}条')

print('\n✅ 快速测试完成')