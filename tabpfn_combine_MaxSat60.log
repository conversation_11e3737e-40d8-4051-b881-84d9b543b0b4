nohup: ignoring input
[37m INFO:2025-09-04 16:11:54,341 - (main.py:25) - [37m
    	    ____                            
    	   / __ )____  __  ______  ________
    	  / __  / __ \/ / / / __ \/ ___/ _ \
    	 / /_/ / /_/ / /_/ / / / / /__/  __/
    	/_____/\____/\__,_/_/ /_/\___/\___/
    
    [ Bounce: Reliable High-Dimensional Bayesian Optimization     ] 
    [ Algorithm for Combinatorial and Mixed Spaces ]
    [0m [0m
[37m INFO:2025-09-04 16:11:54,345 - (main.py:69) - 第1次实验开始 [0m
[37m INFO:2025-09-04 16:11:54,369 - (bounce.py:231) - 初始维度5维，算法目标维度60维 [0m
[37m INFO:2025-09-04 16:11:54,370 - (bounce.py:232) - 🤖 <PERSON><PERSON><PERSON> will split at most 4 times.  每次分裂1个箱子 [0m
[37m INFO:2025-09-04 16:11:54,370 - (bounce.py:235) - 🤖 初始5维，有1 次评估预算  [0m
[37m INFO:2025-09-04 16:11:54,371 - (bounce.py:280) - 每一个可能维度以及评估预算：{5: 1, 10: 3, 20: 6, 40: 12, 80: 200}；以及总预算为50次 [0m
[37m INFO:2025-09-04 16:11:54,371 - (bounce.py:294) - 🔄 策略切换机制已启用，切换阈值: 3步 [0m
[37m INFO:2025-09-04 16:11:54,372 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,372 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,372 - (gp_surrogate.py:53) - GP全局代理模型初始化完成，设备: cpu, 参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True} [0m
[37m INFO:2025-09-04 16:11:54,372 - (surrogate_manager.py:60) - 创建GP代理模型，参数: {} [0m
[37m INFO:2025-09-04 16:11:54,373 - (ensemble_manager.py:539) - ✅ GP模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,373 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,373 - (rbf_surrogate.py:47) - RBF全局代理模型初始化完成，设备: cpu, 核函数: gaussian, epsilon: 1.0 [0m
[37m INFO:2025-09-04 16:11:54,373 - (surrogate_manager.py:66) - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0 [0m
[37m INFO:2025-09-04 16:11:54,374 - (ensemble_manager.py:539) - ✅ RBF模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,374 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,374 - (tabpfn_surrogate.py:39) - TabPFN初始化成功，设备: cpu [0m
[37m INFO:2025-09-04 16:11:54,374 - (tabpfn_surrogate.py:73) - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 60, 类别变量: 0 [0m
[37m INFO:2025-09-04 16:11:54,374 - (surrogate_manager.py:55) - 创建TabPFN代理模型，n_bins: 5 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:539) - ✅ TABPFN模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:40) - 权重管理器初始化完成，模型: ['gp', 'rbf', 'tabpfn'] [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:41) - 初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:561) - 🌟 集成代理模型管理器初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:562) -    设备: cpu [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:563) -    支持模型: ['gp', 'rbf', 'tabpfn'] [0m
[37m INFO:2025-09-04 16:11:54,376 - (ensemble_manager.py:564) -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:54,376 - (ga_tabpfn_integration.py:65) - 🌟 集成代理模型系统已启用 [0m
[37m INFO:2025-09-04 16:11:54,376 - (ga_tabpfn_integration.py:98) - GA-集成代理模型集成系统初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,391 - (ga_tabpfn_integration.py:134) - 🎆 首次训练集成代理模型，数据量: 5 [0m
[37m INFO:2025-09-04 16:11:54,612 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 5, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:11:54,612 - (ensemble_manager.py:584) - GP模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,614 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 5 [0m
[37m INFO:2025-09-04 16:11:54,614 - (ensemble_manager.py:584) - RBF模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,777 - (tabpfn_surrogate.py:305) - TabPFN模型训练完成，训练样本数: 5, 类别数: 4 [0m
[37m INFO:2025-09-04 16:11:54,778 - (ensemble_manager.py:584) - TABPFN模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,778 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 5) [0m
[37m INFO:2025-09-04 16:11:54,779 - (genetic_algorithm.py:186) - 动态种群大小: 65 (目标维度: 5) [0m
[37m INFO:2025-09-04 16:11:54,782 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 65 [0m
[37m INFO:2025-09-04 16:11:58,447 - (genetic_algorithm.py:367) - 集成代理模型评估了65个GA个体，使用权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:58,451 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:11:58,452 - (ensemble_manager.py:718) - 📊 输入训练数据: 5个样本 (上次: 0) [0m
[37m INFO:2025-09-04 16:11:58,452 - (ensemble_manager.py:727) - 🔄 新增5个珍贵样本，立即更新模型 [0m
[37m INFO:2025-09-04 16:11:58,452 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,489 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 5, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:11:58,490 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,491 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,606 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 5 [0m
[37m WARNING:2025-09-04 16:11:58,611 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-29.700618 [0m
[37m WARNING:2025-09-04 16:11:58,612 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-29.700618 [0m
[37m WARNING:2025-09-04 16:11:58,614 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-30.390577 [0m
[37m WARNING:2025-09-04 16:11:58,615 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-30.390577 [0m
[37m INFO:2025-09-04 16:12:01,183 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:01,183 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:01,184 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-29.7006, -29.7006] → 归一化[0.0000, 1.0000], 均值-29.7006→0.7054 [0m
[37m INFO:2025-09-04 16:12:01,184 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-30.3906, -30.3906] → 归一化[0.5000, 0.5000], 均值-30.3906→0.5000 [0m
[37m INFO:2025-09-04 16:12:01,185 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-38.4005, -30.9941] → 归一化[0.0000, 1.0000], 均值-35.9150→0.3356 [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第42个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:848) -    📊 GP: 原始分-29.7006 → 归一化1.0000, 权重0.3333, 加权分0.3333 [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:848) -    📊 RBF: 原始分-30.3906 → 归一化0.5000, 权重0.3333, 加权分0.1667 [0m
[37m INFO:2025-09-04 16:12:01,189 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-33.2065 → 归一化0.7013, 权重0.3333, 加权分0.2338 [0m
[37m INFO:2025-09-04 16:12:01,190 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:02,222 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:128) -    🟡 GP: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:128) -    🟡 RBF: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:128) -    🟡 TABPFN: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00000): [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    GP: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    RBF: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    TABPFN: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,225 - (bounce.py:857) - ✨ Iteration 5: [92mNew incumbent function value -53.247[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:02,226 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 6 [0m
[37m INFO:2025-09-04 16:12:02,226 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,268 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 6, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:02,269 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,269 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,372 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 6 [0m
[37m INFO:2025-09-04 16:12:04,780 - (genetic_algorithm.py:367) - 集成代理模型评估了42个GA个体，使用权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:04,785 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:04,785 - (ensemble_manager.py:718) - 📊 输入训练数据: 6个样本 (上次: 6) [0m
[37m WARNING:2025-09-04 16:12:04,788 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-34.410035 [0m
[37m WARNING:2025-09-04 16:12:04,789 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-34.410035 [0m
[37m WARNING:2025-09-04 16:12:04,790 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-34.200047 [0m
[37m WARNING:2025-09-04 16:12:04,791 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-34.200047 [0m
[37m INFO:2025-09-04 16:12:07,428 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:07,429 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:07,429 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-34.4100, -34.4100] → 归一化[0.0000, 1.0000], 均值-34.4100→0.4700 [0m
[37m INFO:2025-09-04 16:12:07,430 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-34.2000, -34.2000] → 归一化[0.5000, 0.5000], 均值-34.2000→0.5000 [0m
[37m INFO:2025-09-04 16:12:07,430 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-43.5319, -35.7559] → 归一化[0.0000, 1.0000], 均值-41.8915→0.2110 [0m
[37m INFO:2025-09-04 16:12:07,432 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第19个候选点 tensor([-1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:07,432 - (ensemble_manager.py:848) -    📊 GP: 原始分-34.4100 → 归一化1.0000, 权重0.3333, 加权分0.3333 [0m
[37m INFO:2025-09-04 16:12:07,433 - (ensemble_manager.py:848) -    📊 RBF: 原始分-34.2000 → 归一化0.5000, 权重0.3333, 加权分0.1667 [0m
[37m INFO:2025-09-04 16:12:07,433 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-36.8018 → 归一化0.8655, 权重0.3333, 加权分0.2885 [0m
[37m INFO:2025-09-04 16:12:07,435 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:08,634 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:128) -    🟡 GP: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:128) -    🟡 RBF: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:128) -    🟡 TABPFN: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:227) - 🔄 检测到性能差异(8.0988)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.18303): [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:326) -    GP: 0.3333 ↘ 0.2409 (MAE:38.9480, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:326) -    RBF: 0.3333 ↘ 0.2428 (MAE:38.7080, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,637 - (ensemble_manager.py:326) -    TABPFN: 0.3333 ↗ 0.5164 (MAE:30.8492, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,637 - (bounce.py:857) - ✨ Iteration 6: [92mNew incumbent function value -88.759[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:08,637 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 7 [0m
[37m INFO:2025-09-04 16:12:08,638 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,666 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 7, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:08,667 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,668 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,762 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 7 [0m
[37m INFO:2025-09-04 16:12:11,054 - (genetic_algorithm.py:367) - 集成代理模型评估了45个GA个体，使用权重: {'gp': 0.2408768477894236, 'rbf': 0.24275800497601976, 'tabpfn': 0.5163651472345567} [0m
[37m INFO:2025-09-04 16:12:11,057 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:11,058 - (ensemble_manager.py:718) - 📊 输入训练数据: 7个样本 (上次: 7) [0m
[37m WARNING:2025-09-04 16:12:11,062 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-43.468166 [0m
[37m WARNING:2025-09-04 16:12:11,062 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-43.468166 [0m
[37m WARNING:2025-09-04 16:12:11,063 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-41.994225 [0m
[37m WARNING:2025-09-04 16:12:11,064 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-41.994225 [0m
[37m INFO:2025-09-04 16:12:13,714 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2408768477894236, 'rbf': 0.24275800497601976, 'tabpfn': 0.5163651472345567} [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-43.4682, -43.4682] → 归一化[0.0000, 1.0000], 均值-43.4682→0.6167 [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-41.9942, -41.9942] → 归一化[0.5000, 0.5000], 均值-41.9942→0.5000 [0m
[37m INFO:2025-09-04 16:12:13,716 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-62.9084, -48.9668] → 归一化[0.0000, 1.0000], 均值-59.6589→0.2331 [0m
[37m INFO:2025-09-04 16:12:13,718 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第32个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 GP: 原始分-43.4682 → 归一化0.6667, 权重0.2409, 加权分0.1606 [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 RBF: 原始分-41.9942 → 归一化0.5000, 权重0.2428, 加权分0.1214 [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-48.9668 → 归一化1.0000, 权重0.5164, 加权分0.5164 [0m
[37m INFO:2025-09-04 16:12:13,720 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:14,932 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:14,932 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 GP: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 RBF: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 TABPFN: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:227) - 🔄 检测到性能差异(11.8914)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02117): [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    GP: 0.2409 ↓ 0.2324 (MAE:45.6896, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    RBF: 0.2428 ↘ 0.2301 (MAE:46.0210, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    TABPFN: 0.5164 ↗ 0.5375 (MAE:34.1295, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,935 - (bounce.py:857) - ✨ Iteration 7: [92mNew incumbent function value -102.641[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:14,935 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 8 [0m
[37m INFO:2025-09-04 16:12:14,936 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 8 [0m
[37m INFO:2025-09-04 16:12:14,963 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 8, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:14,964 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 8 [0m
[37m INFO:2025-09-04 16:12:14,965 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 8 [0m
[37m INFO:2025-09-04 16:12:15,060 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 8 [0m
[37m INFO:2025-09-04 16:12:17,934 - (genetic_algorithm.py:367) - 集成代理模型评估了46个GA个体，使用权重: {'gp': 0.2323568274215991, 'rbf': 0.23011205907289042, 'tabpfn': 0.5375311135055104} [0m
[37m INFO:2025-09-04 16:12:17,937 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:17,937 - (ensemble_manager.py:718) - 📊 输入训练数据: 8个样本 (上次: 8) [0m
[37m WARNING:2025-09-04 16:12:17,942 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-51.921679 [0m
[37m WARNING:2025-09-04 16:12:17,943 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-51.921679 [0m
[37m WARNING:2025-09-04 16:12:17,944 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-49.575075 [0m
[37m WARNING:2025-09-04 16:12:17,945 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-49.575075 [0m
[37m INFO:2025-09-04 16:12:20,871 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:20,872 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2323568274215991, 'rbf': 0.23011205907289042, 'tabpfn': 0.5375311135055104} [0m
[37m INFO:2025-09-04 16:12:20,872 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-51.9217, -51.9217] → 归一化[0.0000, 1.0000], 均值-51.9217→0.5914 [0m
[37m INFO:2025-09-04 16:12:20,873 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-49.5751, -49.5751] → 归一化[0.5000, 0.5000], 均值-49.5751→0.5000 [0m
[37m INFO:2025-09-04 16:12:20,873 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-76.0021, -60.7574] → 归一化[0.0000, 1.0000], 均值-71.8186→0.2744 [0m
[37m INFO:2025-09-04 16:12:20,874 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第22个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:20,874 - (ensemble_manager.py:848) -    📊 GP: 原始分-51.9217 → 归一化0.8483, 权重0.2324, 加权分0.1971 [0m
[37m INFO:2025-09-04 16:12:20,875 - (ensemble_manager.py:848) -    📊 RBF: 原始分-49.5751 → 归一化0.5000, 权重0.2301, 加权分0.1151 [0m
[37m INFO:2025-09-04 16:12:20,875 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-60.7574 → 归一化1.0000, 权重0.5375, 加权分0.5375 [0m
[37m INFO:2025-09-04 16:12:20,876 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:21,998 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:21,998 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 GP: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 RBF: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 TABPFN: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:227) - 🔄 检测到性能差异(14.0441)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02389): [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:326) -    GP: 0.2324 ↘ 0.2222 (MAE:44.1666, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:326) -    RBF: 0.2301 ↘ 0.2163 (MAE:45.0018, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,001 - (ensemble_manager.py:326) -    TABPFN: 0.5375 ↗ 0.5614 (MAE:30.9577, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,001 - (bounce.py:864) - 🚀 Iteration 8: No improvement. Best function value -102.641 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:22,001 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:12:22,002 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 9 [0m
[37m INFO:2025-09-04 16:12:22,003 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,031 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 9, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:22,032 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,033 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,133 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,134 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 10) [0m
[37m INFO:2025-09-04 16:12:22,134 - (genetic_algorithm.py:186) - 动态种群大小: 80 (目标维度: 10) [0m
[37m INFO:2025-09-04 16:12:22,139 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 80 [0m
[37m WARNING:2025-09-04 16:12:22,253 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m WARNING:2025-09-04 16:12:22,254 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m INFO:2025-09-04 16:12:25,971 - (genetic_algorithm.py:367) - 集成代理模型评估了80个GA个体，使用权重: {'gp': 0.22224779123477634, 'rbf': 0.21633237875593175, 'tabpfn': 0.561419830009292} [0m
[37m INFO:2025-09-04 16:12:25,974 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:25,974 - (ensemble_manager.py:718) - 📊 输入训练数据: 9个样本 (上次: 9) [0m
[37m WARNING:2025-09-04 16:12:25,978 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-56.871040 [0m
[37m WARNING:2025-09-04 16:12:25,979 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-56.871040 [0m
[37m WARNING:2025-09-04 16:12:25,980 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m WARNING:2025-09-04 16:12:25,981 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m INFO:2025-09-04 16:12:28,549 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:28,550 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.22224779123477634, 'rbf': 0.21633237875593175, 'tabpfn': 0.561419830009292} [0m
[37m INFO:2025-09-04 16:12:28,550 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-56.8710, -56.8710] → 归一化[0.0000, 1.0000], 均值-56.8710→0.9786 [0m
[37m INFO:2025-09-04 16:12:28,551 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-54.2355, -54.2355] → 归一化[0.5000, 0.5000], 均值-54.2355→0.5000 [0m
[37m INFO:2025-09-04 16:12:28,551 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-79.6129, -42.4961] → 归一化[0.0000, 1.0000], 均值-62.2404→0.4681 [0m
[37m INFO:2025-09-04 16:12:28,552 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第25个候选点 tensor([-1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:28,552 - (ensemble_manager.py:848) -    📊 GP: 原始分-56.8710 → 归一化1.0000, 权重0.2222, 加权分0.2222 [0m
[37m INFO:2025-09-04 16:12:28,553 - (ensemble_manager.py:848) -    📊 RBF: 原始分-54.2355 → 归一化0.5000, 权重0.2163, 加权分0.1082 [0m
[37m INFO:2025-09-04 16:12:28,553 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-42.8675 → 归一化0.9900, 权重0.5614, 加权分0.5558 [0m
[37m INFO:2025-09-04 16:12:28,554 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1.,  1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:128) -    🟡 GP: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:128) -    🟡 RBF: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:128) -    🟡 TABPFN: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.2963)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01407): [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:326) -    GP: 0.2222 ↓ 0.2163 (MAE:46.6503, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (ensemble_manager.py:326) -    RBF: 0.2163 ↓ 0.2082 (MAE:47.8455, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (ensemble_manager.py:326) -    TABPFN: 0.5614 ↗ 0.5755 (MAE:31.5492, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (bounce.py:857) - ✨ Iteration 9: [92mNew incumbent function value -113.456[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:29,744 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 10 [0m
[37m INFO:2025-09-04 16:12:29,744 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,779 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 10, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:29,780 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,781 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,872 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 10 [0m
[37m WARNING:2025-09-04 16:12:29,955 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m WARNING:2025-09-04 16:12:29,956 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m INFO:2025-09-04 16:12:32,757 - (genetic_algorithm.py:367) - 集成代理模型评估了58个GA个体，使用权重: {'gp': 0.21627751846143584, 'rbf': 0.20823729980724226, 'tabpfn': 0.5754851817313219} [0m
[37m INFO:2025-09-04 16:12:32,760 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:32,760 - (ensemble_manager.py:718) - 📊 输入训练数据: 10个样本 (上次: 10) [0m
[37m WARNING:2025-09-04 16:12:32,764 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-63.158300 [0m
[37m WARNING:2025-09-04 16:12:32,765 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-63.158300 [0m
[37m WARNING:2025-09-04 16:12:32,766 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m WARNING:2025-09-04 16:12:32,767 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21627751846143584, 'rbf': 0.20823729980724226, 'tabpfn': 0.5754851817313219} [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-63.1583, -63.1583] → 归一化[0.0000, 1.0000], 均值-63.1583→0.9800 [0m
[37m INFO:2025-09-04 16:12:35,653 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-60.1576, -60.1576] → 归一化[0.5000, 0.5000], 均值-60.1576→0.5000 [0m
[37m INFO:2025-09-04 16:12:35,653 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-87.7971, -58.8256] → 归一化[0.0000, 1.0000], 均值-75.5795→0.4217 [0m
[37m INFO:2025-09-04 16:12:35,654 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第34个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 GP: 原始分-63.1583 → 归一化1.0000, 权重0.2163, 加权分0.2163 [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 RBF: 原始分-60.1576 → 归一化0.5000, 权重0.2082, 加权分0.1041 [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-59.8315 → 归一化0.9653, 权重0.5755, 加权分0.5555 [0m
[37m INFO:2025-09-04 16:12:35,656 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:36,803 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 GP: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 RBF: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 TABPFN: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.8626)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02172): [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:326) -    GP: 0.2163 ↓ 0.2066 (MAE:45.5068, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (ensemble_manager.py:326) -    RBF: 0.2082 ↘ 0.1962 (MAE:47.0030, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (ensemble_manager.py:326) -    TABPFN: 0.5755 ↗ 0.5972 (MAE:29.1403, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (bounce.py:864) - 🚀 Iteration 10: No improvement. Best function value -113.456 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:36,807 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 11 [0m
[37m INFO:2025-09-04 16:12:36,807 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,841 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 11, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:36,841 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,842 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,934 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 11 [0m
[37m WARNING:2025-09-04 16:12:37,000 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m WARNING:2025-09-04 16:12:37,002 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m INFO:2025-09-04 16:12:39,414 - (genetic_algorithm.py:367) - 集成代理模型评估了45个GA个体，使用权重: {'gp': 0.20656575978112623, 'rbf': 0.196227876571623, 'tabpfn': 0.5972063636472509} [0m
[37m INFO:2025-09-04 16:12:39,421 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:39,421 - (ensemble_manager.py:718) - 📊 输入训练数据: 11个样本 (上次: 11) [0m
[37m WARNING:2025-09-04 16:12:39,426 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-67.137049 [0m
[37m WARNING:2025-09-04 16:12:39,428 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-67.137049 [0m
[37m WARNING:2025-09-04 16:12:39,429 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m WARNING:2025-09-04 16:12:39,431 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m INFO:2025-09-04 16:12:42,243 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:42,243 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.20656575978112623, 'rbf': 0.196227876571623, 'tabpfn': 0.5972063636472509} [0m
[37m INFO:2025-09-04 16:12:42,244 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-67.1370, -67.1370] → 归一化[0.0000, 1.0000], 均值-67.1370→0.9791 [0m
[37m INFO:2025-09-04 16:12:42,247 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-64.0476, -64.0476] → 归一化[0.5000, 0.5000], 均值-64.0476→0.5000 [0m
[37m INFO:2025-09-04 16:12:42,247 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-91.2199, -73.8646] → 归一化[0.0000, 1.0000], 均值-87.9948→0.1858 [0m
[37m INFO:2025-09-04 16:12:42,250 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第46个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 GP: 原始分-67.1370 → 归一化1.0000, 权重0.2066, 加权分0.2066 [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 RBF: 原始分-64.0476 → 归一化0.5000, 权重0.1962, 加权分0.0981 [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-74.8786 → 归一化0.9416, 权重0.5972, 加权分0.5623 [0m
[37m INFO:2025-09-04 16:12:42,252 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:43,452 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:43,452 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 GP: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 RBF: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 TABPFN: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.8907)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01367): [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:326) -    GP: 0.2066 ↑ 0.2142 (MAE:45.3600, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:326) -    RBF: 0.1962 ↑ 0.2023 (MAE:47.0837, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,455 - (ensemble_manager.py:326) -    TABPFN: 0.5972 ↘ 0.5835 (MAE:30.1930, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,455 - (bounce.py:864) - 🚀 Iteration 11: No improvement. Best function value -113.456 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:43,456 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:12:43,457 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 12 [0m
[37m INFO:2025-09-04 16:12:43,458 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,491 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 12, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:43,492 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,493 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,588 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,589 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 20) [0m
[37m INFO:2025-09-04 16:12:43,589 - (genetic_algorithm.py:186) - 动态种群大小: 110 (目标维度: 20) [0m
[37m INFO:2025-09-04 16:12:43,600 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 110 [0m
[37m WARNING:2025-09-04 16:12:43,770 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:43,772 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:43,774 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m WARNING:2025-09-04 16:12:43,775 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m INFO:2025-09-04 16:12:48,178 - (genetic_algorithm.py:367) - 集成代理模型评估了110个GA个体，使用权重: {'gp': 0.21417205849408769, 'rbf': 0.20228727868650243, 'tabpfn': 0.58354066281941} [0m
[37m INFO:2025-09-04 16:12:48,183 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:48,183 - (ensemble_manager.py:718) - 📊 输入训练数据: 12个样本 (上次: 12) [0m
[37m WARNING:2025-09-04 16:12:48,190 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:48,191 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:48,192 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m WARNING:2025-09-04 16:12:48,193 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m INFO:2025-09-04 16:12:51,090 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:51,091 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21417205849408769, 'rbf': 0.20228727868650243, 'tabpfn': 0.58354066281941} [0m
[37m INFO:2025-09-04 16:12:51,091 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-71.1802, -71.1802] → 归一化[0.0000, 1.0000], 均值-71.1802→0.9207 [0m
[37m INFO:2025-09-04 16:12:51,092 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-68.0117, -68.0117] → 归一化[0.5000, 0.5000], 均值-68.0117→0.5000 [0m
[37m INFO:2025-09-04 16:12:51,092 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-90.9945, -53.7860] → 归一化[0.0000, 1.0000], 均值-75.4612→0.4175 [0m
[37m INFO:2025-09-04 16:12:51,094 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第38个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:51,094 - (ensemble_manager.py:848) -    📊 GP: 原始分-71.1802 → 归一化0.9383, 权重0.2142, 加权分0.2010 [0m
[37m INFO:2025-09-04 16:12:51,095 - (ensemble_manager.py:848) -    📊 RBF: 原始分-68.0117 → 归一化0.5000, 权重0.2023, 加权分0.1011 [0m
[37m INFO:2025-09-04 16:12:51,095 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-64.3663 → 归一化0.7156, 权重0.5835, 加权分0.4176 [0m
[37m INFO:2025-09-04 16:12:51,096 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:128) -    🟡 GP: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:128) -    🟡 RBF: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:128) -    🟡 TABPFN: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.5649)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00626): [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    GP: 0.2142 → 0.2116 (MAE:45.4971, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    RBF: 0.2023 → 0.1986 (MAE:47.4014, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    TABPFN: 0.5835 ↑ 0.5898 (MAE:29.8365, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,627 - (bounce.py:857) - ✨ Iteration 12: [92mNew incumbent function value -117.637[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:52,628 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 13 [0m
[37m INFO:2025-09-04 16:12:52,628 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,668 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 13, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:52,669 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,670 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,767 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 13 [0m
[37m WARNING:2025-09-04 16:12:52,892 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-75.044885 [0m
[37m WARNING:2025-09-04 16:12:52,896 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-75.044885 [0m
[37m WARNING:2025-09-04 16:12:52,897 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m WARNING:2025-09-04 16:12:52,898 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m INFO:2025-09-04 16:12:56,719 - (genetic_algorithm.py:367) - 集成代理模型评估了71个GA个体，使用权重: {'gp': 0.21164357490310365, 'rbf': 0.1985572815708856, 'tabpfn': 0.5897991435260107} [0m
[37m INFO:2025-09-04 16:12:56,724 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:56,724 - (ensemble_manager.py:718) - 📊 输入训练数据: 13个样本 (上次: 13) [0m
[37m WARNING:2025-09-04 16:12:56,729 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-75.044895 [0m
[37m WARNING:2025-09-04 16:12:56,730 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-75.044895 [0m
[37m WARNING:2025-09-04 16:12:56,731 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m WARNING:2025-09-04 16:12:56,732 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m INFO:2025-09-04 16:12:59,600 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21164357490310365, 'rbf': 0.1985572815708856, 'tabpfn': 0.5897991435260107} [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-75.0449, -75.0449] → 归一化[0.0000, 1.0000], 均值-75.0449→0.9800 [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-71.8290, -71.8290] → 归一化[0.5000, 0.5000], 均值-71.8290→0.5000 [0m
[37m INFO:2025-09-04 16:12:59,602 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-94.1907, -62.8305] → 归一化[0.0000, 1.0000], 均值-82.3882→0.3764 [0m
[37m INFO:2025-09-04 16:12:59,603 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第36个候选点 tensor([-1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 GP: 原始分-75.0449 → 归一化1.0000, 权重0.2116, 加权分0.2116 [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 RBF: 原始分-71.8290 → 归一化0.5000, 权重0.1986, 加权分0.0993 [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-62.8305 → 归一化1.0000, 权重0.5898, 加权分0.5898 [0m
[37m INFO:2025-09-04 16:12:59,605 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:01,127 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 GP: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 RBF: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 TABPFN: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.9649)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00486): [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:326) -    GP: 0.2116 → 0.2147 (MAE:44.6076, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (ensemble_manager.py:326) -    RBF: 0.1986 → 0.2004 (MAE:46.6576, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (ensemble_manager.py:326) -    TABPFN: 0.5898 → 0.5849 (MAE:29.6927, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (bounce.py:864) - 🚀 Iteration 13: No improvement. Best function value -117.637 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:01,131 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 14 [0m
[37m INFO:2025-09-04 16:13:01,131 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,173 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 14, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:01,174 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,175 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,270 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 14 [0m
[37m WARNING:2025-09-04 16:13:01,386 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:01,390 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:01,392 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m WARNING:2025-09-04 16:13:01,393 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m INFO:2025-09-04 16:13:05,329 - (genetic_algorithm.py:367) - 集成代理模型评估了76个GA个体，使用权重: {'gp': 0.2146915483388602, 'rbf': 0.20036872180530474, 'tabpfn': 0.5849397298558351} [0m
[37m INFO:2025-09-04 16:13:05,333 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:05,333 - (ensemble_manager.py:718) - 📊 输入训练数据: 14个样本 (上次: 14) [0m
[37m WARNING:2025-09-04 16:13:05,338 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:05,340 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:05,341 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m WARNING:2025-09-04 16:13:05,342 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m INFO:2025-09-04 16:13:08,346 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:08,346 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2146915483388602, 'rbf': 0.20036872180530474, 'tabpfn': 0.5849397298558351} [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-77.9261, -77.9261] → 归一化[0.0000, 1.0000], 均值-77.9261→0.9799 [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-74.7366, -74.7366] → 归一化[0.5000, 0.5000], 均值-74.7366→0.5000 [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-98.6572, -59.8825] → 归一化[0.0000, 1.0000], 均值-86.4066→0.3159 [0m
[37m INFO:2025-09-04 16:13:08,348 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第37个候选点 tensor([ 1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 GP: 原始分-77.9261 → 归一化1.0000, 权重0.2147, 加权分0.2147 [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 RBF: 原始分-74.7366 → 归一化0.5000, 权重0.2004, 加权分0.1002 [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-71.1047 → 归一化0.7106, 权重0.5849, 加权分0.4156 [0m
[37m INFO:2025-09-04 16:13:08,350 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:09,860 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:128) -    🟡 GP: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:128) -    🟡 RBF: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:128) -    🟡 TABPFN: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.5882)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01208): [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:326) -    GP: 0.2147 ↓ 0.2092 (MAE:43.5465, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (ensemble_manager.py:326) -    RBF: 0.2004 ↓ 0.1937 (MAE:45.7105, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (ensemble_manager.py:326) -    TABPFN: 0.5849 ↗ 0.5970 (MAE:28.1223, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (bounce.py:864) - 🚀 Iteration 14: No improvement. Best function value -117.637 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:09,864 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 15 [0m
[37m INFO:2025-09-04 16:13:09,864 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 15 [0m
[37m INFO:2025-09-04 16:13:09,910 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 15, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:09,911 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 15 [0m
[37m INFO:2025-09-04 16:13:09,912 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 15 [0m
[37m INFO:2025-09-04 16:13:10,009 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 15 [0m
[37m WARNING:2025-09-04 16:13:10,113 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-80.352085 [0m
[37m WARNING:2025-09-04 16:13:10,115 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-80.352085 [0m
[37m WARNING:2025-09-04 16:13:10,116 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m WARNING:2025-09-04 16:13:10,117 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m INFO:2025-09-04 16:13:13,427 - (genetic_algorithm.py:367) - 集成代理模型评估了69个GA个体，使用权重: {'gp': 0.20923969099889522, 'rbf': 0.19374254816163344, 'tabpfn': 0.5970177608394713} [0m
[37m INFO:2025-09-04 16:13:13,435 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:13,435 - (ensemble_manager.py:718) - 📊 输入训练数据: 15个样本 (上次: 15) [0m
[37m WARNING:2025-09-04 16:13:13,439 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-80.352069 [0m
[37m WARNING:2025-09-04 16:13:13,441 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-80.352069 [0m
[37m WARNING:2025-09-04 16:13:13,443 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m WARNING:2025-09-04 16:13:13,444 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m INFO:2025-09-04 16:13:16,202 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:16,203 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.20923969099889522, 'rbf': 0.19374254816163344, 'tabpfn': 0.5970177608394713} [0m
[37m INFO:2025-09-04 16:13:16,203 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-80.3521, -80.3521] → 归一化[0.0000, 1.0000], 均值-80.3521→0.9795 [0m
[37m INFO:2025-09-04 16:13:16,204 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-77.2157, -77.2157] → 归一化[0.5000, 0.5000], 均值-77.2157→0.5000 [0m
[37m INFO:2025-09-04 16:13:16,204 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-100.6431, -76.5565] → 归一化[0.0000, 1.0000], 均值-91.5231→0.3786 [0m
[37m INFO:2025-09-04 16:13:16,207 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第37个候选点 tensor([-1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:16,207 - (ensemble_manager.py:848) -    📊 GP: 原始分-80.3521 → 归一化1.0000, 权重0.2092, 加权分0.2092 [0m
[37m INFO:2025-09-04 16:13:16,208 - (ensemble_manager.py:848) -    📊 RBF: 原始分-77.2157 → 归一化0.5000, 权重0.1937, 加权分0.0969 [0m
[37m INFO:2025-09-04 16:13:16,208 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-81.5026 → 归一化0.7947, 权重0.5970, 加权分0.4744 [0m
[37m INFO:2025-09-04 16:13:16,208 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:17,729 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 GP: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 RBF: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 TABPFN: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.1934)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00404): [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:326) -    GP: 0.2092 → 0.2116 (MAE:43.1166, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:326) -    RBF: 0.1937 → 0.1954 (MAE:45.3690, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,732 - (ensemble_manager.py:326) -    TABPFN: 0.5970 → 0.5930 (MAE:28.1757, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,732 - (bounce.py:857) - ✨ Iteration 15: [92mNew incumbent function value -119.170[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:13:17,732 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 16 [0m
[37m INFO:2025-09-04 16:13:17,733 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,775 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 16, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:17,776 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,778 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,873 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 16 [0m
[37m WARNING:2025-09-04 16:13:17,979 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-82.940878 [0m
[37m WARNING:2025-09-04 16:13:17,987 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-82.940878 [0m
[37m WARNING:2025-09-04 16:13:17,989 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m WARNING:2025-09-04 16:13:17,990 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m INFO:2025-09-04 16:13:21,143 - (genetic_algorithm.py:367) - 集成代理模型评估了73个GA个体，使用权重: {'gp': 0.21163946435028272, 'rbf': 0.195379222771705, 'tabpfn': 0.5929813128780123} [0m
[37m INFO:2025-09-04 16:13:21,151 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:21,151 - (ensemble_manager.py:718) - 📊 输入训练数据: 16个样本 (上次: 16) [0m
[37m WARNING:2025-09-04 16:13:21,156 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-82.940866 [0m
[37m WARNING:2025-09-04 16:13:21,157 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-82.940866 [0m
[37m WARNING:2025-09-04 16:13:21,159 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m WARNING:2025-09-04 16:13:21,160 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m INFO:2025-09-04 16:13:23,739 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21163946435028272, 'rbf': 0.195379222771705, 'tabpfn': 0.5929813128780123} [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-82.9409, -82.9409] → 归一化[0.0000, 1.0000], 均值-82.9409→0.9740 [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-79.8379, -79.8379] → 归一化[0.5000, 0.5000], 均值-79.8379→0.5000 [0m
[37m INFO:2025-09-04 16:13:23,741 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-104.3765, -89.1961] → 归一化[0.0000, 1.0000], 均值-95.7077→0.5711 [0m
[37m INFO:2025-09-04 16:13:23,742 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第43个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 GP: 原始分-82.9409 → 归一化1.0000, 权重0.2116, 加权分0.2116 [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 RBF: 原始分-79.8379 → 归一化0.5000, 权重0.1954, 加权分0.0977 [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-90.2266 → 归一化0.9321, 权重0.5930, 加权分0.5527 [0m
[37m INFO:2025-09-04 16:13:23,743 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:25,159 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 GP: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 RBF: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 TABPFN: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.7472)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01252): [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    GP: 0.2116 ↓ 0.2058 (MAE:42.0665, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    RBF: 0.1954 ↓ 0.1887 (MAE:44.3898, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    TABPFN: 0.5930 ↗ 0.6055 (MAE:26.6426, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,162 - (bounce.py:864) - 🚀 Iteration 16: No improvement. Best function value -119.170 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:25,162 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 17 [0m
[37m INFO:2025-09-04 16:13:25,163 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,194 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 17, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:25,195 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,196 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,286 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 17 [0m
[37m WARNING:2025-09-04 16:13:25,397 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m WARNING:2025-09-04 16:13:25,399 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m INFO:2025-09-04 16:13:28,478 - (genetic_algorithm.py:367) - 集成代理模型评估了72个GA个体，使用权重: {'gp': 0.205846566601404, 'rbf': 0.1886566828199899, 'tabpfn': 0.6054967505786062} [0m
[37m INFO:2025-09-04 16:13:28,483 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:28,483 - (ensemble_manager.py:718) - 📊 输入训练数据: 17个样本 (上次: 17) [0m
[37m WARNING:2025-09-04 16:13:28,488 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-84.842715 [0m
[37m WARNING:2025-09-04 16:13:28,489 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-84.842715 [0m
[37m WARNING:2025-09-04 16:13:28,490 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m WARNING:2025-09-04 16:13:28,491 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m INFO:2025-09-04 16:13:31,180 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:31,180 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.205846566601404, 'rbf': 0.1886566828199899, 'tabpfn': 0.6054967505786062} [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-84.8427, -84.8427] → 归一化[0.0000, 1.0000], 均值-84.8427→0.9547 [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-81.8154, -81.8154] → 归一化[0.5000, 0.5000], 均值-81.8154→0.5000 [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-111.2996, -81.9093] → 归一化[0.0000, 1.0000], 均值-97.5443→0.4680 [0m
[37m INFO:2025-09-04 16:13:31,183 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第40个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:31,183 - (ensemble_manager.py:848) -    📊 GP: 原始分-84.8427 → 归一化1.0000, 权重0.2058, 加权分0.2058 [0m
[37m INFO:2025-09-04 16:13:31,184 - (ensemble_manager.py:848) -    📊 RBF: 原始分-81.8154 → 归一化0.5000, 权重0.1887, 加权分0.0943 [0m
[37m INFO:2025-09-04 16:13:31,184 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-81.9093 → 归一化1.0000, 权重0.6055, 加权分0.6055 [0m
[37m INFO:2025-09-04 16:13:31,184 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:32,625 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:32,626 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:32,626 - (ensemble_manager.py:128) -    🟡 GP: 13/50 (26.0%) [0m
[37m INFO:2025-09-04 16:13:32,626 - (ensemble_manager.py:128) -    🟡 RBF: 13/50 (26.0%) [0m
[37m INFO:2025-09-04 16:13:32,627 - (ensemble_manager.py:128) -    🟡 TABPFN: 13/50 (26.0%) [0m
[37m INFO:2025-09-04 16:13:32,627 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.7891)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:32,627 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00151): [0m
[37m INFO:2025-09-04 16:13:32,627 - (ensemble_manager.py:326) -    GP: 0.2058 → 0.2073 (MAE:39.7216, 数据:13) [0m
[37m INFO:2025-09-04 16:13:32,628 - (ensemble_manager.py:326) -    RBF: 0.1887 → 0.1887 (MAE:42.0991, 数据:13) [0m
[37m INFO:2025-09-04 16:13:32,628 - (ensemble_manager.py:326) -    TABPFN: 0.6055 → 0.6040 (MAE:25.3100, 数据:13) [0m
[37m INFO:2025-09-04 16:13:32,628 - (bounce.py:864) - 🚀 Iteration 17: No improvement. Best function value -119.170 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:32,629 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:13:32,630 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 18 [0m
[37m INFO:2025-09-04 16:13:32,631 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 18 [0m
[37m INFO:2025-09-04 16:13:32,672 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 18, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:32,673 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 18 [0m
[37m INFO:2025-09-04 16:13:32,675 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 18 [0m
[37m INFO:2025-09-04 16:13:32,782 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 18 [0m
[37m INFO:2025-09-04 16:13:32,783 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 40) [0m
[37m INFO:2025-09-04 16:13:32,783 - (genetic_algorithm.py:186) - 动态种群大小: 170 (目标维度: 40) [0m
[37m INFO:2025-09-04 16:13:32,817 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 170 [0m
[37m WARNING:2025-09-04 16:13:33,175 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-85.525989 [0m
[37m WARNING:2025-09-04 16:13:33,182 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-85.525989 [0m
[37m WARNING:2025-09-04 16:13:33,184 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-82.627113 [0m
[37m WARNING:2025-09-04 16:13:33,185 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-82.627113 [0m
[37m INFO:2025-09-04 16:13:41,918 - (genetic_algorithm.py:367) - 集成代理模型评估了170个GA个体，使用权重: {'gp': 0.207297494678928, 'rbf': 0.18871196158439854, 'tabpfn': 0.6039905437366735} [0m
[37m INFO:2025-09-04 16:13:41,928 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:41,928 - (ensemble_manager.py:718) - 📊 输入训练数据: 18个样本 (上次: 18) [0m
[37m WARNING:2025-09-04 16:13:41,932 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-85.525989 [0m
[37m WARNING:2025-09-04 16:13:41,936 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-85.525989 [0m
[37m WARNING:2025-09-04 16:13:41,937 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-82.627113 [0m
[37m WARNING:2025-09-04 16:13:41,938 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-82.627113 [0m
[37m INFO:2025-09-04 16:13:44,711 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:44,712 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.207297494678928, 'rbf': 0.18871196158439854, 'tabpfn': 0.6039905437366735} [0m
[37m INFO:2025-09-04 16:13:44,712 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-85.5260, -85.5260] → 归一化[0.0000, 1.0000], 均值-85.5260→0.9800 [0m
[37m INFO:2025-09-04 16:13:44,712 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-82.6271, -82.6271] → 归一化[0.5000, 0.5000], 均值-82.6271→0.5000 [0m
[37m INFO:2025-09-04 16:13:44,713 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-111.0702, -81.4912] → 归一化[0.0000, 1.0000], 均值-90.6318→0.6910 [0m
[37m INFO:2025-09-04 16:13:44,715 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第48个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:44,715 - (ensemble_manager.py:848) -    📊 GP: 原始分-85.5260 → 归一化1.0000, 权重0.2073, 加权分0.2073 [0m
[37m INFO:2025-09-04 16:13:44,715 - (ensemble_manager.py:848) -    📊 RBF: 原始分-82.6271 → 归一化0.5000, 权重0.1887, 加权分0.0944 [0m
[37m INFO:2025-09-04 16:13:44,716 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-83.6230 → 归一化0.9279, 权重0.6040, 加权分0.5605 [0m
[37m INFO:2025-09-04 16:13:44,716 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:46,438 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:46,439 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:46,439 - (ensemble_manager.py:128) -    🟡 GP: 14/50 (28.0%) [0m
[37m INFO:2025-09-04 16:13:46,439 - (ensemble_manager.py:128) -    🟡 RBF: 14/50 (28.0%) [0m
[37m INFO:2025-09-04 16:13:46,440 - (ensemble_manager.py:128) -    🟡 TABPFN: 14/50 (28.0%) [0m
[37m INFO:2025-09-04 16:13:46,440 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.7210)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:46,440 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00498): [0m
[37m INFO:2025-09-04 16:13:46,440 - (ensemble_manager.py:326) -    GP: 0.2073 → 0.2052 (MAE:38.6465, 数据:14) [0m
[37m INFO:2025-09-04 16:13:46,441 - (ensemble_manager.py:326) -    RBF: 0.1887 → 0.1858 (MAE:41.0612, 数据:14) [0m
[37m INFO:2025-09-04 16:13:46,441 - (ensemble_manager.py:326) -    TABPFN: 0.6040 → 0.6090 (MAE:24.3401, 数据:14) [0m
[37m INFO:2025-09-04 16:13:46,441 - (bounce.py:864) - 🚀 Iteration 18: No improvement. Best function value -119.170 [全局模型策略] [0m
[37m WARNING:2025-09-04 16:13:46,442 - (bounce.py:876) - 🔄 策略切换: 全局模型策略 -> 历史最优策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:13:46,442 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 19 [0m
[37m INFO:2025-09-04 16:13:46,442 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 19 [0m
[37m INFO:2025-09-04 16:13:46,486 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 19, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:46,486 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 19 [0m
[37m INFO:2025-09-04 16:13:46,487 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 19 [0m
[37m INFO:2025-09-04 16:13:46,582 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 19 [0m
[37m WARNING:2025-09-04 16:13:46,815 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-86.896935 [0m
[37m WARNING:2025-09-04 16:13:46,823 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-86.896935 [0m
[37m WARNING:2025-09-04 16:13:46,826 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-84.078067 [0m
[37m WARNING:2025-09-04 16:13:46,827 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-84.078067 [0m
[37m INFO:2025-09-04 16:13:51,007 - (genetic_algorithm.py:367) - 集成代理模型评估了115个GA个体，使用权重: {'gp': 0.20520403798063758, 'rbf': 0.1858237237245665, 'tabpfn': 0.608972238294796} [0m
[37m INFO:2025-09-04 16:13:51,017 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-119.170) [0m
[37m INFO:2025-09-04 16:13:52,573 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:52,573 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:52,574 - (ensemble_manager.py:128) -    🟡 GP: 15/50 (30.0%) [0m
[37m INFO:2025-09-04 16:13:52,574 - (ensemble_manager.py:128) -    🟡 RBF: 15/50 (30.0%) [0m
[37m INFO:2025-09-04 16:13:52,574 - (ensemble_manager.py:128) -    🟡 TABPFN: 15/50 (30.0%) [0m
[37m INFO:2025-09-04 16:13:52,574 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.8699)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:52,575 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00275): [0m
[37m INFO:2025-09-04 16:13:52,575 - (ensemble_manager.py:326) -    GP: 0.2052 → 0.2040 (MAE:38.5412, 数据:15) [0m
[37m INFO:2025-09-04 16:13:52,575 - (ensemble_manager.py:326) -    RBF: 0.1858 → 0.1843 (MAE:40.9829, 数据:15) [0m
[37m INFO:2025-09-04 16:13:52,575 - (ensemble_manager.py:326) -    TABPFN: 0.6090 → 0.6117 (MAE:24.1130, 数据:15) [0m
[37m INFO:2025-09-04 16:13:52,575 - (bounce.py:857) - ✨ Iteration 19: [92mNew incumbent function value -123.965[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:13:52,576 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 20 [0m
[37m INFO:2025-09-04 16:13:52,576 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 20 [0m
[37m INFO:2025-09-04 16:13:52,621 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 20, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:52,622 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 20 [0m
[37m INFO:2025-09-04 16:13:52,623 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 20 [0m
[37m INFO:2025-09-04 16:13:52,715 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 20 [0m
[37m WARNING:2025-09-04 16:13:52,944 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-88.845090 [0m
[37m WARNING:2025-09-04 16:13:52,951 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-88.845090 [0m
[37m WARNING:2025-09-04 16:13:52,952 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-86.072389 [0m
[37m WARNING:2025-09-04 16:13:52,953 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-86.072389 [0m
[37m INFO:2025-09-04 16:13:57,247 - (genetic_algorithm.py:367) - 集成代理模型评估了124个GA个体，使用权重: {'gp': 0.20395928114003553, 'rbf': 0.18431793045260536, 'tabpfn': 0.6117227884073592} [0m
[37m INFO:2025-09-04 16:13:57,255 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-123.965) [0m
[37m INFO:2025-09-04 16:13:58,857 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:58,857 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:58,858 - (ensemble_manager.py:128) -    🟡 GP: 16/50 (32.0%) [0m
[37m INFO:2025-09-04 16:13:58,858 - (ensemble_manager.py:128) -    🟡 RBF: 16/50 (32.0%) [0m
[37m INFO:2025-09-04 16:13:58,858 - (ensemble_manager.py:128) -    🟡 TABPFN: 16/50 (32.0%) [0m
[37m INFO:2025-09-04 16:13:58,859 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.9300)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:58,859 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00677): [0m
[37m INFO:2025-09-04 16:13:58,859 - (ensemble_manager.py:326) -    GP: 0.2040 → 0.2009 (MAE:37.6322, 数据:16) [0m
[37m INFO:2025-09-04 16:13:58,860 - (ensemble_manager.py:326) -    RBF: 0.1843 → 0.1806 (MAE:40.0946, 数据:16) [0m
[37m INFO:2025-09-04 16:13:58,860 - (ensemble_manager.py:326) -    TABPFN: 0.6117 ↑ 0.6185 (MAE:23.1646, 数据:16) [0m
[37m INFO:2025-09-04 16:13:58,860 - (bounce.py:864) - 🚀 Iteration 20: No improvement. Best function value -123.965 [历史最优策略] [0m
[37m INFO:2025-09-04 16:13:58,861 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 21 [0m
[37m INFO:2025-09-04 16:13:58,862 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 21 [0m
[37m INFO:2025-09-04 16:13:58,905 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 21, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:58,906 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 21 [0m
[37m INFO:2025-09-04 16:13:58,908 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 21 [0m
[37m INFO:2025-09-04 16:13:59,001 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 21 [0m
[37m WARNING:2025-09-04 16:13:59,218 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-90.036016 [0m
[37m WARNING:2025-09-04 16:13:59,221 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-90.036016 [0m
[37m WARNING:2025-09-04 16:13:59,223 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-87.347170 [0m
[37m WARNING:2025-09-04 16:13:59,225 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-87.347170 [0m
[37m INFO:2025-09-04 16:14:03,400 - (genetic_algorithm.py:367) - 集成代理模型评估了116个GA个体，使用权重: {'gp': 0.2008834809605841, 'rbf': 0.18062589577705562, 'tabpfn': 0.6184906232623604} [0m
[37m INFO:2025-09-04 16:14:03,409 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-123.965) [0m
[37m INFO:2025-09-04 16:14:05,050 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:05,051 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:05,051 - (ensemble_manager.py:128) -    🟡 GP: 17/50 (34.0%) [0m
[37m INFO:2025-09-04 16:14:05,051 - (ensemble_manager.py:128) -    🟡 RBF: 17/50 (34.0%) [0m
[37m INFO:2025-09-04 16:14:05,052 - (ensemble_manager.py:128) -    🟡 TABPFN: 17/50 (34.0%) [0m
[37m INFO:2025-09-04 16:14:05,052 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.1894)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:05,053 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00321): [0m
[37m INFO:2025-09-04 16:14:05,053 - (ensemble_manager.py:326) -    GP: 0.2009 → 0.1993 (MAE:37.7866, 数据:17) [0m
[37m INFO:2025-09-04 16:14:05,053 - (ensemble_manager.py:326) -    RBF: 0.1806 → 0.1790 (MAE:40.2622, 数据:17) [0m
[37m INFO:2025-09-04 16:14:05,053 - (ensemble_manager.py:326) -    TABPFN: 0.6185 → 0.6217 (MAE:23.0728, 数据:17) [0m
[37m INFO:2025-09-04 16:14:05,054 - (bounce.py:857) - ✨ Iteration 21: [92mNew incumbent function value -130.292[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:14:05,054 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 22 [0m
[37m INFO:2025-09-04 16:14:05,055 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 22 [0m
[37m INFO:2025-09-04 16:14:05,102 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 22, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:05,103 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 22 [0m
[37m INFO:2025-09-04 16:14:05,104 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 22 [0m
[37m INFO:2025-09-04 16:14:05,205 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 22 [0m
[37m WARNING:2025-09-04 16:14:05,422 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-91.952457 [0m
[37m WARNING:2025-09-04 16:14:05,423 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-91.952457 [0m
[37m WARNING:2025-09-04 16:14:05,429 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-89.299210 [0m
[37m WARNING:2025-09-04 16:14:05,430 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-89.299210 [0m
[37m INFO:2025-09-04 16:14:09,743 - (genetic_algorithm.py:367) - 集成代理模型评估了108个GA个体，使用权重: {'gp': 0.19928732501462393, 'rbf': 0.17901150027424145, 'tabpfn': 0.6217011747111346} [0m
[37m INFO:2025-09-04 16:14:09,754 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-130.292) [0m
[37m INFO:2025-09-04 16:14:11,380 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:11,381 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:11,381 - (ensemble_manager.py:128) -    🟡 GP: 18/50 (36.0%) [0m
[37m INFO:2025-09-04 16:14:11,382 - (ensemble_manager.py:128) -    🟡 RBF: 18/50 (36.0%) [0m
[37m INFO:2025-09-04 16:14:11,382 - (ensemble_manager.py:128) -    🟡 TABPFN: 18/50 (36.0%) [0m
[37m INFO:2025-09-04 16:14:11,382 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.3096)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:11,383 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00161): [0m
[37m INFO:2025-09-04 16:14:11,383 - (ensemble_manager.py:326) -    GP: 0.1993 → 0.1985 (MAE:37.8343, 数据:18) [0m
[37m INFO:2025-09-04 16:14:11,383 - (ensemble_manager.py:326) -    RBF: 0.1790 → 0.1782 (MAE:40.3199, 数据:18) [0m
[37m INFO:2025-09-04 16:14:11,383 - (ensemble_manager.py:326) -    TABPFN: 0.6217 → 0.6233 (MAE:23.0103, 数据:18) [0m
[37m INFO:2025-09-04 16:14:11,384 - (bounce.py:857) - ✨ Iteration 22: [92mNew incumbent function value -130.599[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:14:11,384 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 23 [0m
[37m INFO:2025-09-04 16:14:11,385 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 23 [0m
[37m INFO:2025-09-04 16:14:11,431 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 23, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:11,432 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 23 [0m
[37m INFO:2025-09-04 16:14:11,434 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 23 [0m
[37m INFO:2025-09-04 16:14:11,529 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 23 [0m
[37m WARNING:2025-09-04 16:14:11,727 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-93.699626 [0m
[37m WARNING:2025-09-04 16:14:11,730 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-93.699626 [0m
[37m WARNING:2025-09-04 16:14:11,732 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-91.094841 [0m
[37m WARNING:2025-09-04 16:14:11,733 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-91.094841 [0m
[37m INFO:2025-09-04 16:14:15,889 - (genetic_algorithm.py:367) - 集成代理模型评估了108个GA个体，使用权重: {'gp': 0.1985049064359377, 'rbf': 0.17818028829474838, 'tabpfn': 0.623314805269314} [0m
[37m INFO:2025-09-04 16:14:15,898 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-130.599) [0m
[37m INFO:2025-09-04 16:14:17,586 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:17,587 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:17,587 - (ensemble_manager.py:128) -    🟡 GP: 19/50 (38.0%) [0m
[37m INFO:2025-09-04 16:14:17,587 - (ensemble_manager.py:128) -    🟡 RBF: 19/50 (38.0%) [0m
[37m INFO:2025-09-04 16:14:17,588 - (ensemble_manager.py:128) -    🟡 TABPFN: 19/50 (38.0%) [0m
[37m INFO:2025-09-04 16:14:17,588 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.7195)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:17,588 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00513): [0m
[37m INFO:2025-09-04 16:14:17,588 - (ensemble_manager.py:326) -    GP: 0.1985 → 0.1959 (MAE:38.1020, 数据:19) [0m
[37m INFO:2025-09-04 16:14:17,589 - (ensemble_manager.py:326) -    RBF: 0.1782 → 0.1757 (MAE:40.5938, 数据:19) [0m
[37m INFO:2025-09-04 16:14:17,589 - (ensemble_manager.py:326) -    TABPFN: 0.6233 ↑ 0.6284 (MAE:22.8743, 数据:19) [0m
[37m INFO:2025-09-04 16:14:17,589 - (bounce.py:857) - ✨ Iteration 23: [92mNew incumbent function value -136.620[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:14:17,590 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 24 [0m
[37m INFO:2025-09-04 16:14:17,591 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 24 [0m
[37m INFO:2025-09-04 16:14:17,657 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 24, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:17,658 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 24 [0m
[37m INFO:2025-09-04 16:14:17,659 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 24 [0m
[37m INFO:2025-09-04 16:14:17,751 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 24 [0m
[37m WARNING:2025-09-04 16:14:17,961 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-95.420281 [0m
[37m WARNING:2025-09-04 16:14:17,962 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-95.420281 [0m
[37m WARNING:2025-09-04 16:14:17,966 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-92.991706 [0m
[37m WARNING:2025-09-04 16:14:17,968 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-92.991706 [0m
[37m INFO:2025-09-04 16:14:22,336 - (genetic_algorithm.py:367) - 集成代理模型评估了116个GA个体，使用权重: {'gp': 0.19588665288473833, 'rbf': 0.1756690619998998, 'tabpfn': 0.6284442851153619} [0m
[37m INFO:2025-09-04 16:14:22,342 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-136.620) [0m
[37m INFO:2025-09-04 16:14:24,089 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:24,089 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:24,090 - (ensemble_manager.py:128) -    🟡 GP: 20/50 (40.0%) [0m
[37m INFO:2025-09-04 16:14:24,090 - (ensemble_manager.py:128) -    🟡 RBF: 20/50 (40.0%) [0m
[37m INFO:2025-09-04 16:14:24,090 - (ensemble_manager.py:128) -    🟡 TABPFN: 20/50 (40.0%) [0m
[37m INFO:2025-09-04 16:14:24,091 - (ensemble_manager.py:227) - 🔄 检测到性能差异(18.2701)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:24,091 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01042): [0m
[37m INFO:2025-09-04 16:14:24,091 - (ensemble_manager.py:326) -    GP: 0.1959 ↓ 0.1907 (MAE:38.0325, 数据:20) [0m
[37m INFO:2025-09-04 16:14:24,091 - (ensemble_manager.py:326) -    RBF: 0.1757 ↓ 0.1705 (MAE:40.5211, 数据:20) [0m
[37m INFO:2025-09-04 16:14:24,092 - (ensemble_manager.py:326) -    TABPFN: 0.6284 ↗ 0.6389 (MAE:22.2510, 数据:20) [0m
[37m INFO:2025-09-04 16:14:24,092 - (bounce.py:864) - 🚀 Iteration 24: No improvement. Best function value -136.620 [历史最优策略] [0m
[37m INFO:2025-09-04 16:14:24,093 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 25 [0m
[37m INFO:2025-09-04 16:14:24,093 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 25 [0m
[37m INFO:2025-09-04 16:14:24,150 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 25, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:24,152 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 25 [0m
[37m INFO:2025-09-04 16:14:24,153 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 25 [0m
[37m INFO:2025-09-04 16:14:24,243 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 25 [0m
[37m WARNING:2025-09-04 16:14:24,457 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-96.807669 [0m
[37m WARNING:2025-09-04 16:14:24,459 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-96.807669 [0m
[37m WARNING:2025-09-04 16:14:24,461 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-94.557320 [0m
[37m WARNING:2025-09-04 16:14:24,463 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-94.557320 [0m
[37m INFO:2025-09-04 16:14:28,650 - (genetic_algorithm.py:367) - 集成代理模型评估了112个GA个体，使用权重: {'gp': 0.19065263138298386, 'rbf': 0.1704785462851951, 'tabpfn': 0.6388688223318211} [0m
[37m INFO:2025-09-04 16:14:28,658 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-136.620) [0m
[37m INFO:2025-09-04 16:14:30,446 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:30,446 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:30,447 - (ensemble_manager.py:128) -    🟡 GP: 21/50 (42.0%) [0m
[37m INFO:2025-09-04 16:14:30,447 - (ensemble_manager.py:128) -    🟡 RBF: 21/50 (42.0%) [0m
[37m INFO:2025-09-04 16:14:30,447 - (ensemble_manager.py:128) -    🟡 TABPFN: 21/50 (42.0%) [0m
[37m INFO:2025-09-04 16:14:30,448 - (ensemble_manager.py:227) - 🔄 检测到性能差异(18.9701)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:30,448 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01450): [0m
[37m INFO:2025-09-04 16:14:30,448 - (ensemble_manager.py:326) -    GP: 0.1907 ↓ 0.1833 (MAE:37.8889, 数据:21) [0m
[37m INFO:2025-09-04 16:14:30,449 - (ensemble_manager.py:326) -    RBF: 0.1705 ↓ 0.1633 (MAE:40.3662, 数据:21) [0m
[37m INFO:2025-09-04 16:14:30,449 - (ensemble_manager.py:326) -    TABPFN: 0.6389 ↗ 0.6534 (MAE:21.3961, 数据:21) [0m
[37m INFO:2025-09-04 16:14:30,450 - (bounce.py:864) - 🚀 Iteration 25: No improvement. Best function value -136.620 [历史最优策略] [0m
[37m INFO:2025-09-04 16:14:30,451 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 26 [0m
[37m INFO:2025-09-04 16:14:30,452 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 26 [0m
[37m INFO:2025-09-04 16:14:30,513 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 26, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:30,514 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 26 [0m
[37m INFO:2025-09-04 16:14:30,515 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 26 [0m
[37m INFO:2025-09-04 16:14:30,614 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 26 [0m
[37m WARNING:2025-09-04 16:14:30,822 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-98.073764 [0m
[37m WARNING:2025-09-04 16:14:30,830 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-98.073764 [0m
[37m WARNING:2025-09-04 16:14:30,831 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-95.990707 [0m
[37m WARNING:2025-09-04 16:14:30,832 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-95.990707 [0m
[37m INFO:2025-09-04 16:14:35,195 - (genetic_algorithm.py:367) - 集成代理模型评估了112个GA个体，使用权重: {'gp': 0.1833355356686007, 'rbf': 0.16329133277478658, 'tabpfn': 0.6533731315566127} [0m
[37m INFO:2025-09-04 16:14:35,203 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-136.620) [0m
[37m INFO:2025-09-04 16:14:37,022 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:37,023 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:37,023 - (ensemble_manager.py:128) -    🟡 GP: 22/50 (44.0%) [0m
[37m INFO:2025-09-04 16:14:37,023 - (ensemble_manager.py:128) -    🟡 RBF: 22/50 (44.0%) [0m
[37m INFO:2025-09-04 16:14:37,024 - (ensemble_manager.py:128) -    🟡 TABPFN: 22/50 (44.0%) [0m
[37m INFO:2025-09-04 16:14:37,024 - (ensemble_manager.py:227) - 🔄 检测到性能差异(19.7088)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:37,024 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01269): [0m
[37m INFO:2025-09-04 16:14:37,024 - (ensemble_manager.py:326) -    GP: 0.1833 ↓ 0.1768 (MAE:38.1228, 数据:22) [0m
[37m INFO:2025-09-04 16:14:37,025 - (ensemble_manager.py:326) -    RBF: 0.1633 ↓ 0.1571 (MAE:40.5821, 数据:22) [0m
[37m INFO:2025-09-04 16:14:37,025 - (ensemble_manager.py:326) -    TABPFN: 0.6534 ↗ 0.6661 (MAE:20.8733, 数据:22) [0m
[37m INFO:2025-09-04 16:14:37,025 - (bounce.py:857) - ✨ Iteration 26: [92mNew incumbent function value -141.107[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:14:37,026 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 27 [0m
[37m INFO:2025-09-04 16:14:37,027 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 27 [0m
[37m INFO:2025-09-04 16:14:37,076 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 27, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:37,076 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 27 [0m
[37m INFO:2025-09-04 16:14:37,078 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 27 [0m
[37m INFO:2025-09-04 16:14:37,172 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 27 [0m
[37m WARNING:2025-09-04 16:14:37,375 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-99.566894 [0m
[37m WARNING:2025-09-04 16:14:37,378 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-99.566894 [0m
[37m WARNING:2025-09-04 16:14:37,379 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-97.661686 [0m
[37m WARNING:2025-09-04 16:14:37,381 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-97.661686 [0m
[37m INFO:2025-09-04 16:14:41,697 - (genetic_algorithm.py:367) - 集成代理模型评估了108个GA个体，使用权重: {'gp': 0.17679508118837042, 'rbf': 0.1571447967874671, 'tabpfn': 0.6660601220241625} [0m
[37m INFO:2025-09-04 16:14:41,705 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-141.107) [0m
[37m INFO:2025-09-04 16:14:43,443 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:43,444 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:43,444 - (ensemble_manager.py:128) -    🟡 GP: 23/50 (46.0%) [0m
[37m INFO:2025-09-04 16:14:43,444 - (ensemble_manager.py:128) -    🟡 RBF: 23/50 (46.0%) [0m
[37m INFO:2025-09-04 16:14:43,444 - (ensemble_manager.py:128) -    🟡 TABPFN: 23/50 (46.0%) [0m
[37m INFO:2025-09-04 16:14:43,445 - (ensemble_manager.py:227) - 🔄 检测到性能差异(20.3856)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:43,445 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01316): [0m
[37m INFO:2025-09-04 16:14:43,445 - (ensemble_manager.py:326) -    GP: 0.1768 ↓ 0.1700 (MAE:38.2314, 数据:23) [0m
[37m INFO:2025-09-04 16:14:43,446 - (ensemble_manager.py:326) -    RBF: 0.1571 ↓ 0.1508 (MAE:40.6666, 数据:23) [0m
[37m INFO:2025-09-04 16:14:43,446 - (ensemble_manager.py:326) -    TABPFN: 0.6661 ↗ 0.6792 (MAE:20.2810, 数据:23) [0m
[37m INFO:2025-09-04 16:14:43,446 - (bounce.py:864) - 🚀 Iteration 27: No improvement. Best function value -141.107 [历史最优策略] [0m
[37m INFO:2025-09-04 16:14:43,447 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 28 [0m
[37m INFO:2025-09-04 16:14:43,448 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 28 [0m
[37m INFO:2025-09-04 16:14:43,508 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 28, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:43,509 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 28 [0m
[37m INFO:2025-09-04 16:14:43,510 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 28 [0m
[37m INFO:2025-09-04 16:14:43,601 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 28 [0m
[37m WARNING:2025-09-04 16:14:43,825 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-101.111226 [0m
[37m WARNING:2025-09-04 16:14:43,827 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-101.111226 [0m
[37m WARNING:2025-09-04 16:14:43,829 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-99.180452 [0m
[37m WARNING:2025-09-04 16:14:43,831 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-99.180452 [0m
[37m INFO:2025-09-04 16:14:48,278 - (genetic_algorithm.py:367) - 集成代理模型评估了111个GA个体，使用权重: {'gp': 0.17001045752198474, 'rbf': 0.1507742226080242, 'tabpfn': 0.6792153198699911} [0m
[37m INFO:2025-09-04 16:14:48,286 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-141.107) [0m
[37m INFO:2025-09-04 16:14:50,053 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:50,053 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:50,054 - (ensemble_manager.py:128) -    🟡 GP: 24/50 (48.0%) [0m
[37m INFO:2025-09-04 16:14:50,054 - (ensemble_manager.py:128) -    🟡 RBF: 24/50 (48.0%) [0m
[37m INFO:2025-09-04 16:14:50,054 - (ensemble_manager.py:128) -    🟡 TABPFN: 24/50 (48.0%) [0m
[37m INFO:2025-09-04 16:14:50,055 - (ensemble_manager.py:227) - 🔄 检测到性能差异(21.0591)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:50,055 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01202): [0m
[37m INFO:2025-09-04 16:14:50,055 - (ensemble_manager.py:326) -    GP: 0.1700 ↓ 0.1638 (MAE:38.4791, 数据:24) [0m
[37m INFO:2025-09-04 16:14:50,055 - (ensemble_manager.py:326) -    RBF: 0.1508 ↓ 0.1450 (MAE:40.8933, 数据:24) [0m
[37m INFO:2025-09-04 16:14:50,056 - (ensemble_manager.py:326) -    TABPFN: 0.6792 ↗ 0.6912 (MAE:19.8342, 数据:24) [0m
[37m INFO:2025-09-04 16:14:50,056 - (bounce.py:857) - ✨ Iteration 28: [92mNew incumbent function value -145.288[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:14:50,057 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 29 [0m
[37m INFO:2025-09-04 16:14:50,057 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 29 [0m
[37m INFO:2025-09-04 16:14:50,120 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 29, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:50,121 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 29 [0m
[37m INFO:2025-09-04 16:14:50,123 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 29 [0m
[37m INFO:2025-09-04 16:14:50,219 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 29 [0m
[37m WARNING:2025-09-04 16:14:50,424 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-102.562360 [0m
[37m WARNING:2025-09-04 16:14:50,430 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-102.562360 [0m
[37m WARNING:2025-09-04 16:14:50,432 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-100.770368 [0m
[37m WARNING:2025-09-04 16:14:50,434 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-100.770368 [0m
[37m INFO:2025-09-04 16:14:54,661 - (genetic_algorithm.py:367) - 集成代理模型评估了114个GA个体，使用权重: {'gp': 0.1637671060295459, 'rbf': 0.14499854877496998, 'tabpfn': 0.6912343451954841} [0m
[37m INFO:2025-09-04 16:14:54,668 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-145.288) [0m
[37m INFO:2025-09-04 16:14:56,480 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:14:56,480 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:14:56,481 - (ensemble_manager.py:128) -    🟡 GP: 25/50 (50.0%) [0m
[37m INFO:2025-09-04 16:14:56,481 - (ensemble_manager.py:128) -    🟡 RBF: 25/50 (50.0%) [0m
[37m INFO:2025-09-04 16:14:56,481 - (ensemble_manager.py:128) -    🟡 TABPFN: 25/50 (50.0%) [0m
[37m INFO:2025-09-04 16:14:56,482 - (ensemble_manager.py:227) - 🔄 检测到性能差异(21.7112)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:14:56,482 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01541): [0m
[37m INFO:2025-09-04 16:14:56,482 - (ensemble_manager.py:326) -    GP: 0.1638 ↓ 0.1558 (MAE:38.4204, 数据:25) [0m
[37m INFO:2025-09-04 16:14:56,483 - (ensemble_manager.py:326) -    RBF: 0.1450 ↓ 0.1375 (MAE:40.8097, 数据:25) [0m
[37m INFO:2025-09-04 16:14:56,483 - (ensemble_manager.py:326) -    TABPFN: 0.6912 ↗ 0.7066 (MAE:19.0985, 数据:25) [0m
[37m INFO:2025-09-04 16:14:56,483 - (bounce.py:864) - 🚀 Iteration 29: No improvement. Best function value -145.288 [历史最优策略] [0m
[37m INFO:2025-09-04 16:14:56,484 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:14:56,488 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 30 [0m
[37m INFO:2025-09-04 16:14:56,489 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 30 [0m
[37m INFO:2025-09-04 16:14:56,567 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 30, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:14:56,568 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 30 [0m
[37m INFO:2025-09-04 16:14:56,569 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 30 [0m
[37m INFO:2025-09-04 16:14:56,661 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 30 [0m
[37m INFO:2025-09-04 16:14:56,662 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 60) [0m
[37m INFO:2025-09-04 16:14:56,662 - (genetic_algorithm.py:186) - 动态种群大小: 230 (目标维度: 60) [0m
[37m INFO:2025-09-04 16:14:56,721 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 230 [0m
[37m WARNING:2025-09-04 16:14:57,204 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-103.733085 [0m
[37m WARNING:2025-09-04 16:14:57,208 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-103.733085 [0m
[37m WARNING:2025-09-04 16:14:57,210 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-102.063816 [0m
[37m WARNING:2025-09-04 16:14:57,212 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-102.063816 [0m
[37m INFO:2025-09-04 16:15:04,246 - (genetic_algorithm.py:367) - 集成代理模型评估了230个GA个体，使用权重: {'gp': 0.15584051817294042, 'rbf': 0.13751400225632918, 'tabpfn': 0.7066454795707304} [0m
[37m INFO:2025-09-04 16:15:04,258 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-145.288) [0m
[37m INFO:2025-09-04 16:15:06,191 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:06,192 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:06,192 - (ensemble_manager.py:128) -    🟡 GP: 26/50 (52.0%) [0m
[37m INFO:2025-09-04 16:15:06,192 - (ensemble_manager.py:128) -    🟡 RBF: 26/50 (52.0%) [0m
[37m INFO:2025-09-04 16:15:06,192 - (ensemble_manager.py:128) -    🟡 TABPFN: 26/50 (52.0%) [0m
[37m INFO:2025-09-04 16:15:06,193 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.1299)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:06,193 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01124): [0m
[37m INFO:2025-09-04 16:15:06,193 - (ensemble_manager.py:326) -    GP: 0.1558 ↓ 0.1500 (MAE:38.3212, 数据:26) [0m
[37m INFO:2025-09-04 16:15:06,194 - (ensemble_manager.py:326) -    RBF: 0.1375 ↓ 0.1321 (MAE:40.6828, 数据:26) [0m
[37m INFO:2025-09-04 16:15:06,194 - (ensemble_manager.py:326) -    TABPFN: 0.7066 ↗ 0.7179 (MAE:18.5529, 数据:26) [0m
[37m INFO:2025-09-04 16:15:06,194 - (bounce.py:864) - 🚀 Iteration 30: No improvement. Best function value -145.288 [历史最优策略] [0m
[37m INFO:2025-09-04 16:15:06,195 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 31 [0m
[37m INFO:2025-09-04 16:15:06,196 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 31 [0m
[37m INFO:2025-09-04 16:15:06,351 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 31, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:06,352 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 31 [0m
[37m INFO:2025-09-04 16:15:06,353 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 31 [0m
[37m INFO:2025-09-04 16:15:06,460 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 31 [0m
[37m WARNING:2025-09-04 16:15:06,816 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-104.975422 [0m
[37m WARNING:2025-09-04 16:15:06,818 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-104.975422 [0m
[37m WARNING:2025-09-04 16:15:06,821 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-103.273816 [0m
[37m WARNING:2025-09-04 16:15:06,822 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-103.273816 [0m
[37m INFO:2025-09-04 16:15:12,024 - (genetic_algorithm.py:367) - 集成代理模型评估了156个GA个体，使用权重: {'gp': 0.15002455439018375, 'rbf': 0.13209287509219708, 'tabpfn': 0.7178825705176192} [0m
[37m INFO:2025-09-04 16:15:12,034 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... (fx=-145.288) [0m
[37m INFO:2025-09-04 16:15:13,943 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:13,944 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:13,944 - (ensemble_manager.py:128) -    🟡 GP: 27/50 (54.0%) [0m
[37m INFO:2025-09-04 16:15:13,944 - (ensemble_manager.py:128) -    🟡 RBF: 27/50 (54.0%) [0m
[37m INFO:2025-09-04 16:15:13,944 - (ensemble_manager.py:128) -    🟡 TABPFN: 27/50 (54.0%) [0m
[37m INFO:2025-09-04 16:15:13,945 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.5854)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:13,945 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00766): [0m
[37m INFO:2025-09-04 16:15:13,945 - (ensemble_manager.py:326) -    GP: 0.1500 → 0.1460 (MAE:38.5952, 数据:27) [0m
[37m INFO:2025-09-04 16:15:13,946 - (ensemble_manager.py:326) -    RBF: 0.1321 → 0.1285 (MAE:40.9324, 数据:27) [0m
[37m INFO:2025-09-04 16:15:13,946 - (ensemble_manager.py:326) -    TABPFN: 0.7179 ↑ 0.7255 (MAE:18.3470, 数据:27) [0m
[37m INFO:2025-09-04 16:15:13,946 - (bounce.py:857) - ✨ Iteration 31: [92mNew incumbent function value -150.696[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:15:13,947 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 32 [0m
[37m INFO:2025-09-04 16:15:13,947 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 32 [0m
[37m INFO:2025-09-04 16:15:14,039 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 32, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:14,040 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 32 [0m
[37m INFO:2025-09-04 16:15:14,041 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 32 [0m
[37m INFO:2025-09-04 16:15:14,133 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 32 [0m
[37m WARNING:2025-09-04 16:15:14,478 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-106.506367 [0m
[37m WARNING:2025-09-04 16:15:14,480 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-106.506367 [0m
[37m WARNING:2025-09-04 16:15:14,482 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-104.755745 [0m
[37m WARNING:2025-09-04 16:15:14,483 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-104.755745 [0m
[37m INFO:2025-09-04 16:15:19,976 - (genetic_algorithm.py:367) - 集成代理模型评估了152个GA个体，使用权重: {'gp': 0.14595576220731604, 'rbf': 0.1284980753823841, 'tabpfn': 0.7255461624102999} [0m
[37m INFO:2025-09-04 16:15:19,985 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-150.696) [0m
[37m INFO:2025-09-04 16:15:22,072 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:22,073 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:22,073 - (ensemble_manager.py:128) -    🟡 GP: 28/50 (56.0%) [0m
[37m INFO:2025-09-04 16:15:22,073 - (ensemble_manager.py:128) -    🟡 RBF: 28/50 (56.0%) [0m
[37m INFO:2025-09-04 16:15:22,074 - (ensemble_manager.py:128) -    🟡 TABPFN: 28/50 (56.0%) [0m
[37m INFO:2025-09-04 16:15:22,074 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.0734)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:22,074 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01392): [0m
[37m INFO:2025-09-04 16:15:22,074 - (ensemble_manager.py:326) -    GP: 0.1460 ↓ 0.1388 (MAE:38.4525, 数据:28) [0m
[37m INFO:2025-09-04 16:15:22,075 - (ensemble_manager.py:326) -    RBF: 0.1285 ↓ 0.1218 (MAE:40.7688, 数据:28) [0m
[37m INFO:2025-09-04 16:15:22,075 - (ensemble_manager.py:326) -    TABPFN: 0.7255 ↗ 0.7395 (MAE:17.6954, 数据:28) [0m
[37m INFO:2025-09-04 16:15:22,075 - (bounce.py:864) - 🚀 Iteration 32: No improvement. Best function value -150.696 [历史最优策略] [0m
[37m INFO:2025-09-04 16:15:22,076 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 33 [0m
[37m INFO:2025-09-04 16:15:22,077 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 33 [0m
[37m INFO:2025-09-04 16:15:22,202 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 33, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:22,203 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 33 [0m
[37m INFO:2025-09-04 16:15:22,205 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 33 [0m
[37m INFO:2025-09-04 16:15:22,300 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 33 [0m
[37m WARNING:2025-09-04 16:15:22,641 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-107.613381 [0m
[37m WARNING:2025-09-04 16:15:22,642 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-107.613381 [0m
[37m WARNING:2025-09-04 16:15:22,645 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-105.857303 [0m
[37m WARNING:2025-09-04 16:15:22,647 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-105.857303 [0m
[37m INFO:2025-09-04 16:15:28,134 - (genetic_algorithm.py:367) - 集成代理模型评估了151个GA个体，使用权重: {'gp': 0.138782087783986, 'rbf': 0.12175471322197967, 'tabpfn': 0.7394631989940343} [0m
[37m INFO:2025-09-04 16:15:28,146 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-150.696) [0m
[37m INFO:2025-09-04 16:15:30,320 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:30,321 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:30,321 - (ensemble_manager.py:128) -    🟡 GP: 29/50 (58.0%) [0m
[37m INFO:2025-09-04 16:15:30,321 - (ensemble_manager.py:128) -    🟡 RBF: 29/50 (58.0%) [0m
[37m INFO:2025-09-04 16:15:30,321 - (ensemble_manager.py:128) -    🟡 TABPFN: 29/50 (58.0%) [0m
[37m INFO:2025-09-04 16:15:30,322 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.5656)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:30,322 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01042): [0m
[37m INFO:2025-09-04 16:15:30,323 - (ensemble_manager.py:326) -    GP: 0.1388 ↓ 0.1333 (MAE:38.6016, 数据:29) [0m
[37m INFO:2025-09-04 16:15:30,323 - (ensemble_manager.py:326) -    RBF: 0.1218 → 0.1168 (MAE:40.8986, 数据:29) [0m
[37m INFO:2025-09-04 16:15:30,323 - (ensemble_manager.py:326) -    TABPFN: 0.7395 ↗ 0.7499 (MAE:17.3330, 数据:29) [0m
[37m INFO:2025-09-04 16:15:30,323 - (bounce.py:864) - 🚀 Iteration 33: No improvement. Best function value -150.696 [历史最优策略] [0m
[37m INFO:2025-09-04 16:15:30,324 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 34 [0m
[37m INFO:2025-09-04 16:15:30,325 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 34 [0m
[37m INFO:2025-09-04 16:15:30,496 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 34, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:30,497 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 34 [0m
[37m INFO:2025-09-04 16:15:30,498 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 34 [0m
[37m INFO:2025-09-04 16:15:30,596 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 34 [0m
[37m WARNING:2025-09-04 16:15:30,944 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-108.940011 [0m
[37m WARNING:2025-09-04 16:15:30,946 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-108.940011 [0m
[37m WARNING:2025-09-04 16:15:30,947 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-107.167055 [0m
[37m WARNING:2025-09-04 16:15:30,952 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-107.167055 [0m
[37m INFO:2025-09-04 16:15:37,819 - (genetic_algorithm.py:367) - 集成代理模型评估了155个GA个体，使用权重: {'gp': 0.13333374879613913, 'rbf': 0.11678517985278873, 'tabpfn': 0.7498810713510722} [0m
[37m INFO:2025-09-04 16:15:37,830 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-150.696) [0m
[37m INFO:2025-09-04 16:15:40,361 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:40,362 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:40,363 - (ensemble_manager.py:128) -    🟡 GP: 30/50 (60.0%) [0m
[37m INFO:2025-09-04 16:15:40,363 - (ensemble_manager.py:128) -    🟡 RBF: 30/50 (60.0%) [0m
[37m INFO:2025-09-04 16:15:40,363 - (ensemble_manager.py:128) -    🟡 TABPFN: 30/50 (60.0%) [0m
[37m INFO:2025-09-04 16:15:40,363 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.8483)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:40,364 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01035): [0m
[37m INFO:2025-09-04 16:15:40,364 - (ensemble_manager.py:326) -    GP: 0.1333 ↓ 0.1280 (MAE:38.3973, 数据:30) [0m
[37m INFO:2025-09-04 16:15:40,364 - (ensemble_manager.py:326) -    RBF: 0.1168 ↓ 0.1118 (MAE:40.6768, 数据:30) [0m
[37m INFO:2025-09-04 16:15:40,364 - (ensemble_manager.py:326) -    TABPFN: 0.7499 ↗ 0.7602 (MAE:16.8285, 数据:30) [0m
[37m INFO:2025-09-04 16:15:40,365 - (bounce.py:864) - 🚀 Iteration 34: No improvement. Best function value -150.696 [历史最优策略] [0m
[37m WARNING:2025-09-04 16:15:40,365 - (bounce.py:876) - 🔄 策略切换: 历史最优策略 -> 全局模型策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:15:40,365 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 35 [0m
[37m INFO:2025-09-04 16:15:40,366 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 35 [0m
[37m INFO:2025-09-04 16:15:40,447 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 35, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:40,448 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 35 [0m
[37m INFO:2025-09-04 16:15:40,449 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 35 [0m
[37m INFO:2025-09-04 16:15:40,553 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 35 [0m
[37m WARNING:2025-09-04 16:15:40,908 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.903718 [0m
[37m WARNING:2025-09-04 16:15:40,910 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.903718 [0m
[37m WARNING:2025-09-04 16:15:40,912 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.145534 [0m
[37m WARNING:2025-09-04 16:15:40,915 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.145534 [0m
[37m INFO:2025-09-04 16:15:46,601 - (genetic_algorithm.py:367) - 集成代理模型评估了152个GA个体，使用权重: {'gp': 0.12798894985495007, 'rbf': 0.11177522815578593, 'tabpfn': 0.7602358219892641} [0m
[37m INFO:2025-09-04 16:15:46,614 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:15:46,615 - (ensemble_manager.py:718) - 📊 输入训练数据: 35个样本 (上次: 35) [0m
[37m WARNING:2025-09-04 16:15:46,619 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.903718 [0m
[37m WARNING:2025-09-04 16:15:46,621 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.903718 [0m
[37m WARNING:2025-09-04 16:15:46,622 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.145534 [0m
[37m WARNING:2025-09-04 16:15:46,624 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.145534 [0m
[37m INFO:2025-09-04 16:15:50,275 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:15:50,276 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.12798894985495007, 'rbf': 0.11177522815578593, 'tabpfn': 0.7602358219892641} [0m
[37m INFO:2025-09-04 16:15:50,276 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-109.9037, -109.9037] → 归一化[0.0000, 1.0000], 均值-109.9037→0.9800 [0m
[37m INFO:2025-09-04 16:15:50,277 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-108.1455, -108.1455] → 归一化[0.5000, 0.5000], 均值-108.1455→0.5000 [0m
[37m INFO:2025-09-04 16:15:50,277 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-144.6899, -101.0559] → 归一化[0.0000, 1.0000], 均值-120.3352→0.5582 [0m
[37m INFO:2025-09-04 16:15:50,280 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第46个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:15:50,280 - (ensemble_manager.py:848) -    📊 GP: 原始分-109.9037 → 归一化1.0000, 权重0.1280, 加权分0.1280 [0m
[37m INFO:2025-09-04 16:15:50,281 - (ensemble_manager.py:848) -    📊 RBF: 原始分-108.1455 → 归一化0.5000, 权重0.1118, 加权分0.0559 [0m
[37m INFO:2025-09-04 16:15:50,281 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-115.1142 → 归一化0.6778, 权重0.7602, 加权分0.5153 [0m
[37m INFO:2025-09-04 16:15:50,282 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:15:52,799 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:15:52,801 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:15:52,801 - (ensemble_manager.py:128) -    🟡 GP: 31/50 (62.0%) [0m
[37m INFO:2025-09-04 16:15:52,801 - (ensemble_manager.py:128) -    🟡 RBF: 31/50 (62.0%) [0m
[37m INFO:2025-09-04 16:15:52,801 - (ensemble_manager.py:128) -    🟡 TABPFN: 31/50 (62.0%) [0m
[37m INFO:2025-09-04 16:15:52,802 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.0254)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:15:52,802 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02871): [0m
[37m INFO:2025-09-04 16:15:52,802 - (ensemble_manager.py:326) -    GP: 0.1280 ↗ 0.1425 (MAE:37.3732, 数据:31) [0m
[37m INFO:2025-09-04 16:15:52,802 - (ensemble_manager.py:326) -    RBF: 0.1118 ↗ 0.1260 (MAE:39.5225, 数据:31) [0m
[37m INFO:2025-09-04 16:15:52,803 - (ensemble_manager.py:326) -    TABPFN: 0.7602 ↘ 0.7315 (MAE:17.4971, 数据:31) [0m
[37m INFO:2025-09-04 16:15:52,803 - (bounce.py:864) - 🚀 Iteration 35: No improvement. Best function value -150.696 [全局模型策略] [0m
[37m INFO:2025-09-04 16:15:52,804 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 36 [0m
[37m INFO:2025-09-04 16:15:52,805 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 36 [0m
[37m INFO:2025-09-04 16:15:52,884 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 36, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:15:52,885 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 36 [0m
[37m INFO:2025-09-04 16:15:52,886 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 36 [0m
[37m INFO:2025-09-04 16:15:52,977 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 36 [0m
[37m WARNING:2025-09-04 16:15:53,310 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.704562 [0m
[37m WARNING:2025-09-04 16:15:53,314 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.704562 [0m
[37m WARNING:2025-09-04 16:15:53,317 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.009668 [0m
[37m WARNING:2025-09-04 16:15:53,319 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.009668 [0m
[37m INFO:2025-09-04 16:15:58,905 - (genetic_algorithm.py:367) - 集成代理模型评估了149个GA个体，使用权重: {'gp': 0.14247338134759477, 'rbf': 0.12600489841869214, 'tabpfn': 0.7315217202337131} [0m
[37m INFO:2025-09-04 16:15:58,917 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:15:58,918 - (ensemble_manager.py:718) - 📊 输入训练数据: 36个样本 (上次: 36) [0m
[37m WARNING:2025-09-04 16:15:58,923 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.704562 [0m
[37m WARNING:2025-09-04 16:15:58,924 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.704562 [0m
[37m WARNING:2025-09-04 16:15:58,925 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.009668 [0m
[37m WARNING:2025-09-04 16:15:58,926 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.009668 [0m
[37m INFO:2025-09-04 16:16:02,424 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:16:02,424 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.14247338134759477, 'rbf': 0.12600489841869214, 'tabpfn': 0.7315217202337131} [0m
[37m INFO:2025-09-04 16:16:02,425 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-109.7046, -109.7046] → 归一化[0.0000, 1.0000], 均值-109.7046→0.9800 [0m
[37m INFO:2025-09-04 16:16:02,425 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-108.0097, -108.0097] → 归一化[0.5000, 0.5000], 均值-108.0097→0.5000 [0m
[37m INFO:2025-09-04 16:16:02,425 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-142.7232, -90.3476] → 归一化[0.0000, 1.0000], 均值-104.4786→0.7302 [0m
[37m INFO:2025-09-04 16:16:02,426 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第7个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:16:02,427 - (ensemble_manager.py:848) -    📊 GP: 原始分-109.7046 → 归一化1.0000, 权重0.1425, 加权分0.1425 [0m
[37m INFO:2025-09-04 16:16:02,427 - (ensemble_manager.py:848) -    📊 RBF: 原始分-108.0097 → 归一化0.5000, 权重0.1260, 加权分0.0630 [0m
[37m INFO:2025-09-04 16:16:02,428 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-92.0081 → 归一化0.9683, 权重0.7315, 加权分0.7083 [0m
[37m INFO:2025-09-04 16:16:02,428 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:16:05,154 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:05,155 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:05,155 - (ensemble_manager.py:128) -    🟡 GP: 32/50 (64.0%) [0m
[37m INFO:2025-09-04 16:16:05,155 - (ensemble_manager.py:128) -    🟡 RBF: 32/50 (64.0%) [0m
[37m INFO:2025-09-04 16:16:05,156 - (ensemble_manager.py:128) -    🟡 TABPFN: 32/50 (64.0%) [0m
[37m INFO:2025-09-04 16:16:05,156 - (ensemble_manager.py:227) - 🔄 检测到性能差异(21.1616)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:05,157 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00731): [0m
[37m INFO:2025-09-04 16:16:05,157 - (ensemble_manager.py:326) -    GP: 0.1425 → 0.1464 (MAE:36.3417, 数据:32) [0m
[37m INFO:2025-09-04 16:16:05,157 - (ensemble_manager.py:326) -    RBF: 0.1260 → 0.1294 (MAE:38.4768, 数据:32) [0m
[37m INFO:2025-09-04 16:16:05,157 - (ensemble_manager.py:326) -    TABPFN: 0.7315 ↓ 0.7242 (MAE:17.3151, 数据:32) [0m
[37m INFO:2025-09-04 16:16:05,158 - (bounce.py:864) - 🚀 Iteration 36: No improvement. Best function value -150.696 [全局模型策略] [0m
[37m INFO:2025-09-04 16:16:05,158 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 37 [0m
[37m INFO:2025-09-04 16:16:05,159 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 37 [0m
[37m INFO:2025-09-04 16:16:05,265 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 37, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:05,266 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 37 [0m
[37m INFO:2025-09-04 16:16:05,268 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 37 [0m
[37m INFO:2025-09-04 16:16:05,378 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 37 [0m
[37m WARNING:2025-09-04 16:16:05,748 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.824592 [0m
[37m WARNING:2025-09-04 16:16:05,752 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.824592 [0m
[37m WARNING:2025-09-04 16:16:05,754 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.173445 [0m
[37m WARNING:2025-09-04 16:16:05,757 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.173445 [0m
[37m INFO:2025-09-04 16:16:12,201 - (genetic_algorithm.py:367) - 集成代理模型评估了163个GA个体，使用权重: {'gp': 0.14638493180302406, 'rbf': 0.12940337939846747, 'tabpfn': 0.7242116887985085} [0m
[37m INFO:2025-09-04 16:16:12,211 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:16:12,211 - (ensemble_manager.py:718) - 📊 输入训练数据: 37个样本 (上次: 37) [0m
[37m WARNING:2025-09-04 16:16:12,215 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-109.824592 [0m
[37m WARNING:2025-09-04 16:16:12,217 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-109.824592 [0m
[37m WARNING:2025-09-04 16:16:12,219 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-108.173445 [0m
[37m WARNING:2025-09-04 16:16:12,220 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-108.173445 [0m
[37m INFO:2025-09-04 16:16:16,018 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:16:16,019 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.14638493180302406, 'rbf': 0.12940337939846747, 'tabpfn': 0.7242116887985085} [0m
[37m INFO:2025-09-04 16:16:16,019 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-109.8246, -109.8246] → 归一化[0.0000, 1.0000], 均值-109.8246→0.9798 [0m
[37m INFO:2025-09-04 16:16:16,020 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-108.1734, -108.1734] → 归一化[0.5000, 0.5000], 均值-108.1734→0.5000 [0m
[37m INFO:2025-09-04 16:16:16,020 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-142.3351, -93.5774] → 归一化[0.0000, 1.0000], 均值-106.6103→0.7327 [0m
[37m INFO:2025-09-04 16:16:16,021 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第13个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:16:16,021 - (ensemble_manager.py:848) -    📊 GP: 原始分-109.8246 → 归一化1.0000, 权重0.1464, 加权分0.1464 [0m
[37m INFO:2025-09-04 16:16:16,022 - (ensemble_manager.py:848) -    📊 RBF: 原始分-108.1734 → 归一化0.5000, 权重0.1294, 加权分0.0647 [0m
[37m INFO:2025-09-04 16:16:16,022 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-93.5774 → 归一化1.0000, 权重0.7242, 加权分0.7242 [0m
[37m INFO:2025-09-04 16:16:16,023 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:16:18,897 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:18,897 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:18,898 - (ensemble_manager.py:128) -    🟡 GP: 33/50 (66.0%) [0m
[37m INFO:2025-09-04 16:16:18,898 - (ensemble_manager.py:128) -    🟡 RBF: 33/50 (66.0%) [0m
[37m INFO:2025-09-04 16:16:18,898 - (ensemble_manager.py:128) -    🟡 TABPFN: 33/50 (66.0%) [0m
[37m INFO:2025-09-04 16:16:18,899 - (ensemble_manager.py:227) - 🔄 检测到性能差异(20.9840)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:18,899 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00401): [0m
[37m INFO:2025-09-04 16:16:18,899 - (ensemble_manager.py:326) -    GP: 0.1464 → 0.1484 (MAE:36.3337, 数据:33) [0m
[37m INFO:2025-09-04 16:16:18,900 - (ensemble_manager.py:326) -    RBF: 0.1294 → 0.1314 (MAE:38.4541, 数据:33) [0m
[37m INFO:2025-09-04 16:16:18,900 - (ensemble_manager.py:326) -    TABPFN: 0.7242 → 0.7202 (MAE:17.4701, 数据:33) [0m
[37m INFO:2025-09-04 16:16:18,900 - (bounce.py:864) - 🚀 Iteration 37: No improvement. Best function value -150.696 [全局模型策略] [0m
[37m WARNING:2025-09-04 16:16:18,901 - (bounce.py:876) - 🔄 策略切换: 全局模型策略 -> 历史最优策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:16:18,901 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 38 [0m
[37m INFO:2025-09-04 16:16:18,902 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 38 [0m
[37m INFO:2025-09-04 16:16:19,058 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 38, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:19,059 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 38 [0m
[37m INFO:2025-09-04 16:16:19,060 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 38 [0m
[37m INFO:2025-09-04 16:16:19,164 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 38 [0m
[37m WARNING:2025-09-04 16:16:19,510 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-110.824039 [0m
[37m WARNING:2025-09-04 16:16:19,513 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-110.824039 [0m
[37m WARNING:2025-09-04 16:16:19,515 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-109.166285 [0m
[37m WARNING:2025-09-04 16:16:19,517 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-109.166285 [0m
[37m INFO:2025-09-04 16:16:26,296 - (genetic_algorithm.py:367) - 集成代理模型评估了153个GA个体，使用权重: {'gp': 0.1483784488585336, 'rbf': 0.13141944833108607, 'tabpfn': 0.7202021028103803} [0m
[37m INFO:2025-09-04 16:16:26,309 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-150.696) [0m
[37m INFO:2025-09-04 16:16:29,183 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:29,184 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:29,184 - (ensemble_manager.py:128) -    🟡 GP: 34/50 (68.0%) [0m
[37m INFO:2025-09-04 16:16:29,185 - (ensemble_manager.py:128) -    🟡 RBF: 34/50 (68.0%) [0m
[37m INFO:2025-09-04 16:16:29,185 - (ensemble_manager.py:128) -    🟡 TABPFN: 34/50 (68.0%) [0m
[37m INFO:2025-09-04 16:16:29,186 - (ensemble_manager.py:227) - 🔄 检测到性能差异(21.3493)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:29,186 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00783): [0m
[37m INFO:2025-09-04 16:16:29,186 - (ensemble_manager.py:326) -    GP: 0.1484 → 0.1443 (MAE:36.4468, 数据:34) [0m
[37m INFO:2025-09-04 16:16:29,186 - (ensemble_manager.py:326) -    RBF: 0.1314 → 0.1277 (MAE:38.5535, 数据:34) [0m
[37m INFO:2025-09-04 16:16:29,187 - (ensemble_manager.py:326) -    TABPFN: 0.7202 ↑ 0.7280 (MAE:17.2042, 数据:34) [0m
[37m INFO:2025-09-04 16:16:29,187 - (bounce.py:857) - ✨ Iteration 38: [92mNew incumbent function value -151.002[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:16:29,188 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 39 [0m
[37m INFO:2025-09-04 16:16:29,188 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 39 [0m
[37m INFO:2025-09-04 16:16:29,259 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 39, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:29,260 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 39 [0m
[37m INFO:2025-09-04 16:16:29,261 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 39 [0m
[37m INFO:2025-09-04 16:16:29,363 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 39 [0m
[37m WARNING:2025-09-04 16:16:29,720 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-111.905646 [0m
[37m WARNING:2025-09-04 16:16:29,721 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-111.905646 [0m
[37m WARNING:2025-09-04 16:16:29,723 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-110.239001 [0m
[37m WARNING:2025-09-04 16:16:29,725 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-110.239001 [0m
[37m INFO:2025-09-04 16:16:36,095 - (genetic_algorithm.py:367) - 集成代理模型评估了151个GA个体，使用权重: {'gp': 0.1442972734200731, 'rbf': 0.1276661904921818, 'tabpfn': 0.7280365360877451} [0m
[37m INFO:2025-09-04 16:16:36,107 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-151.002) [0m
[37m INFO:2025-09-04 16:16:38,969 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:38,970 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:38,970 - (ensemble_manager.py:128) -    🟡 GP: 35/50 (70.0%) [0m
[37m INFO:2025-09-04 16:16:38,971 - (ensemble_manager.py:128) -    🟡 RBF: 35/50 (70.0%) [0m
[37m INFO:2025-09-04 16:16:38,971 - (ensemble_manager.py:128) -    🟡 TABPFN: 35/50 (70.0%) [0m
[37m INFO:2025-09-04 16:16:38,972 - (ensemble_manager.py:227) - 🔄 检测到性能差异(21.7053)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:38,972 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00200): [0m
[37m INFO:2025-09-04 16:16:38,973 - (ensemble_manager.py:326) -    GP: 0.1443 → 0.1431 (MAE:36.9740, 数据:35) [0m
[37m INFO:2025-09-04 16:16:38,973 - (ensemble_manager.py:326) -    RBF: 0.1277 → 0.1268 (MAE:39.0682, 数据:35) [0m
[37m INFO:2025-09-04 16:16:38,973 - (ensemble_manager.py:326) -    TABPFN: 0.7280 → 0.7300 (MAE:17.3628, 数据:35) [0m
[37m INFO:2025-09-04 16:16:38,974 - (bounce.py:857) - ✨ Iteration 39: [92mNew incumbent function value -166.806[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:16:38,974 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 40 [0m
[37m INFO:2025-09-04 16:16:38,975 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 40 [0m
[37m INFO:2025-09-04 16:16:39,164 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 40, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:39,166 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 40 [0m
[37m INFO:2025-09-04 16:16:39,168 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 40 [0m
[37m INFO:2025-09-04 16:16:39,277 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 40 [0m
[37m WARNING:2025-09-04 16:16:39,609 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-113.343744 [0m
[37m WARNING:2025-09-04 16:16:39,610 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-113.343744 [0m
[37m WARNING:2025-09-04 16:16:39,612 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-111.653169 [0m
[37m WARNING:2025-09-04 16:16:39,613 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-111.653169 [0m
[37m INFO:2025-09-04 16:16:45,929 - (genetic_algorithm.py:367) - 集成代理模型评估了150个GA个体，使用权重: {'gp': 0.14310974473768495, 'rbf': 0.12684908601003994, 'tabpfn': 0.7300411692522751} [0m
[37m INFO:2025-09-04 16:16:45,939 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-166.806) [0m
[37m INFO:2025-09-04 16:16:48,528 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:48,528 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:48,529 - (ensemble_manager.py:128) -    🟡 GP: 36/50 (72.0%) [0m
[37m INFO:2025-09-04 16:16:48,529 - (ensemble_manager.py:128) -    🟡 RBF: 36/50 (72.0%) [0m
[37m INFO:2025-09-04 16:16:48,529 - (ensemble_manager.py:128) -    🟡 TABPFN: 36/50 (72.0%) [0m
[37m INFO:2025-09-04 16:16:48,530 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.0236)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:48,530 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00038): [0m
[37m INFO:2025-09-04 16:16:48,530 - (ensemble_manager.py:326) -    GP: 0.1431 → 0.1428 (MAE:37.5481, 数据:36) [0m
[37m INFO:2025-09-04 16:16:48,530 - (ensemble_manager.py:326) -    RBF: 0.1268 → 0.1268 (MAE:39.6311, 数据:36) [0m
[37m INFO:2025-09-04 16:16:48,531 - (ensemble_manager.py:326) -    TABPFN: 0.7300 → 0.7304 (MAE:17.6075, 数据:36) [0m
[37m INFO:2025-09-04 16:16:48,531 - (bounce.py:857) - ✨ Iteration 40: [92mNew incumbent function value -170.987[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:16:48,532 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 41 [0m
[37m INFO:2025-09-04 16:16:48,532 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 41 [0m
[37m INFO:2025-09-04 16:16:48,682 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 41, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:48,683 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 41 [0m
[37m INFO:2025-09-04 16:16:48,685 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 41 [0m
[37m INFO:2025-09-04 16:16:48,781 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 41 [0m
[37m WARNING:2025-09-04 16:16:49,125 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-114.588848 [0m
[37m WARNING:2025-09-04 16:16:49,127 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-114.588848 [0m
[37m WARNING:2025-09-04 16:16:49,129 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-113.100326 [0m
[37m WARNING:2025-09-04 16:16:49,130 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-113.100326 [0m
[37m INFO:2025-09-04 16:16:55,244 - (genetic_algorithm.py:367) - 集成代理模型评估了152个GA个体，使用权重: {'gp': 0.14275309248039006, 'rbf': 0.12682594729215987, 'tabpfn': 0.7304209602274501} [0m
[37m INFO:2025-09-04 16:16:55,253 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-170.987) [0m
[37m INFO:2025-09-04 16:16:58,158 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:16:58,159 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:16:58,159 - (ensemble_manager.py:128) -    🟡 GP: 37/50 (74.0%) [0m
[37m INFO:2025-09-04 16:16:58,160 - (ensemble_manager.py:128) -    🟡 RBF: 37/50 (74.0%) [0m
[37m INFO:2025-09-04 16:16:58,160 - (ensemble_manager.py:128) -    🟡 TABPFN: 37/50 (74.0%) [0m
[37m INFO:2025-09-04 16:16:58,160 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.3827)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:16:58,160 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00381): [0m
[37m INFO:2025-09-04 16:16:58,161 - (ensemble_manager.py:326) -    GP: 0.1428 → 0.1407 (MAE:37.9446, 数据:37) [0m
[37m INFO:2025-09-04 16:16:58,161 - (ensemble_manager.py:326) -    RBF: 0.1268 → 0.1251 (MAE:40.0115, 数据:37) [0m
[37m INFO:2025-09-04 16:16:58,161 - (ensemble_manager.py:326) -    TABPFN: 0.7304 → 0.7342 (MAE:17.6288, 数据:37) [0m
[37m INFO:2025-09-04 16:16:58,162 - (bounce.py:864) - 🚀 Iteration 41: No improvement. Best function value -170.987 [历史最优策略] [0m
[37m INFO:2025-09-04 16:16:58,162 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 42 [0m
[37m INFO:2025-09-04 16:16:58,163 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 42 [0m
[37m INFO:2025-09-04 16:16:58,310 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 42, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:16:58,311 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 42 [0m
[37m INFO:2025-09-04 16:16:58,312 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 42 [0m
[37m INFO:2025-09-04 16:16:58,414 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 42 [0m
[37m WARNING:2025-09-04 16:16:58,752 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-115.652701 [0m
[37m WARNING:2025-09-04 16:16:58,754 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-115.652701 [0m
[37m WARNING:2025-09-04 16:16:58,758 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-114.379026 [0m
[37m WARNING:2025-09-04 16:16:58,760 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-114.379026 [0m
[37m INFO:2025-09-04 16:17:05,975 - (genetic_algorithm.py:367) - 集成代理模型评估了149个GA个体，使用权重: {'gp': 0.1406671067417111, 'rbf': 0.12510447898537222, 'tabpfn': 0.7342284142729166} [0m
[37m INFO:2025-09-04 16:17:05,990 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-170.987) [0m
[37m INFO:2025-09-04 16:17:09,048 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:17:09,049 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:17:09,049 - (ensemble_manager.py:128) -    🟡 GP: 38/50 (76.0%) [0m
[37m INFO:2025-09-04 16:17:09,049 - (ensemble_manager.py:128) -    🟡 RBF: 38/50 (76.0%) [0m
[37m INFO:2025-09-04 16:17:09,050 - (ensemble_manager.py:128) -    🟡 TABPFN: 38/50 (76.0%) [0m
[37m INFO:2025-09-04 16:17:09,050 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.6793)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:17:09,051 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00310): [0m
[37m INFO:2025-09-04 16:17:09,051 - (ensemble_manager.py:326) -    GP: 0.1407 → 0.1389 (MAE:38.2841, 数据:38) [0m
[37m INFO:2025-09-04 16:17:09,051 - (ensemble_manager.py:326) -    RBF: 0.1251 → 0.1237 (MAE:40.3301, 数据:38) [0m
[37m INFO:2025-09-04 16:17:09,051 - (ensemble_manager.py:326) -    TABPFN: 0.7342 → 0.7373 (MAE:17.6508, 数据:38) [0m
[37m INFO:2025-09-04 16:17:09,051 - (bounce.py:864) - 🚀 Iteration 42: No improvement. Best function value -170.987 [历史最优策略] [0m
[37m INFO:2025-09-04 16:17:09,052 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 43 [0m
[37m INFO:2025-09-04 16:17:09,053 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 43 [0m
[37m INFO:2025-09-04 16:17:09,175 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 43, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:17:09,176 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 43 [0m
[37m INFO:2025-09-04 16:17:09,178 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 43 [0m
[37m INFO:2025-09-04 16:17:09,287 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 43 [0m
[37m WARNING:2025-09-04 16:17:09,663 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-116.928505 [0m
[37m WARNING:2025-09-04 16:17:09,665 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-116.928505 [0m
[37m WARNING:2025-09-04 16:17:09,666 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-115.591120 [0m
[37m WARNING:2025-09-04 16:17:09,668 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-115.591120 [0m
[37m INFO:2025-09-04 16:17:16,396 - (genetic_algorithm.py:367) - 集成代理模型评估了147个GA个体，使用权重: {'gp': 0.13894176398268407, 'rbf': 0.12373435570194154, 'tabpfn': 0.7373238803153744} [0m
[37m INFO:2025-09-04 16:17:16,409 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-170.987) [0m
[37m INFO:2025-09-04 16:17:19,583 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:17:19,583 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:17:19,584 - (ensemble_manager.py:128) -    🟡 GP: 39/50 (78.0%) [0m
[37m INFO:2025-09-04 16:17:19,584 - (ensemble_manager.py:128) -    🟡 RBF: 39/50 (78.0%) [0m
[37m INFO:2025-09-04 16:17:19,584 - (ensemble_manager.py:128) -    🟡 TABPFN: 39/50 (78.0%) [0m
[37m INFO:2025-09-04 16:17:19,585 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.0682)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:17:19,585 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00460): [0m
[37m INFO:2025-09-04 16:17:19,585 - (ensemble_manager.py:326) -    GP: 0.1389 → 0.1365 (MAE:38.6728, 数据:39) [0m
[37m INFO:2025-09-04 16:17:19,585 - (ensemble_manager.py:326) -    RBF: 0.1237 → 0.1216 (MAE:40.7007, 数据:39) [0m
[37m INFO:2025-09-04 16:17:19,585 - (ensemble_manager.py:326) -    TABPFN: 0.7373 → 0.7419 (MAE:17.6325, 数据:39) [0m
[37m INFO:2025-09-04 16:17:19,586 - (bounce.py:864) - 🚀 Iteration 43: No improvement. Best function value -170.987 [历史最优策略] [0m
[37m WARNING:2025-09-04 16:17:19,586 - (bounce.py:876) - 🔄 策略切换: 历史最优策略 -> 全局模型策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:17:19,586 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 44 [0m
[37m INFO:2025-09-04 16:17:19,587 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 44 [0m
[37m INFO:2025-09-04 16:17:19,695 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 44, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:17:19,696 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 44 [0m
[37m INFO:2025-09-04 16:17:19,698 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 44 [0m
[37m INFO:2025-09-04 16:17:19,793 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 44 [0m
[37m WARNING:2025-09-04 16:17:20,169 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-118.217023 [0m
[37m WARNING:2025-09-04 16:17:20,174 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-118.217023 [0m
[37m WARNING:2025-09-04 16:17:20,176 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-116.836169 [0m
[37m WARNING:2025-09-04 16:17:20,178 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-116.836169 [0m
[37m INFO:2025-09-04 16:17:27,318 - (genetic_algorithm.py:367) - 集成代理模型评估了162个GA个体，使用权重: {'gp': 0.13645229308717538, 'rbf': 0.12162804528267847, 'tabpfn': 0.7419196616301461} [0m
[37m INFO:2025-09-04 16:17:27,331 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:17:27,332 - (ensemble_manager.py:718) - 📊 输入训练数据: 44个样本 (上次: 44) [0m
[37m WARNING:2025-09-04 16:17:27,337 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-118.217022 [0m
[37m WARNING:2025-09-04 16:17:27,337 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-118.217022 [0m
[37m WARNING:2025-09-04 16:17:27,339 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-116.836169 [0m
[37m WARNING:2025-09-04 16:17:27,341 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-116.836169 [0m
[37m INFO:2025-09-04 16:17:31,255 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:17:31,256 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.13645229308717538, 'rbf': 0.12162804528267847, 'tabpfn': 0.7419196616301461} [0m
[37m INFO:2025-09-04 16:17:31,256 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-118.2170, -118.2170] → 归一化[0.0000, 1.0000], 均值-118.2170→0.9691 [0m
[37m INFO:2025-09-04 16:17:31,256 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-116.8362, -116.8362] → 归一化[0.5000, 0.5000], 均值-116.8362→0.5000 [0m
[37m INFO:2025-09-04 16:17:31,257 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-156.2838, -108.4023] → 归一化[0.0000, 1.0000], 均值-129.3855→0.5618 [0m
[37m INFO:2025-09-04 16:17:31,259 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第30个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:17:31,259 - (ensemble_manager.py:848) -    📊 GP: 原始分-118.2170 → 归一化0.9982, 权重0.1365, 加权分0.1362 [0m
[37m INFO:2025-09-04 16:17:31,260 - (ensemble_manager.py:848) -    📊 RBF: 原始分-116.8362 → 归一化0.5000, 权重0.1216, 加权分0.0608 [0m
[37m INFO:2025-09-04 16:17:31,260 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-108.4023 → 归一化1.0000, 权重0.7419, 加权分0.7419 [0m
[37m INFO:2025-09-04 16:17:31,261 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:17:34,659 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:17:34,660 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:17:34,661 - (ensemble_manager.py:128) -    🟡 GP: 40/50 (80.0%) [0m
[37m INFO:2025-09-04 16:17:34,661 - (ensemble_manager.py:128) -    🟡 RBF: 40/50 (80.0%) [0m
[37m INFO:2025-09-04 16:17:34,662 - (ensemble_manager.py:128) -    🟡 TABPFN: 40/50 (80.0%) [0m
[37m INFO:2025-09-04 16:17:34,662 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.8227)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:17:34,663 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00518): [0m
[37m INFO:2025-09-04 16:17:34,663 - (ensemble_manager.py:326) -    GP: 0.1365 → 0.1391 (MAE:38.6378, 数据:40) [0m
[37m INFO:2025-09-04 16:17:34,663 - (ensemble_manager.py:326) -    RBF: 0.1216 → 0.1242 (MAE:40.6495, 数据:40) [0m
[37m INFO:2025-09-04 16:17:34,663 - (ensemble_manager.py:326) -    TABPFN: 0.7419 ↓ 0.7367 (MAE:17.8268, 数据:40) [0m
[37m INFO:2025-09-04 16:17:34,664 - (bounce.py:864) - 🚀 Iteration 44: No improvement. Best function value -170.987 [全局模型策略] [0m
[37m INFO:2025-09-04 16:17:34,664 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 45 [0m
[37m INFO:2025-09-04 16:17:34,665 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 45 [0m
[37m INFO:2025-09-04 16:17:34,766 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 45, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:17:34,771 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 45 [0m
[37m INFO:2025-09-04 16:17:34,775 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 45 [0m
[37m INFO:2025-09-04 16:17:34,878 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 45 [0m
[37m WARNING:2025-09-04 16:17:35,266 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-119.094345 [0m
[37m WARNING:2025-09-04 16:17:35,269 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-119.094345 [0m
[37m WARNING:2025-09-04 16:17:35,271 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-117.695138 [0m
[37m WARNING:2025-09-04 16:17:35,273 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-117.695138 [0m
[37m INFO:2025-09-04 16:17:42,177 - (genetic_algorithm.py:367) - 集成代理模型评估了151个GA个体，使用权重: {'gp': 0.1390500022791535, 'rbf': 0.1242116414970555, 'tabpfn': 0.7367383562237909} [0m
[37m INFO:2025-09-04 16:17:42,187 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:17:42,187 - (ensemble_manager.py:718) - 📊 输入训练数据: 45个样本 (上次: 45) [0m
[37m WARNING:2025-09-04 16:17:42,192 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-119.094342 [0m
[37m WARNING:2025-09-04 16:17:42,193 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-119.094342 [0m
[37m WARNING:2025-09-04 16:17:42,194 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-117.695138 [0m
[37m WARNING:2025-09-04 16:17:42,196 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-117.695138 [0m
[37m INFO:2025-09-04 16:17:46,988 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:17:46,988 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.1390500022791535, 'rbf': 0.1242116414970555, 'tabpfn': 0.7367383562237909} [0m
[37m INFO:2025-09-04 16:17:46,989 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-119.0943, -119.0943] → 归一化[0.0000, 1.0000], 均值-119.0943→0.9579 [0m
[37m INFO:2025-09-04 16:17:46,989 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-117.6951, -117.6951] → 归一化[0.5000, 0.5000], 均值-117.6951→0.5000 [0m
[37m INFO:2025-09-04 16:17:46,989 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-157.0721, -119.4553] → 归一化[0.0000, 1.0000], 均值-129.6442→0.7291 [0m
[37m INFO:2025-09-04 16:17:46,992 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第36个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:17:46,992 - (ensemble_manager.py:848) -    📊 GP: 原始分-119.0943 → 归一化1.0000, 权重0.1391, 加权分0.1391 [0m
[37m INFO:2025-09-04 16:17:46,992 - (ensemble_manager.py:848) -    📊 RBF: 原始分-117.6951 → 归一化0.5000, 权重0.1242, 加权分0.0621 [0m
[37m INFO:2025-09-04 16:17:46,993 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-119.4553 → 归一化1.0000, 权重0.7367, 加权分0.7367 [0m
[37m INFO:2025-09-04 16:17:46,994 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:17:50,216 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:17:50,217 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:17:50,217 - (ensemble_manager.py:128) -    🟡 GP: 41/50 (82.0%) [0m
[37m INFO:2025-09-04 16:17:50,217 - (ensemble_manager.py:128) -    🟡 RBF: 41/50 (82.0%) [0m
[37m INFO:2025-09-04 16:17:50,218 - (ensemble_manager.py:128) -    🟡 TABPFN: 41/50 (82.0%) [0m
[37m INFO:2025-09-04 16:17:50,219 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.3377)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:17:50,219 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00338): [0m
[37m INFO:2025-09-04 16:17:50,219 - (ensemble_manager.py:326) -    GP: 0.1391 → 0.1408 (MAE:38.0284, 数据:41) [0m
[37m INFO:2025-09-04 16:17:50,219 - (ensemble_manager.py:326) -    RBF: 0.1242 → 0.1258 (MAE:40.0252, 数据:41) [0m
[37m INFO:2025-09-04 16:17:50,220 - (ensemble_manager.py:326) -    TABPFN: 0.7367 → 0.7334 (MAE:17.6875, 数据:41) [0m
[37m INFO:2025-09-04 16:17:50,220 - (bounce.py:864) - 🚀 Iteration 45: No improvement. Best function value -170.987 [全局模型策略] [0m
[37m INFO:2025-09-04 16:17:50,221 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 46 [0m
[37m INFO:2025-09-04 16:17:50,221 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 46 [0m
[37m INFO:2025-09-04 16:17:50,412 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 46, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:17:50,414 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 46 [0m
[37m INFO:2025-09-04 16:17:50,415 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 46 [0m
[37m INFO:2025-09-04 16:17:50,543 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 46 [0m
[37m WARNING:2025-09-04 16:17:50,942 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-119.400072 [0m
[37m WARNING:2025-09-04 16:17:50,946 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-119.400072 [0m
[37m WARNING:2025-09-04 16:17:50,949 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-118.022317 [0m
[37m WARNING:2025-09-04 16:17:50,951 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-118.022317 [0m
[37m INFO:2025-09-04 16:17:58,732 - (genetic_algorithm.py:367) - 集成代理模型评估了158个GA个体，使用权重: {'gp': 0.14083853711537775, 'rbf': 0.12580372299911977, 'tabpfn': 0.7333577398855026} [0m
[37m INFO:2025-09-04 16:17:58,743 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:17:58,744 - (ensemble_manager.py:718) - 📊 输入训练数据: 46个样本 (上次: 46) [0m
[37m WARNING:2025-09-04 16:17:58,748 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-119.400072 [0m
[37m WARNING:2025-09-04 16:17:58,750 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-119.400072 [0m
[37m WARNING:2025-09-04 16:17:58,752 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-118.022317 [0m
[37m WARNING:2025-09-04 16:17:58,754 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-118.022317 [0m
[37m INFO:2025-09-04 16:18:03,810 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:18:03,810 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.14083853711537775, 'rbf': 0.12580372299911977, 'tabpfn': 0.7333577398855026} [0m
[37m INFO:2025-09-04 16:18:03,811 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-119.4001, -119.4001] → 归一化[0.0000, 1.0000], 均值-119.4001→0.9548 [0m
[37m INFO:2025-09-04 16:18:03,812 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-118.0223, -118.0223] → 归一化[0.5000, 0.5000], 均值-118.0223→0.5000 [0m
[37m INFO:2025-09-04 16:18:03,812 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-156.9065, -120.0712] → 归一化[0.0000, 1.0000], 均值-130.0551→0.7290 [0m
[37m INFO:2025-09-04 16:18:03,814 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第46个候选点 tensor([ 1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:18:03,815 - (ensemble_manager.py:848) -    📊 GP: 原始分-119.4001 → 归一化1.0000, 权重0.1408, 加权分0.1408 [0m
[37m INFO:2025-09-04 16:18:03,815 - (ensemble_manager.py:848) -    📊 RBF: 原始分-118.0223 → 归一化0.5000, 权重0.1258, 加权分0.0629 [0m
[37m INFO:2025-09-04 16:18:03,815 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-120.0712 → 归一化1.0000, 权重0.7334, 加权分0.7334 [0m
[37m INFO:2025-09-04 16:18:03,816 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:18:06,831 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:18:06,832 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:18:06,832 - (ensemble_manager.py:128) -    🟡 GP: 42/50 (84.0%) [0m
[37m INFO:2025-09-04 16:18:06,832 - (ensemble_manager.py:128) -    🟡 RBF: 42/50 (84.0%) [0m
[37m INFO:2025-09-04 16:18:06,832 - (ensemble_manager.py:128) -    🟡 TABPFN: 42/50 (84.0%) [0m
[37m INFO:2025-09-04 16:18:06,833 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.3390)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:18:06,833 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00132): [0m
[37m INFO:2025-09-04 16:18:06,833 - (ensemble_manager.py:326) -    GP: 0.1408 → 0.1401 (MAE:37.9530, 数据:42) [0m
[37m INFO:2025-09-04 16:18:06,834 - (ensemble_manager.py:326) -    RBF: 0.1258 → 0.1252 (MAE:39.9351, 数据:42) [0m
[37m INFO:2025-09-04 16:18:06,834 - (ensemble_manager.py:326) -    TABPFN: 0.7334 → 0.7347 (MAE:17.5961, 数据:42) [0m
[37m INFO:2025-09-04 16:18:06,835 - (bounce.py:864) - 🚀 Iteration 46: No improvement. Best function value -170.987 [全局模型策略] [0m
[37m WARNING:2025-09-04 16:18:06,835 - (bounce.py:876) - 🔄 策略切换: 全局模型策略 -> 历史最优策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:18:06,836 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 47 [0m
[37m INFO:2025-09-04 16:18:06,836 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 47 [0m
[37m INFO:2025-09-04 16:18:06,959 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 47, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:18:06,960 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 47 [0m
[37m INFO:2025-09-04 16:18:06,961 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 47 [0m
[37m INFO:2025-09-04 16:18:07,064 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 47 [0m
[37m WARNING:2025-09-04 16:18:07,426 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-120.181972 [0m
[37m WARNING:2025-09-04 16:18:07,427 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-120.181972 [0m
[37m WARNING:2025-09-04 16:18:07,429 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-118.793397 [0m
[37m WARNING:2025-09-04 16:18:07,431 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-118.793397 [0m
[37m INFO:2025-09-04 16:18:14,072 - (genetic_algorithm.py:367) - 集成代理模型评估了146个GA个体，使用权重: {'gp': 0.14012458366881067, 'rbf': 0.1251974560804628, 'tabpfn': 0.7346779602507266} [0m
[37m INFO:2025-09-04 16:18:14,087 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-170.987) [0m
[37m INFO:2025-09-04 16:18:17,286 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:18:17,286 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:18:17,287 - (ensemble_manager.py:128) -    🟡 GP: 43/50 (86.0%) [0m
[37m INFO:2025-09-04 16:18:17,287 - (ensemble_manager.py:128) -    🟡 RBF: 43/50 (86.0%) [0m
[37m INFO:2025-09-04 16:18:17,288 - (ensemble_manager.py:128) -    🟡 TABPFN: 43/50 (86.0%) [0m
[37m INFO:2025-09-04 16:18:17,288 - (ensemble_manager.py:227) - 🔄 检测到性能差异(22.7224)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:18:17,289 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00118): [0m
[37m INFO:2025-09-04 16:18:17,289 - (ensemble_manager.py:326) -    GP: 0.1401 → 0.1394 (MAE:38.5838, 数据:43) [0m
[37m INFO:2025-09-04 16:18:17,290 - (ensemble_manager.py:326) -    RBF: 0.1252 → 0.1248 (MAE:40.5520, 数据:43) [0m
[37m INFO:2025-09-04 16:18:17,290 - (ensemble_manager.py:326) -    TABPFN: 0.7347 → 0.7359 (MAE:17.8296, 数据:43) [0m
[37m INFO:2025-09-04 16:18:17,291 - (bounce.py:857) - ✨ Iteration 47: [92mNew incumbent function value -185.257[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:18:17,291 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 48 [0m
[37m INFO:2025-09-04 16:18:17,292 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 48 [0m
[37m INFO:2025-09-04 16:18:17,392 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 48, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:18:17,393 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 48 [0m
[37m INFO:2025-09-04 16:18:17,395 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 48 [0m
[37m INFO:2025-09-04 16:18:17,492 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 48 [0m
[37m WARNING:2025-09-04 16:18:17,858 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-121.619037 [0m
[37m WARNING:2025-09-04 16:18:17,861 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-121.619037 [0m
[37m WARNING:2025-09-04 16:18:17,864 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-120.178051 [0m
[37m WARNING:2025-09-04 16:18:17,867 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-120.178051 [0m
[37m INFO:2025-09-04 16:18:25,025 - (genetic_algorithm.py:367) - 集成代理模型评估了153个GA个体，使用权重: {'gp': 0.13935332464956154, 'rbf': 0.12478875005161885, 'tabpfn': 0.7358579252988197} [0m
[37m INFO:2025-09-04 16:18:25,034 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-185.257) [0m
[37m INFO:2025-09-04 16:18:28,570 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:18:28,571 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:18:28,571 - (ensemble_manager.py:128) -    🟡 GP: 44/50 (88.0%) [0m
[37m INFO:2025-09-04 16:18:28,571 - (ensemble_manager.py:128) -    🟡 RBF: 44/50 (88.0%) [0m
[37m INFO:2025-09-04 16:18:28,571 - (ensemble_manager.py:128) -    🟡 TABPFN: 44/50 (88.0%) [0m
[37m INFO:2025-09-04 16:18:28,572 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.1044)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:18:28,572 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00082): [0m
[37m INFO:2025-09-04 16:18:28,572 - (ensemble_manager.py:326) -    GP: 0.1394 → 0.1388 (MAE:39.2343, 数据:44) [0m
[37m INFO:2025-09-04 16:18:28,573 - (ensemble_manager.py:326) -    RBF: 0.1248 → 0.1245 (MAE:41.1905, 数据:44) [0m
[37m INFO:2025-09-04 16:18:28,573 - (ensemble_manager.py:326) -    TABPFN: 0.7359 → 0.7367 (MAE:18.0861, 数据:44) [0m
[37m INFO:2025-09-04 16:18:28,573 - (bounce.py:857) - ✨ Iteration 48: [92mNew incumbent function value -188.824[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:18:28,574 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 49 [0m
[37m INFO:2025-09-04 16:18:28,575 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 49 [0m
[37m INFO:2025-09-04 16:18:28,706 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 49, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:18:28,707 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 49 [0m
[37m INFO:2025-09-04 16:18:28,709 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 49 [0m
[37m INFO:2025-09-04 16:18:28,818 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 49 [0m
[37m WARNING:2025-09-04 16:18:29,149 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-122.757042 [0m
[37m WARNING:2025-09-04 16:18:29,152 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-122.757042 [0m
[37m WARNING:2025-09-04 16:18:29,154 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-121.578996 [0m
[37m WARNING:2025-09-04 16:18:29,156 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-121.578996 [0m
[37m INFO:2025-09-04 16:18:36,001 - (genetic_algorithm.py:367) - 集成代理模型评估了142个GA个体，使用权重: {'gp': 0.13877369664427472, 'rbf': 0.12454684503736171, 'tabpfn': 0.7366794583183636} [0m
[37m INFO:2025-09-04 16:18:36,016 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-188.824) [0m
[37m INFO:2025-09-04 16:18:39,308 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:18:39,308 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:18:39,308 - (ensemble_manager.py:128) -    🟡 GP: 45/50 (90.0%) [0m
[37m INFO:2025-09-04 16:18:39,309 - (ensemble_manager.py:128) -    🟡 RBF: 45/50 (90.0%) [0m
[37m INFO:2025-09-04 16:18:39,309 - (ensemble_manager.py:128) -    🟡 TABPFN: 45/50 (90.0%) [0m
[37m INFO:2025-09-04 16:18:39,309 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.4925)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:18:39,309 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00277): [0m
[37m INFO:2025-09-04 16:18:39,310 - (ensemble_manager.py:326) -    GP: 0.1388 → 0.1372 (MAE:39.7513, 数据:45) [0m
[37m INFO:2025-09-04 16:18:39,310 - (ensemble_manager.py:326) -    RBF: 0.1245 → 0.1233 (MAE:41.6902, 数据:45) [0m
[37m INFO:2025-09-04 16:18:39,310 - (ensemble_manager.py:326) -    TABPFN: 0.7367 → 0.7395 (MAE:18.1977, 数据:45) [0m
[37m INFO:2025-09-04 16:18:39,311 - (bounce.py:864) - 🚀 Iteration 49: No improvement. Best function value -188.824 [历史最优策略] [0m
[37m INFO:2025-09-04 16:18:39,311 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 50 [0m
[37m INFO:2025-09-04 16:18:39,312 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 50 [0m
[37m INFO:2025-09-04 16:18:39,411 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 50, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:18:39,413 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 50 [0m
[37m INFO:2025-09-04 16:18:39,414 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 50 [0m
[37m INFO:2025-09-04 16:18:39,604 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 50 [0m
[37m WARNING:2025-09-04 16:18:39,966 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-123.744261 [0m
[37m WARNING:2025-09-04 16:18:39,969 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-123.744261 [0m
[37m WARNING:2025-09-04 16:18:39,971 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-122.852552 [0m
[37m WARNING:2025-09-04 16:18:39,972 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-122.852552 [0m
[37m INFO:2025-09-04 16:18:47,374 - (genetic_algorithm.py:367) - 集成代理模型评估了158个GA个体，使用权重: {'gp': 0.1372076543090419, 'rbf': 0.12334022334978666, 'tabpfn': 0.7394521223411715} [0m
[37m INFO:2025-09-04 16:18:47,386 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-188.824) [0m
[37m INFO:2025-09-04 16:18:50,665 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:18:50,666 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:18:50,666 - (ensemble_manager.py:128) -    🟡 GP: 46/50 (92.0%) [0m
[37m INFO:2025-09-04 16:18:50,666 - (ensemble_manager.py:128) -    🟡 RBF: 46/50 (92.0%) [0m
[37m INFO:2025-09-04 16:18:50,667 - (ensemble_manager.py:128) -    🟡 TABPFN: 46/50 (92.0%) [0m
[37m INFO:2025-09-04 16:18:50,667 - (ensemble_manager.py:227) - 🔄 检测到性能差异(23.8765)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:18:50,668 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00339): [0m
[37m INFO:2025-09-04 16:18:50,668 - (ensemble_manager.py:326) -    GP: 0.1372 → 0.1353 (MAE:40.2177, 数据:46) [0m
[37m INFO:2025-09-04 16:18:50,668 - (ensemble_manager.py:326) -    RBF: 0.1233 → 0.1218 (MAE:42.1339, 数据:46) [0m
[37m INFO:2025-09-04 16:18:50,668 - (ensemble_manager.py:326) -    TABPFN: 0.7395 → 0.7428 (MAE:18.2574, 数据:46) [0m
[37m INFO:2025-09-04 16:18:50,669 - (bounce.py:864) - 🚀 Iteration 50: No improvement. Best function value -188.824 [历史最优策略] [0m
[37m INFO:2025-09-04 16:18:50,669 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 51 [0m
[37m INFO:2025-09-04 16:18:50,670 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 51 [0m
[37m INFO:2025-09-04 16:18:50,832 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 51, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:18:50,834 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 51 [0m
[37m INFO:2025-09-04 16:18:50,836 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 51 [0m
[37m INFO:2025-09-04 16:18:50,938 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 51 [0m
[37m WARNING:2025-09-04 16:18:51,328 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-124.666362 [0m
[37m WARNING:2025-09-04 16:18:51,331 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-124.666362 [0m
[37m WARNING:2025-09-04 16:18:51,333 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-124.070151 [0m
[37m WARNING:2025-09-04 16:18:51,335 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-124.070151 [0m
[37m INFO:2025-09-04 16:18:58,532 - (genetic_algorithm.py:367) - 集成代理模型评估了161个GA个体，使用权重: {'gp': 0.13532260192282497, 'rbf': 0.12183992289650139, 'tabpfn': 0.7428374751806737} [0m
[37m INFO:2025-09-04 16:18:58,543 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-188.824) [0m
[37m INFO:2025-09-04 16:19:02,068 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:19:02,069 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:19:02,069 - (ensemble_manager.py:128) -    🟡 GP: 47/50 (94.0%) [0m
[37m INFO:2025-09-04 16:19:02,069 - (ensemble_manager.py:128) -    🟡 RBF: 47/50 (94.0%) [0m
[37m INFO:2025-09-04 16:19:02,070 - (ensemble_manager.py:128) -    🟡 TABPFN: 47/50 (94.0%) [0m
[37m INFO:2025-09-04 16:19:02,070 - (ensemble_manager.py:227) - 🔄 检测到性能差异(24.2453)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:19:02,070 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00153): [0m
[37m INFO:2025-09-04 16:19:02,071 - (ensemble_manager.py:326) -    GP: 0.1353 → 0.1344 (MAE:40.8029, 数据:47) [0m
[37m INFO:2025-09-04 16:19:02,071 - (ensemble_manager.py:326) -    RBF: 0.1218 → 0.1213 (MAE:42.6910, 数据:47) [0m
[37m INFO:2025-09-04 16:19:02,071 - (ensemble_manager.py:326) -    TABPFN: 0.7428 → 0.7444 (MAE:18.4458, 数据:47) [0m
[37m INFO:2025-09-04 16:19:02,071 - (bounce.py:857) - ✨ Iteration 51: [92mNew incumbent function value -192.392[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:19:02,072 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 52 [0m
[37m INFO:2025-09-04 16:19:02,073 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 52 [0m
[37m INFO:2025-09-04 16:19:02,175 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 52, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:19:02,176 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 52 [0m
[37m INFO:2025-09-04 16:19:02,177 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 52 [0m
[37m INFO:2025-09-04 16:19:02,276 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 52 [0m
[37m WARNING:2025-09-04 16:19:02,621 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-125.619400 [0m
[37m WARNING:2025-09-04 16:19:02,623 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-125.619400 [0m
[37m WARNING:2025-09-04 16:19:02,626 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-125.384030 [0m
[37m WARNING:2025-09-04 16:19:02,629 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-125.384030 [0m
[37m INFO:2025-09-04 16:19:11,437 - (genetic_algorithm.py:367) - 集成代理模型评估了153个GA个体，使用权重: {'gp': 0.13435426465850178, 'rbf': 0.12128230963191733, 'tabpfn': 0.7443634257095809} [0m
[37m INFO:2025-09-04 16:19:11,449 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:19:15,783 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:19:15,783 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:19:15,784 - (ensemble_manager.py:128) -    🟡 GP: 48/50 (96.0%) [0m
[37m INFO:2025-09-04 16:19:15,784 - (ensemble_manager.py:128) -    🟡 RBF: 48/50 (96.0%) [0m
[37m INFO:2025-09-04 16:19:15,784 - (ensemble_manager.py:128) -    🟡 TABPFN: 48/50 (96.0%) [0m
[37m INFO:2025-09-04 16:19:15,785 - (ensemble_manager.py:227) - 🔄 检测到性能差异(24.6124)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:19:15,785 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00302): [0m
[37m INFO:2025-09-04 16:19:15,785 - (ensemble_manager.py:326) -    GP: 0.1344 → 0.1326 (MAE:41.2760, 数据:48) [0m
[37m INFO:2025-09-04 16:19:15,786 - (ensemble_manager.py:326) -    RBF: 0.1213 → 0.1200 (MAE:43.1297, 数据:48) [0m
[37m INFO:2025-09-04 16:19:15,786 - (ensemble_manager.py:326) -    TABPFN: 0.7444 → 0.7474 (MAE:18.5173, 数据:48) [0m
[37m INFO:2025-09-04 16:19:15,786 - (bounce.py:864) - 🚀 Iteration 52: No improvement. Best function value -192.392 [历史最优策略] [0m
[37m INFO:2025-09-04 16:19:15,787 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 53 [0m
[37m INFO:2025-09-04 16:19:15,788 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 53 [0m
[37m INFO:2025-09-04 16:19:16,022 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 53, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:19:16,023 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 53 [0m
[37m INFO:2025-09-04 16:19:16,028 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 53 [0m
[37m INFO:2025-09-04 16:19:16,167 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 53 [0m
[37m WARNING:2025-09-04 16:19:16,592 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-126.580040 [0m
[37m WARNING:2025-09-04 16:19:16,593 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-126.580040 [0m
[37m WARNING:2025-09-04 16:19:16,595 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-126.586803 [0m
[37m WARNING:2025-09-04 16:19:16,597 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-126.586803 [0m
[37m INFO:2025-09-04 16:19:25,092 - (genetic_algorithm.py:367) - 集成代理模型评估了154个GA个体，使用权重: {'gp': 0.13262480066164561, 'rbf': 0.11999136183316635, 'tabpfn': 0.7473838375051881} [0m
[37m INFO:2025-09-04 16:19:25,103 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:19:28,818 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:19:28,819 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:19:28,819 - (ensemble_manager.py:128) -    🟡 GP: 49/50 (98.0%) [0m
[37m INFO:2025-09-04 16:19:28,819 - (ensemble_manager.py:128) -    🟡 RBF: 49/50 (98.0%) [0m
[37m INFO:2025-09-04 16:19:28,820 - (ensemble_manager.py:128) -    🟡 TABPFN: 49/50 (98.0%) [0m
[37m INFO:2025-09-04 16:19:28,820 - (ensemble_manager.py:227) - 🔄 检测到性能差异(24.9802)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:19:28,820 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00356): [0m
[37m INFO:2025-09-04 16:19:28,821 - (ensemble_manager.py:326) -    GP: 0.1326 → 0.1306 (MAE:41.7102, 数据:49) [0m
[37m INFO:2025-09-04 16:19:28,821 - (ensemble_manager.py:326) -    RBF: 0.1200 → 0.1184 (MAE:43.5259, 数据:49) [0m
[37m INFO:2025-09-04 16:19:28,821 - (ensemble_manager.py:326) -    TABPFN: 0.7474 → 0.7509 (MAE:18.5458, 数据:49) [0m
[37m INFO:2025-09-04 16:19:28,821 - (bounce.py:864) - 🚀 Iteration 53: No improvement. Best function value -192.392 [历史最优策略] [0m
[37m INFO:2025-09-04 16:19:28,822 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 54 [0m
[37m INFO:2025-09-04 16:19:28,823 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 54 [0m
[37m INFO:2025-09-04 16:19:29,008 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 54, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:19:29,008 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 54 [0m
[37m INFO:2025-09-04 16:19:29,010 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 54 [0m
[37m INFO:2025-09-04 16:19:29,149 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 54 [0m
[37m WARNING:2025-09-04 16:19:29,542 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-127.745029 [0m
[37m WARNING:2025-09-04 16:19:29,544 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-127.745029 [0m
[37m INFO:2025-09-04 16:19:37,460 - (genetic_algorithm.py:367) - 集成代理模型评估了150个GA个体，使用权重: {'gp': 0.13061931884479414, 'rbf': 0.11843732176632507, 'tabpfn': 0.7509433593888808} [0m
[37m INFO:2025-09-04 16:19:37,473 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:19:41,849 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:19:41,849 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:19:41,850 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:19:41,850 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:19:41,851 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:19:41,851 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:19:41,852 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:19:41,852 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:19:41,852 - (ensemble_manager.py:227) - 🔄 检测到性能差异(25.3361)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:19:41,853 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00377): [0m
[37m INFO:2025-09-04 16:19:41,853 - (ensemble_manager.py:326) -    GP: 0.1306 → 0.1285 (MAE:42.1091, 数据:50) [0m
[37m INFO:2025-09-04 16:19:41,853 - (ensemble_manager.py:326) -    RBF: 0.1184 → 0.1168 (MAE:43.8831, 数据:50) [0m
[37m INFO:2025-09-04 16:19:41,854 - (ensemble_manager.py:326) -    TABPFN: 0.7509 → 0.7547 (MAE:18.5470, 数据:50) [0m
[37m INFO:2025-09-04 16:19:41,854 - (bounce.py:864) - 🚀 Iteration 54: No improvement. Best function value -192.392 [历史最优策略] [0m
[37m WARNING:2025-09-04 16:19:41,854 - (bounce.py:876) - 🔄 策略切换: 历史最优策略 -> 全局模型策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:19:41,855 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 55 [0m
[37m INFO:2025-09-04 16:19:41,855 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 55 [0m
[37m INFO:2025-09-04 16:19:41,982 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 55, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:19:41,983 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 55 [0m
[37m INFO:2025-09-04 16:19:41,984 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 55 [0m
[37m INFO:2025-09-04 16:19:42,085 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 55 [0m
[37m WARNING:2025-09-04 16:19:42,488 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-128.861137 [0m
[37m WARNING:2025-09-04 16:19:42,491 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-128.861137 [0m
[37m INFO:2025-09-04 16:19:50,439 - (genetic_algorithm.py:367) - 集成代理模型评估了165个GA个体，使用权重: {'gp': 0.12850266795864126, 'rbf': 0.11678082098669557, 'tabpfn': 0.7547165110546632} [0m
[37m INFO:2025-09-04 16:19:50,450 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:19:50,451 - (ensemble_manager.py:718) - 📊 输入训练数据: 55个样本 (上次: 55) [0m
[37m WARNING:2025-09-04 16:19:50,456 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-128.321004 [0m
[37m WARNING:2025-09-04 16:19:50,458 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-128.321004 [0m
[37m WARNING:2025-09-04 16:19:50,459 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-128.861137 [0m
[37m WARNING:2025-09-04 16:19:50,460 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-128.861137 [0m
[37m INFO:2025-09-04 16:19:56,308 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:19:56,308 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.12850266795864126, 'rbf': 0.11678082098669557, 'tabpfn': 0.7547165110546632} [0m
[37m INFO:2025-09-04 16:19:56,308 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-128.3210, -128.3210] → 归一化[0.0000, 1.0000], 均值-128.3210→0.8945 [0m
[37m INFO:2025-09-04 16:19:56,309 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-128.8611, -128.8611] → 归一化[0.5000, 0.5000], 均值-128.8611→0.5000 [0m
[37m INFO:2025-09-04 16:19:56,309 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-172.0481, -158.1486] → 归一化[0.0000, 1.0000], 均值-159.7827→0.8824 [0m
[37m INFO:2025-09-04 16:19:56,311 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第43个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:19:56,311 - (ensemble_manager.py:848) -    📊 GP: 原始分-128.3210 → 归一化1.0000, 权重0.1285, 加权分0.1285 [0m
[37m INFO:2025-09-04 16:19:56,312 - (ensemble_manager.py:848) -    📊 RBF: 原始分-128.8611 → 归一化0.5000, 权重0.1168, 加权分0.0584 [0m
[37m INFO:2025-09-04 16:19:56,312 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-158.4741 → 归一化0.9766, 权重0.7547, 加权分0.7370 [0m
[37m INFO:2025-09-04 16:19:56,312 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:20:00,818 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-29.700618361562906, -53.24739778572175), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:00,819 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:00,819 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-30.390576620647096, -53.24739778572175), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:00,819 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:00,820 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-37.28786617379044, -53.24739778572175), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:00,820 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:00,820 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:20:00,820 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:20:00,821 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:00,821 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:00,821 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:00,822 - (ensemble_manager.py:227) - 🔄 检测到性能差异(25.9517)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:20:00,822 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00637): [0m
[37m INFO:2025-09-04 16:20:00,822 - (ensemble_manager.py:326) -    GP: 0.1285 → 0.1252 (MAE:42.6994, 数据:50) [0m
[37m INFO:2025-09-04 16:20:00,822 - (ensemble_manager.py:326) -    RBF: 0.1168 → 0.1137 (MAE:44.4764, 数据:50) [0m
[37m INFO:2025-09-04 16:20:00,822 - (ensemble_manager.py:326) -    TABPFN: 0.7547 ↑ 0.7611 (MAE:18.5247, 数据:50) [0m
[37m INFO:2025-09-04 16:20:00,823 - (bounce.py:864) - 🚀 Iteration 55: No improvement. Best function value -192.392 [全局模型策略] [0m
[37m INFO:2025-09-04 16:20:00,823 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 56 [0m
[37m INFO:2025-09-04 16:20:00,824 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 56 [0m
[37m INFO:2025-09-04 16:20:00,945 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 56, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:20:00,946 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 56 [0m
[37m INFO:2025-09-04 16:20:00,948 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 56 [0m
[37m INFO:2025-09-04 16:20:01,054 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 56 [0m
[37m WARNING:2025-09-04 16:20:01,457 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-129.799020 [0m
[37m WARNING:2025-09-04 16:20:01,460 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-129.799020 [0m
[37m INFO:2025-09-04 16:20:10,549 - (genetic_algorithm.py:367) - 集成代理模型评估了159个GA个体，使用权重: {'gp': 0.12518046278550943, 'rbf': 0.11373015006617265, 'tabpfn': 0.761089387148318} [0m
[37m INFO:2025-09-04 16:20:10,561 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:20:10,562 - (ensemble_manager.py:718) - 📊 输入训练数据: 56个样本 (上次: 56) [0m
[37m WARNING:2025-09-04 16:20:10,567 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-129.474819 [0m
[37m WARNING:2025-09-04 16:20:10,569 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-129.474819 [0m
[37m WARNING:2025-09-04 16:20:10,570 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-129.799020 [0m
[37m WARNING:2025-09-04 16:20:10,574 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-129.799020 [0m
[37m INFO:2025-09-04 16:20:16,075 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:20:16,076 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.12518046278550943, 'rbf': 0.11373015006617265, 'tabpfn': 0.761089387148318} [0m
[37m INFO:2025-09-04 16:20:16,076 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-129.4748, -129.4748] → 归一化[0.0000, 1.0000], 均值-129.4748→0.7892 [0m
[37m INFO:2025-09-04 16:20:16,076 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-129.7990, -129.7990] → 归一化[0.5000, 0.5000], 均值-129.7990→0.5000 [0m
[37m INFO:2025-09-04 16:20:16,077 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-173.2397, -164.2266] → 归一化[0.0000, 1.0000], 均值-165.4098→0.8687 [0m
[37m INFO:2025-09-04 16:20:16,081 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第31个候选点 tensor([ 1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:20:16,081 - (ensemble_manager.py:848) -    📊 GP: 原始分-129.4748 → 归一化0.9981, 权重0.1252, 加权分0.1249 [0m
[37m INFO:2025-09-04 16:20:16,081 - (ensemble_manager.py:848) -    📊 RBF: 原始分-129.7990 → 归一化0.5000, 权重0.1137, 加权分0.0569 [0m
[37m INFO:2025-09-04 16:20:16,082 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-164.3273 → 归一化0.9888, 权重0.7611, 加权分0.7526 [0m
[37m INFO:2025-09-04 16:20:16,082 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:20:19,850 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-34.410035289240014, -88.75929256544865), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:19,851 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:19,851 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-34.2000468148262, -88.75929256544865), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:19,852 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:19,852 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-43.02037942615203, -88.75929256544865), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:19,852 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:19,852 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:20:19,853 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:20:19,853 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:19,853 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:19,854 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:19,854 - (ensemble_manager.py:227) - 🔄 检测到性能差异(26.6163)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:20:19,855 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01726): [0m
[37m INFO:2025-09-04 16:20:19,855 - (ensemble_manager.py:326) -    GP: 0.1252 ↓ 0.1163 (MAE:42.5915, 数据:50) [0m
[37m INFO:2025-09-04 16:20:19,855 - (ensemble_manager.py:326) -    RBF: 0.1137 ↓ 0.1053 (MAE:44.3578, 数据:50) [0m
[37m INFO:2025-09-04 16:20:19,855 - (ensemble_manager.py:326) -    TABPFN: 0.7611 ↗ 0.7783 (MAE:17.7416, 数据:50) [0m
[37m INFO:2025-09-04 16:20:19,856 - (bounce.py:864) - 🚀 Iteration 56: No improvement. Best function value -192.392 [全局模型策略] [0m
[37m INFO:2025-09-04 16:20:19,857 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 57 [0m
[37m INFO:2025-09-04 16:20:19,857 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 57 [0m
[37m INFO:2025-09-04 16:20:19,989 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 57, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:20:19,990 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 57 [0m
[37m INFO:2025-09-04 16:20:19,991 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 57 [0m
[37m INFO:2025-09-04 16:20:20,090 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 57 [0m
[37m WARNING:2025-09-04 16:20:20,457 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-130.652166 [0m
[37m WARNING:2025-09-04 16:20:20,461 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-130.652166 [0m
[37m INFO:2025-09-04 16:20:28,535 - (genetic_algorithm.py:367) - 集成代理模型评估了150个GA个体，使用权重: {'gp': 0.11633925240046529, 'rbf': 0.10531441494292515, 'tabpfn': 0.7783463326566097} [0m
[37m INFO:2025-09-04 16:20:28,546 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:20:28,547 - (ensemble_manager.py:718) - 📊 输入训练数据: 57个样本 (上次: 57) [0m
[37m WARNING:2025-09-04 16:20:28,551 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-130.498330 [0m
[37m WARNING:2025-09-04 16:20:28,552 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-130.498330 [0m
[37m WARNING:2025-09-04 16:20:28,554 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-130.652166 [0m
[37m WARNING:2025-09-04 16:20:28,555 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-130.652166 [0m
[37m INFO:2025-09-04 16:20:33,751 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:20:33,751 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.11633925240046529, 'rbf': 0.10531441494292515, 'tabpfn': 0.7783463326566097} [0m
[37m INFO:2025-09-04 16:20:33,752 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-130.4983, -130.4983] → 归一化[0.0000, 1.0000], 均值-130.4983→0.8080 [0m
[37m INFO:2025-09-04 16:20:33,753 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-130.6522, -130.6522] → 归一化[0.5000, 0.5000], 均值-130.6522→0.5000 [0m
[37m INFO:2025-09-04 16:20:33,753 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-174.3584, -169.2679] → 归一化[0.0000, 1.0000], 均值-170.3271→0.7919 [0m
[37m INFO:2025-09-04 16:20:33,756 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第47个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:20:33,756 - (ensemble_manager.py:848) -    📊 GP: 原始分-130.4983 → 归一化0.9569, 权重0.1163, 加权分0.1113 [0m
[37m INFO:2025-09-04 16:20:33,757 - (ensemble_manager.py:848) -    📊 RBF: 原始分-130.6522 → 归一化0.5000, 权重0.1053, 加权分0.0527 [0m
[37m INFO:2025-09-04 16:20:33,757 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-169.2679 → 归一化1.0000, 权重0.7783, 加权分0.7783 [0m
[37m INFO:2025-09-04 16:20:33,758 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:20:38,065 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-43.46816634518385, -102.64102921221583), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:38,065 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:38,065 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-41.99422477920084, -102.64102921221583), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:38,066 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:38,066 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-61.95086600277837, -102.64102921221583), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:38,066 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:38,066 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:20:38,067 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:20:38,067 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:38,068 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:38,068 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:38,069 - (ensemble_manager.py:227) - 🔄 检测到性能差异(27.0591)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:20:38,069 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01401): [0m
[37m INFO:2025-09-04 16:20:38,069 - (ensemble_manager.py:326) -    GP: 0.1163 ↓ 0.1079 (MAE:42.2769, 数据:50) [0m
[37m INFO:2025-09-04 16:20:38,069 - (ensemble_manager.py:326) -    RBF: 0.1053 ↓ 0.0998 (MAE:44.0107, 数据:50) [0m
[37m INFO:2025-09-04 16:20:38,070 - (ensemble_manager.py:326) -    TABPFN: 0.7783 ↗ 0.7924 (MAE:16.9515, 数据:50) [0m
[37m INFO:2025-09-04 16:20:38,070 - (bounce.py:864) - 🚀 Iteration 57: No improvement. Best function value -192.392 [全局模型策略] [0m
[37m WARNING:2025-09-04 16:20:38,070 - (bounce.py:876) - 🔄 策略切换: 全局模型策略 -> 历史最优策略 (停滞3步) [0m
[37m INFO:2025-09-04 16:20:38,071 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 58 [0m
[37m INFO:2025-09-04 16:20:38,072 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 58 [0m
[37m INFO:2025-09-04 16:20:38,204 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 58, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:20:38,205 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 58 [0m
[37m INFO:2025-09-04 16:20:38,207 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 58 [0m
[37m INFO:2025-09-04 16:20:38,304 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 58 [0m
[37m WARNING:2025-09-04 16:20:38,676 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-131.398523 [0m
[37m WARNING:2025-09-04 16:20:38,680 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-131.398523 [0m
[37m INFO:2025-09-04 16:20:46,304 - (genetic_algorithm.py:367) - 集成代理模型评估了146个GA个体，使用权重: {'gp': 0.10788061544184317, 'rbf': 0.09976292118380782, 'tabpfn': 0.7923564633743491} [0m
[37m INFO:2025-09-04 16:20:46,314 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:20:49,929 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-51.921678519675375, -91.51928438770722), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:49,929 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:49,929 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-49.57507533332772, -91.51928438770722), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:49,930 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:49,930 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-70.07704407221746, -91.51928438770722), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:20:49,930 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:20:49,930 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:20:49,931 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:20:49,931 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:49,932 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:49,932 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:20:49,932 - (ensemble_manager.py:227) - 🔄 检测到性能差异(27.5203)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:20:49,933 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00431): [0m
[37m INFO:2025-09-04 16:20:49,933 - (ensemble_manager.py:326) -    GP: 0.1079 → 0.1039 (MAE:42.6402, 数据:50) [0m
[37m INFO:2025-09-04 16:20:49,933 - (ensemble_manager.py:326) -    RBF: 0.0998 → 0.0995 (MAE:44.3264, 数据:50) [0m
[37m INFO:2025-09-04 16:20:49,934 - (ensemble_manager.py:326) -    TABPFN: 0.7924 → 0.7967 (MAE:16.8061, 数据:50) [0m
[37m INFO:2025-09-04 16:20:49,934 - (bounce.py:864) - 🚀 Iteration 58: No improvement. Best function value -192.392 [历史最优策略] [0m
[37m INFO:2025-09-04 16:20:49,935 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 59 [0m
[37m INFO:2025-09-04 16:20:49,936 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 59 [0m
[37m INFO:2025-09-04 16:20:50,094 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 59, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:20:50,095 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 59 [0m
[37m INFO:2025-09-04 16:20:50,096 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 59 [0m
[37m INFO:2025-09-04 16:20:50,199 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 59 [0m
[37m WARNING:2025-09-04 16:20:50,549 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-132.377039 [0m
[37m WARNING:2025-09-04 16:20:50,553 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-132.377039 [0m
[37m INFO:2025-09-04 16:20:58,463 - (genetic_algorithm.py:367) - 集成代理模型评估了154个GA个体，使用权重: {'gp': 0.10388188024740849, 'rbf': 0.09945135243098066, 'tabpfn': 0.7966667673216108} [0m
[37m INFO:2025-09-04 16:20:58,476 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:21:02,074 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-56.87103993217658, -113.4561082786957), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:02,075 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:02,075 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-54.23554300603655, -113.4561082786957), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:02,075 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:02,076 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-79.54076776155412, -113.4561082786957), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:02,076 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:02,076 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:21:02,077 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:21:02,077 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:02,077 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:02,078 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:02,078 - (ensemble_manager.py:227) - 🔄 检测到性能差异(27.8908)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:21:02,079 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00557): [0m
[37m INFO:2025-09-04 16:21:02,079 - (ensemble_manager.py:326) -    GP: 0.1039 ↓ 0.0989 (MAE:42.6428, 数据:50) [0m
[37m INFO:2025-09-04 16:21:02,079 - (ensemble_manager.py:326) -    RBF: 0.0995 → 0.0989 (MAE:44.2710, 数据:50) [0m
[37m INFO:2025-09-04 16:21:02,079 - (ensemble_manager.py:326) -    TABPFN: 0.7967 ↑ 0.8022 (MAE:16.3802, 数据:50) [0m
[37m INFO:2025-09-04 16:21:02,080 - (bounce.py:864) - 🚀 Iteration 59: No improvement. Best function value -192.392 [历史最优策略] [0m
[37m INFO:2025-09-04 16:21:02,081 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 60 [0m
[37m INFO:2025-09-04 16:21:02,081 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 60 [0m
[37m INFO:2025-09-04 16:21:02,185 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 60, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:21:02,186 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 60 [0m
[37m INFO:2025-09-04 16:21:02,188 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 60 [0m
[37m INFO:2025-09-04 16:21:02,296 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 60 [0m
[37m WARNING:2025-09-04 16:21:02,673 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-133.317827 [0m
[37m WARNING:2025-09-04 16:21:02,677 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-133.317827 [0m
[37m INFO:2025-09-04 16:21:10,595 - (genetic_algorithm.py:367) - 集成代理模型评估了155个GA个体，使用权重: {'gp': 0.09888028727494308, 'rbf': 0.09888028727494308, 'tabpfn': 0.8022394254501137} [0m
[37m INFO:2025-09-04 16:21:10,605 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-192.392) [0m
[37m INFO:2025-09-04 16:21:14,360 - (ensemble_manager.py:80) - 📈 GP滑动窗口运作: 移除旧数据(-63.15829989220301, -102.94769497024456), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:14,360 - (ensemble_manager.py:95) - 🎯 GP窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:14,360 - (ensemble_manager.py:80) - 📈 RBF滑动窗口运作: 移除旧数据(-60.15759953330246, -102.94769497024456), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:14,361 - (ensemble_manager.py:95) - 🎯 RBF窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:14,361 - (ensemble_manager.py:80) - 📈 TABPFN滑动窗口运作: 移除旧数据(-85.85186821765268, -102.94769497024456), 50→51→50 (窗口大小:50) [0m
[37m INFO:2025-09-04 16:21:14,361 - (ensemble_manager.py:95) - 🎯 TABPFN窗口已满: 50条记录，后续将启动滑动窗口机制 [0m
[37m INFO:2025-09-04 16:21:14,362 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:21:14,363 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:21:14,363 - (ensemble_manager.py:128) -    🟢 GP: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:14,363 - (ensemble_manager.py:128) -    🟢 RBF: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:14,363 - (ensemble_manager.py:128) -    🟢 TABPFN: 50/50 (100.0%) [0m
[37m INFO:2025-09-04 16:21:14,364 - (ensemble_manager.py:227) - 🔄 检测到性能差异(28.1680)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:21:14,364 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00023): [0m
[37m INFO:2025-09-04 16:21:14,364 - (ensemble_manager.py:326) -    GP: 0.0989 → 0.0988 (MAE:43.1038, 数据:50) [0m
[37m INFO:2025-09-04 16:21:14,365 - (ensemble_manager.py:326) -    RBF: 0.0989 → 0.0988 (MAE:44.6618, 数据:50) [0m
[37m INFO:2025-09-04 16:21:14,365 - (ensemble_manager.py:326) -    TABPFN: 0.8022 → 0.8025 (MAE:16.4939, 数据:50) [0m
[37m INFO:2025-09-04 16:21:14,365 - (bounce.py:857) - ✨ Iteration 60: [92mNew incumbent function value -195.653[0m [历史最优策略有效] [0m
[37m INFO:2025-09-04 16:21:14,366 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 61 [0m
[37m INFO:2025-09-04 16:21:14,367 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 61 [0m
[37m INFO:2025-09-04 16:21:14,461 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 61, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:21:14,463 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 61 [0m
[37m INFO:2025-09-04 16:21:14,464 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 61 [0m
[37m INFO:2025-09-04 16:21:14,583 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 61 [0m
[37m WARNING:2025-09-04 16:21:14,964 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-134.339711 [0m
[37m WARNING:2025-09-04 16:21:14,978 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-134.339711 [0m
[37m INFO:2025-09-04 16:21:22,500 - (genetic_algorithm.py:367) - 集成代理模型评估了152个GA个体，使用权重: {'gp': 0.09876619090392574, 'rbf': 0.09876619090392574, 'tabpfn': 0.8024676181921484} [0m
[37m INFO:2025-09-04 16:21:22,510 - (bounce.py:558) - 🏆 [历史最优策略] 使用历史最优值作为TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... (fx=-195.653) [0m
