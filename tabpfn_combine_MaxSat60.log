nohup: ignoring input
[37m INFO:2025-09-04 16:11:54,341 - (main.py:25) - [37m
    	    ____                            
    	   / __ )____  __  ______  ________
    	  / __  / __ \/ / / / __ \/ ___/ _ \
    	 / /_/ / /_/ / /_/ / / / / /__/  __/
    	/_____/\____/\__,_/_/ /_/\___/\___/
    
    [ Bounce: Reliable High-Dimensional Bayesian Optimization     ] 
    [ Algorithm for Combinatorial and Mixed Spaces ]
    [0m [0m
[37m INFO:2025-09-04 16:11:54,345 - (main.py:69) - 第1次实验开始 [0m
[37m INFO:2025-09-04 16:11:54,369 - (bounce.py:231) - 初始维度5维，算法目标维度60维 [0m
[37m INFO:2025-09-04 16:11:54,370 - (bounce.py:232) - 🤖 <PERSON><PERSON><PERSON> will split at most 4 times.  每次分裂1个箱子 [0m
[37m INFO:2025-09-04 16:11:54,370 - (bounce.py:235) - 🤖 初始5维，有1 次评估预算  [0m
[37m INFO:2025-09-04 16:11:54,371 - (bounce.py:280) - 每一个可能维度以及评估预算：{5: 1, 10: 3, 20: 6, 40: 12, 80: 200}；以及总预算为50次 [0m
[37m INFO:2025-09-04 16:11:54,371 - (bounce.py:294) - 🔄 策略切换机制已启用，切换阈值: 3步 [0m
[37m INFO:2025-09-04 16:11:54,372 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,372 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,372 - (gp_surrogate.py:53) - GP全局代理模型初始化完成，设备: cpu, 参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True} [0m
[37m INFO:2025-09-04 16:11:54,372 - (surrogate_manager.py:60) - 创建GP代理模型，参数: {} [0m
[37m INFO:2025-09-04 16:11:54,373 - (ensemble_manager.py:539) - ✅ GP模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,373 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,373 - (rbf_surrogate.py:47) - RBF全局代理模型初始化完成，设备: cpu, 核函数: gaussian, epsilon: 1.0 [0m
[37m INFO:2025-09-04 16:11:54,373 - (surrogate_manager.py:66) - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0 [0m
[37m INFO:2025-09-04 16:11:54,374 - (ensemble_manager.py:539) - ✅ RBF模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,374 - (device_manager.py:47) - 设备管理器初始化: cpu, CUDA可用: False [0m
[37m INFO:2025-09-04 16:11:54,374 - (tabpfn_surrogate.py:39) - TabPFN初始化成功，设备: cpu [0m
[37m INFO:2025-09-04 16:11:54,374 - (tabpfn_surrogate.py:73) - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 60, 类别变量: 0 [0m
[37m INFO:2025-09-04 16:11:54,374 - (surrogate_manager.py:55) - 创建TabPFN代理模型，n_bins: 5 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:539) - ✅ TABPFN模型初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:40) - 权重管理器初始化完成，模型: ['gp', 'rbf', 'tabpfn'] [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:41) - 初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:561) - 🌟 集成代理模型管理器初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:562) -    设备: cpu [0m
[37m INFO:2025-09-04 16:11:54,375 - (ensemble_manager.py:563) -    支持模型: ['gp', 'rbf', 'tabpfn'] [0m
[37m INFO:2025-09-04 16:11:54,376 - (ensemble_manager.py:564) -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:54,376 - (ga_tabpfn_integration.py:65) - 🌟 集成代理模型系统已启用 [0m
[37m INFO:2025-09-04 16:11:54,376 - (ga_tabpfn_integration.py:98) - GA-集成代理模型集成系统初始化完成 [0m
[37m INFO:2025-09-04 16:11:54,391 - (ga_tabpfn_integration.py:134) - 🎆 首次训练集成代理模型，数据量: 5 [0m
[37m INFO:2025-09-04 16:11:54,612 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 5, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:11:54,612 - (ensemble_manager.py:584) - GP模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,614 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 5 [0m
[37m INFO:2025-09-04 16:11:54,614 - (ensemble_manager.py:584) - RBF模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,777 - (tabpfn_surrogate.py:305) - TabPFN模型训练完成，训练样本数: 5, 类别数: 4 [0m
[37m INFO:2025-09-04 16:11:54,778 - (ensemble_manager.py:584) - TABPFN模型训练完成 [0m
[37m INFO:2025-09-04 16:11:54,778 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 5) [0m
[37m INFO:2025-09-04 16:11:54,779 - (genetic_algorithm.py:186) - 动态种群大小: 65 (目标维度: 5) [0m
[37m INFO:2025-09-04 16:11:54,782 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 65 [0m
[37m INFO:2025-09-04 16:11:58,447 - (genetic_algorithm.py:367) - 集成代理模型评估了65个GA个体，使用权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:11:58,451 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:11:58,452 - (ensemble_manager.py:718) - 📊 输入训练数据: 5个样本 (上次: 0) [0m
[37m INFO:2025-09-04 16:11:58,452 - (ensemble_manager.py:727) - 🔄 新增5个珍贵样本，立即更新模型 [0m
[37m INFO:2025-09-04 16:11:58,452 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,489 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 5, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:11:58,490 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,491 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 5 [0m
[37m INFO:2025-09-04 16:11:58,606 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 5 [0m
[37m WARNING:2025-09-04 16:11:58,611 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-29.700618 [0m
[37m WARNING:2025-09-04 16:11:58,612 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-29.700618 [0m
[37m WARNING:2025-09-04 16:11:58,614 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-30.390577 [0m
[37m WARNING:2025-09-04 16:11:58,615 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-30.390577 [0m
[37m INFO:2025-09-04 16:12:01,183 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:01,183 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:01,184 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-29.7006, -29.7006] → 归一化[0.0000, 1.0000], 均值-29.7006→0.7054 [0m
[37m INFO:2025-09-04 16:12:01,184 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-30.3906, -30.3906] → 归一化[0.5000, 0.5000], 均值-30.3906→0.5000 [0m
[37m INFO:2025-09-04 16:12:01,185 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-38.4005, -30.9941] → 归一化[0.0000, 1.0000], 均值-35.9150→0.3356 [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第42个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:848) -    📊 GP: 原始分-29.7006 → 归一化1.0000, 权重0.3333, 加权分0.3333 [0m
[37m INFO:2025-09-04 16:12:01,188 - (ensemble_manager.py:848) -    📊 RBF: 原始分-30.3906 → 归一化0.5000, 权重0.3333, 加权分0.1667 [0m
[37m INFO:2025-09-04 16:12:01,189 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-33.2065 → 归一化0.7013, 权重0.3333, 加权分0.2338 [0m
[37m INFO:2025-09-04 16:12:01,190 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:02,222 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:128) -    🟡 GP: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,223 - (ensemble_manager.py:128) -    🟡 RBF: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:128) -    🟡 TABPFN: 1/50 (2.0%) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00000): [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    GP: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    RBF: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,224 - (ensemble_manager.py:326) -    TABPFN: 0.3333 → 0.3333 (MAE:0.5000, 数据:1) [0m
[37m INFO:2025-09-04 16:12:02,225 - (bounce.py:857) - ✨ Iteration 5: [92mNew incumbent function value -53.247[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:02,226 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 6 [0m
[37m INFO:2025-09-04 16:12:02,226 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,268 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 6, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:02,269 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,269 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 6 [0m
[37m INFO:2025-09-04 16:12:02,372 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 6 [0m
[37m INFO:2025-09-04 16:12:04,780 - (genetic_algorithm.py:367) - 集成代理模型评估了42个GA个体，使用权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:04,785 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:04,785 - (ensemble_manager.py:718) - 📊 输入训练数据: 6个样本 (上次: 6) [0m
[37m WARNING:2025-09-04 16:12:04,788 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-34.410035 [0m
[37m WARNING:2025-09-04 16:12:04,789 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-34.410035 [0m
[37m WARNING:2025-09-04 16:12:04,790 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-34.200047 [0m
[37m WARNING:2025-09-04 16:12:04,791 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-34.200047 [0m
[37m INFO:2025-09-04 16:12:07,428 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:07,429 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333} [0m
[37m INFO:2025-09-04 16:12:07,429 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-34.4100, -34.4100] → 归一化[0.0000, 1.0000], 均值-34.4100→0.4700 [0m
[37m INFO:2025-09-04 16:12:07,430 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-34.2000, -34.2000] → 归一化[0.5000, 0.5000], 均值-34.2000→0.5000 [0m
[37m INFO:2025-09-04 16:12:07,430 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-43.5319, -35.7559] → 归一化[0.0000, 1.0000], 均值-41.8915→0.2110 [0m
[37m INFO:2025-09-04 16:12:07,432 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第19个候选点 tensor([-1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:07,432 - (ensemble_manager.py:848) -    📊 GP: 原始分-34.4100 → 归一化1.0000, 权重0.3333, 加权分0.3333 [0m
[37m INFO:2025-09-04 16:12:07,433 - (ensemble_manager.py:848) -    📊 RBF: 原始分-34.2000 → 归一化0.5000, 权重0.3333, 加权分0.1667 [0m
[37m INFO:2025-09-04 16:12:07,433 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-36.8018 → 归一化0.8655, 权重0.3333, 加权分0.2885 [0m
[37m INFO:2025-09-04 16:12:07,435 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:08,634 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:128) -    🟡 GP: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,635 - (ensemble_manager.py:128) -    🟡 RBF: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:128) -    🟡 TABPFN: 2/50 (4.0%) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:227) - 🔄 检测到性能差异(8.0988)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.18303): [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:326) -    GP: 0.3333 ↘ 0.2409 (MAE:38.9480, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,636 - (ensemble_manager.py:326) -    RBF: 0.3333 ↘ 0.2428 (MAE:38.7080, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,637 - (ensemble_manager.py:326) -    TABPFN: 0.3333 ↗ 0.5164 (MAE:30.8492, 数据:2) [0m
[37m INFO:2025-09-04 16:12:08,637 - (bounce.py:857) - ✨ Iteration 6: [92mNew incumbent function value -88.759[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:08,637 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 7 [0m
[37m INFO:2025-09-04 16:12:08,638 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,666 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 7, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:08,667 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,668 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 7 [0m
[37m INFO:2025-09-04 16:12:08,762 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 7 [0m
[37m INFO:2025-09-04 16:12:11,054 - (genetic_algorithm.py:367) - 集成代理模型评估了45个GA个体，使用权重: {'gp': 0.2408768477894236, 'rbf': 0.24275800497601976, 'tabpfn': 0.5163651472345567} [0m
[37m INFO:2025-09-04 16:12:11,057 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:11,058 - (ensemble_manager.py:718) - 📊 输入训练数据: 7个样本 (上次: 7) [0m
[37m WARNING:2025-09-04 16:12:11,062 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-43.468166 [0m
[37m WARNING:2025-09-04 16:12:11,062 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-43.468166 [0m
[37m WARNING:2025-09-04 16:12:11,063 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-41.994225 [0m
[37m WARNING:2025-09-04 16:12:11,064 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-41.994225 [0m
[37m INFO:2025-09-04 16:12:13,714 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2408768477894236, 'rbf': 0.24275800497601976, 'tabpfn': 0.5163651472345567} [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-43.4682, -43.4682] → 归一化[0.0000, 1.0000], 均值-43.4682→0.6167 [0m
[37m INFO:2025-09-04 16:12:13,715 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-41.9942, -41.9942] → 归一化[0.5000, 0.5000], 均值-41.9942→0.5000 [0m
[37m INFO:2025-09-04 16:12:13,716 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-62.9084, -48.9668] → 归一化[0.0000, 1.0000], 均值-59.6589→0.2331 [0m
[37m INFO:2025-09-04 16:12:13,718 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第32个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 GP: 原始分-43.4682 → 归一化0.6667, 权重0.2409, 加权分0.1606 [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 RBF: 原始分-41.9942 → 归一化0.5000, 权重0.2428, 加权分0.1214 [0m
[37m INFO:2025-09-04 16:12:13,719 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-48.9668 → 归一化1.0000, 权重0.5164, 加权分0.5164 [0m
[37m INFO:2025-09-04 16:12:13,720 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:14,932 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:14,932 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 GP: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 RBF: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:128) -    🟡 TABPFN: 3/50 (6.0%) [0m
[37m INFO:2025-09-04 16:12:14,933 - (ensemble_manager.py:227) - 🔄 检测到性能差异(11.8914)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02117): [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    GP: 0.2409 ↓ 0.2324 (MAE:45.6896, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    RBF: 0.2428 ↘ 0.2301 (MAE:46.0210, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,934 - (ensemble_manager.py:326) -    TABPFN: 0.5164 ↗ 0.5375 (MAE:34.1295, 数据:3) [0m
[37m INFO:2025-09-04 16:12:14,935 - (bounce.py:857) - ✨ Iteration 7: [92mNew incumbent function value -102.641[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:14,935 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 8 [0m
[37m INFO:2025-09-04 16:12:14,936 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 8 [0m
[37m INFO:2025-09-04 16:12:14,963 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 8, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:14,964 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 8 [0m
[37m INFO:2025-09-04 16:12:14,965 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 8 [0m
[37m INFO:2025-09-04 16:12:15,060 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 8 [0m
[37m INFO:2025-09-04 16:12:17,934 - (genetic_algorithm.py:367) - 集成代理模型评估了46个GA个体，使用权重: {'gp': 0.2323568274215991, 'rbf': 0.23011205907289042, 'tabpfn': 0.5375311135055104} [0m
[37m INFO:2025-09-04 16:12:17,937 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:17,937 - (ensemble_manager.py:718) - 📊 输入训练数据: 8个样本 (上次: 8) [0m
[37m WARNING:2025-09-04 16:12:17,942 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-51.921679 [0m
[37m WARNING:2025-09-04 16:12:17,943 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-51.921679 [0m
[37m WARNING:2025-09-04 16:12:17,944 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-49.575075 [0m
[37m WARNING:2025-09-04 16:12:17,945 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-49.575075 [0m
[37m INFO:2025-09-04 16:12:20,871 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:20,872 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2323568274215991, 'rbf': 0.23011205907289042, 'tabpfn': 0.5375311135055104} [0m
[37m INFO:2025-09-04 16:12:20,872 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-51.9217, -51.9217] → 归一化[0.0000, 1.0000], 均值-51.9217→0.5914 [0m
[37m INFO:2025-09-04 16:12:20,873 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-49.5751, -49.5751] → 归一化[0.5000, 0.5000], 均值-49.5751→0.5000 [0m
[37m INFO:2025-09-04 16:12:20,873 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-76.0021, -60.7574] → 归一化[0.0000, 1.0000], 均值-71.8186→0.2744 [0m
[37m INFO:2025-09-04 16:12:20,874 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第22个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:20,874 - (ensemble_manager.py:848) -    📊 GP: 原始分-51.9217 → 归一化0.8483, 权重0.2324, 加权分0.1971 [0m
[37m INFO:2025-09-04 16:12:20,875 - (ensemble_manager.py:848) -    📊 RBF: 原始分-49.5751 → 归一化0.5000, 权重0.2301, 加权分0.1151 [0m
[37m INFO:2025-09-04 16:12:20,875 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-60.7574 → 归一化1.0000, 权重0.5375, 加权分0.5375 [0m
[37m INFO:2025-09-04 16:12:20,876 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:21,998 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:21,998 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 GP: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 RBF: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:21,999 - (ensemble_manager.py:128) -    🟡 TABPFN: 4/50 (8.0%) [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:227) - 🔄 检测到性能差异(14.0441)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02389): [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:326) -    GP: 0.2324 ↘ 0.2222 (MAE:44.1666, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,000 - (ensemble_manager.py:326) -    RBF: 0.2301 ↘ 0.2163 (MAE:45.0018, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,001 - (ensemble_manager.py:326) -    TABPFN: 0.5375 ↗ 0.5614 (MAE:30.9577, 数据:4) [0m
[37m INFO:2025-09-04 16:12:22,001 - (bounce.py:864) - 🚀 Iteration 8: No improvement. Best function value -102.641 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:22,001 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:12:22,002 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 9 [0m
[37m INFO:2025-09-04 16:12:22,003 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,031 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 9, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:22,032 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,033 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,133 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 9 [0m
[37m INFO:2025-09-04 16:12:22,134 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 10) [0m
[37m INFO:2025-09-04 16:12:22,134 - (genetic_algorithm.py:186) - 动态种群大小: 80 (目标维度: 10) [0m
[37m INFO:2025-09-04 16:12:22,139 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 80 [0m
[37m WARNING:2025-09-04 16:12:22,253 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m WARNING:2025-09-04 16:12:22,254 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m INFO:2025-09-04 16:12:25,971 - (genetic_algorithm.py:367) - 集成代理模型评估了80个GA个体，使用权重: {'gp': 0.22224779123477634, 'rbf': 0.21633237875593175, 'tabpfn': 0.561419830009292} [0m
[37m INFO:2025-09-04 16:12:25,974 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:25,974 - (ensemble_manager.py:718) - 📊 输入训练数据: 9个样本 (上次: 9) [0m
[37m WARNING:2025-09-04 16:12:25,978 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-56.871040 [0m
[37m WARNING:2025-09-04 16:12:25,979 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-56.871040 [0m
[37m WARNING:2025-09-04 16:12:25,980 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m WARNING:2025-09-04 16:12:25,981 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-54.235543 [0m
[37m INFO:2025-09-04 16:12:28,549 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:28,550 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.22224779123477634, 'rbf': 0.21633237875593175, 'tabpfn': 0.561419830009292} [0m
[37m INFO:2025-09-04 16:12:28,550 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-56.8710, -56.8710] → 归一化[0.0000, 1.0000], 均值-56.8710→0.9786 [0m
[37m INFO:2025-09-04 16:12:28,551 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-54.2355, -54.2355] → 归一化[0.5000, 0.5000], 均值-54.2355→0.5000 [0m
[37m INFO:2025-09-04 16:12:28,551 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-79.6129, -42.4961] → 归一化[0.0000, 1.0000], 均值-62.2404→0.4681 [0m
[37m INFO:2025-09-04 16:12:28,552 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第25个候选点 tensor([-1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:28,552 - (ensemble_manager.py:848) -    📊 GP: 原始分-56.8710 → 归一化1.0000, 权重0.2222, 加权分0.2222 [0m
[37m INFO:2025-09-04 16:12:28,553 - (ensemble_manager.py:848) -    📊 RBF: 原始分-54.2355 → 归一化0.5000, 权重0.2163, 加权分0.1082 [0m
[37m INFO:2025-09-04 16:12:28,553 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-42.8675 → 归一化0.9900, 权重0.5614, 加权分0.5558 [0m
[37m INFO:2025-09-04 16:12:28,554 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1.,  1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:128) -    🟡 GP: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,741 - (ensemble_manager.py:128) -    🟡 RBF: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:128) -    🟡 TABPFN: 5/50 (10.0%) [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.2963)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01407): [0m
[37m INFO:2025-09-04 16:12:29,742 - (ensemble_manager.py:326) -    GP: 0.2222 ↓ 0.2163 (MAE:46.6503, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (ensemble_manager.py:326) -    RBF: 0.2163 ↓ 0.2082 (MAE:47.8455, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (ensemble_manager.py:326) -    TABPFN: 0.5614 ↗ 0.5755 (MAE:31.5492, 数据:5) [0m
[37m INFO:2025-09-04 16:12:29,743 - (bounce.py:857) - ✨ Iteration 9: [92mNew incumbent function value -113.456[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:29,744 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 10 [0m
[37m INFO:2025-09-04 16:12:29,744 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,779 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 10, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:29,780 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,781 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 10 [0m
[37m INFO:2025-09-04 16:12:29,872 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 10 [0m
[37m WARNING:2025-09-04 16:12:29,955 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m WARNING:2025-09-04 16:12:29,956 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m INFO:2025-09-04 16:12:32,757 - (genetic_algorithm.py:367) - 集成代理模型评估了58个GA个体，使用权重: {'gp': 0.21627751846143584, 'rbf': 0.20823729980724226, 'tabpfn': 0.5754851817313219} [0m
[37m INFO:2025-09-04 16:12:32,760 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:32,760 - (ensemble_manager.py:718) - 📊 输入训练数据: 10个样本 (上次: 10) [0m
[37m WARNING:2025-09-04 16:12:32,764 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-63.158300 [0m
[37m WARNING:2025-09-04 16:12:32,765 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-63.158300 [0m
[37m WARNING:2025-09-04 16:12:32,766 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m WARNING:2025-09-04 16:12:32,767 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-60.157600 [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21627751846143584, 'rbf': 0.20823729980724226, 'tabpfn': 0.5754851817313219} [0m
[37m INFO:2025-09-04 16:12:35,652 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-63.1583, -63.1583] → 归一化[0.0000, 1.0000], 均值-63.1583→0.9800 [0m
[37m INFO:2025-09-04 16:12:35,653 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-60.1576, -60.1576] → 归一化[0.5000, 0.5000], 均值-60.1576→0.5000 [0m
[37m INFO:2025-09-04 16:12:35,653 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-87.7971, -58.8256] → 归一化[0.0000, 1.0000], 均值-75.5795→0.4217 [0m
[37m INFO:2025-09-04 16:12:35,654 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第34个候选点 tensor([-1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 GP: 原始分-63.1583 → 归一化1.0000, 权重0.2163, 加权分0.2163 [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 RBF: 原始分-60.1576 → 归一化0.5000, 权重0.2082, 加权分0.1041 [0m
[37m INFO:2025-09-04 16:12:35,655 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-59.8315 → 归一化0.9653, 权重0.5755, 加权分0.5555 [0m
[37m INFO:2025-09-04 16:12:35,656 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:36,803 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 GP: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 RBF: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,804 - (ensemble_manager.py:128) -    🟡 TABPFN: 6/50 (12.0%) [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.8626)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.02172): [0m
[37m INFO:2025-09-04 16:12:36,805 - (ensemble_manager.py:326) -    GP: 0.2163 ↓ 0.2066 (MAE:45.5068, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (ensemble_manager.py:326) -    RBF: 0.2082 ↘ 0.1962 (MAE:47.0030, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (ensemble_manager.py:326) -    TABPFN: 0.5755 ↗ 0.5972 (MAE:29.1403, 数据:6) [0m
[37m INFO:2025-09-04 16:12:36,806 - (bounce.py:864) - 🚀 Iteration 10: No improvement. Best function value -113.456 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:36,807 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 11 [0m
[37m INFO:2025-09-04 16:12:36,807 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,841 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 11, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:36,841 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,842 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 11 [0m
[37m INFO:2025-09-04 16:12:36,934 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 11 [0m
[37m WARNING:2025-09-04 16:12:37,000 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m WARNING:2025-09-04 16:12:37,002 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m INFO:2025-09-04 16:12:39,414 - (genetic_algorithm.py:367) - 集成代理模型评估了45个GA个体，使用权重: {'gp': 0.20656575978112623, 'rbf': 0.196227876571623, 'tabpfn': 0.5972063636472509} [0m
[37m INFO:2025-09-04 16:12:39,421 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:39,421 - (ensemble_manager.py:718) - 📊 输入训练数据: 11个样本 (上次: 11) [0m
[37m WARNING:2025-09-04 16:12:39,426 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-67.137049 [0m
[37m WARNING:2025-09-04 16:12:39,428 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-67.137049 [0m
[37m WARNING:2025-09-04 16:12:39,429 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m WARNING:2025-09-04 16:12:39,431 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-64.047608 [0m
[37m INFO:2025-09-04 16:12:42,243 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:42,243 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.20656575978112623, 'rbf': 0.196227876571623, 'tabpfn': 0.5972063636472509} [0m
[37m INFO:2025-09-04 16:12:42,244 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-67.1370, -67.1370] → 归一化[0.0000, 1.0000], 均值-67.1370→0.9791 [0m
[37m INFO:2025-09-04 16:12:42,247 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-64.0476, -64.0476] → 归一化[0.5000, 0.5000], 均值-64.0476→0.5000 [0m
[37m INFO:2025-09-04 16:12:42,247 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-91.2199, -73.8646] → 归一化[0.0000, 1.0000], 均值-87.9948→0.1858 [0m
[37m INFO:2025-09-04 16:12:42,250 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第46个候选点 tensor([ 1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 GP: 原始分-67.1370 → 归一化1.0000, 权重0.2066, 加权分0.2066 [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 RBF: 原始分-64.0476 → 归一化0.5000, 权重0.1962, 加权分0.0981 [0m
[37m INFO:2025-09-04 16:12:42,251 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-74.8786 → 归一化0.9416, 权重0.5972, 加权分0.5623 [0m
[37m INFO:2025-09-04 16:12:42,252 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:43,452 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:43,452 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 GP: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 RBF: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,453 - (ensemble_manager.py:128) -    🟡 TABPFN: 7/50 (14.0%) [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.8907)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01367): [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:326) -    GP: 0.2066 ↑ 0.2142 (MAE:45.3600, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,454 - (ensemble_manager.py:326) -    RBF: 0.1962 ↑ 0.2023 (MAE:47.0837, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,455 - (ensemble_manager.py:326) -    TABPFN: 0.5972 ↘ 0.5835 (MAE:30.1930, 数据:7) [0m
[37m INFO:2025-09-04 16:12:43,455 - (bounce.py:864) - 🚀 Iteration 11: No improvement. Best function value -113.456 [全局模型策略] [0m
[37m INFO:2025-09-04 16:12:43,456 - (bounce.py:928) - ✂️ Splitting trust region [0m
[37m INFO:2025-09-04 16:12:43,457 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 12 [0m
[37m INFO:2025-09-04 16:12:43,458 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,491 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 12, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:43,492 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,493 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,588 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 12 [0m
[37m INFO:2025-09-04 16:12:43,589 - (ga_tabpfn_integration.py:150) - GA维度变化或未初始化，重新初始化GA (目标维度: 20) [0m
[37m INFO:2025-09-04 16:12:43,589 - (genetic_algorithm.py:186) - 动态种群大小: 110 (目标维度: 20) [0m
[37m INFO:2025-09-04 16:12:43,600 - (genetic_algorithm.py:227) - 初始化种群完成，种群大小: 110 [0m
[37m WARNING:2025-09-04 16:12:43,770 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:43,772 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:43,774 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m WARNING:2025-09-04 16:12:43,775 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m INFO:2025-09-04 16:12:48,178 - (genetic_algorithm.py:367) - 集成代理模型评估了110个GA个体，使用权重: {'gp': 0.21417205849408769, 'rbf': 0.20228727868650243, 'tabpfn': 0.58354066281941} [0m
[37m INFO:2025-09-04 16:12:48,183 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:48,183 - (ensemble_manager.py:718) - 📊 输入训练数据: 12个样本 (上次: 12) [0m
[37m WARNING:2025-09-04 16:12:48,190 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:48,191 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-71.180210 [0m
[37m WARNING:2025-09-04 16:12:48,192 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m WARNING:2025-09-04 16:12:48,193 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-68.011650 [0m
[37m INFO:2025-09-04 16:12:51,090 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:51,091 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21417205849408769, 'rbf': 0.20228727868650243, 'tabpfn': 0.58354066281941} [0m
[37m INFO:2025-09-04 16:12:51,091 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-71.1802, -71.1802] → 归一化[0.0000, 1.0000], 均值-71.1802→0.9207 [0m
[37m INFO:2025-09-04 16:12:51,092 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-68.0117, -68.0117] → 归一化[0.5000, 0.5000], 均值-68.0117→0.5000 [0m
[37m INFO:2025-09-04 16:12:51,092 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-90.9945, -53.7860] → 归一化[0.0000, 1.0000], 均值-75.4612→0.4175 [0m
[37m INFO:2025-09-04 16:12:51,094 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第38个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:51,094 - (ensemble_manager.py:848) -    📊 GP: 原始分-71.1802 → 归一化0.9383, 权重0.2142, 加权分0.2010 [0m
[37m INFO:2025-09-04 16:12:51,095 - (ensemble_manager.py:848) -    📊 RBF: 原始分-68.0117 → 归一化0.5000, 权重0.2023, 加权分0.1011 [0m
[37m INFO:2025-09-04 16:12:51,095 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-64.3663 → 归一化0.7156, 权重0.5835, 加权分0.4176 [0m
[37m INFO:2025-09-04 16:12:51,096 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1.,  1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:12:52,624 - (ensemble_manager.py:128) -    🟡 GP: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:128) -    🟡 RBF: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:128) -    🟡 TABPFN: 8/50 (16.0%) [0m
[37m INFO:2025-09-04 16:12:52,625 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.5649)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00626): [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    GP: 0.2142 → 0.2116 (MAE:45.4971, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    RBF: 0.2023 → 0.1986 (MAE:47.4014, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,626 - (ensemble_manager.py:326) -    TABPFN: 0.5835 ↑ 0.5898 (MAE:29.8365, 数据:8) [0m
[37m INFO:2025-09-04 16:12:52,627 - (bounce.py:857) - ✨ Iteration 12: [92mNew incumbent function value -117.637[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:12:52,628 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 13 [0m
[37m INFO:2025-09-04 16:12:52,628 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,668 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 13, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:12:52,669 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,670 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 13 [0m
[37m INFO:2025-09-04 16:12:52,767 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 13 [0m
[37m WARNING:2025-09-04 16:12:52,892 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-75.044885 [0m
[37m WARNING:2025-09-04 16:12:52,896 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-75.044885 [0m
[37m WARNING:2025-09-04 16:12:52,897 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m WARNING:2025-09-04 16:12:52,898 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m INFO:2025-09-04 16:12:56,719 - (genetic_algorithm.py:367) - 集成代理模型评估了71个GA个体，使用权重: {'gp': 0.21164357490310365, 'rbf': 0.1985572815708856, 'tabpfn': 0.5897991435260107} [0m
[37m INFO:2025-09-04 16:12:56,724 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:12:56,724 - (ensemble_manager.py:718) - 📊 输入训练数据: 13个样本 (上次: 13) [0m
[37m WARNING:2025-09-04 16:12:56,729 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-75.044895 [0m
[37m WARNING:2025-09-04 16:12:56,730 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-75.044895 [0m
[37m WARNING:2025-09-04 16:12:56,731 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m WARNING:2025-09-04 16:12:56,732 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-71.828984 [0m
[37m INFO:2025-09-04 16:12:59,600 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21164357490310365, 'rbf': 0.1985572815708856, 'tabpfn': 0.5897991435260107} [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-75.0449, -75.0449] → 归一化[0.0000, 1.0000], 均值-75.0449→0.9800 [0m
[37m INFO:2025-09-04 16:12:59,601 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-71.8290, -71.8290] → 归一化[0.5000, 0.5000], 均值-71.8290→0.5000 [0m
[37m INFO:2025-09-04 16:12:59,602 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-94.1907, -62.8305] → 归一化[0.0000, 1.0000], 均值-82.3882→0.3764 [0m
[37m INFO:2025-09-04 16:12:59,603 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第36个候选点 tensor([-1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 GP: 原始分-75.0449 → 归一化1.0000, 权重0.2116, 加权分0.2116 [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 RBF: 原始分-71.8290 → 归一化0.5000, 权重0.1986, 加权分0.0993 [0m
[37m INFO:2025-09-04 16:12:59,604 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-62.8305 → 归一化1.0000, 权重0.5898, 加权分0.5898 [0m
[37m INFO:2025-09-04 16:12:59,605 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:01,127 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 GP: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 RBF: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,128 - (ensemble_manager.py:128) -    🟡 TABPFN: 9/50 (18.0%) [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:227) - 🔄 检测到性能差异(16.9649)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00486): [0m
[37m INFO:2025-09-04 16:13:01,129 - (ensemble_manager.py:326) -    GP: 0.2116 → 0.2147 (MAE:44.6076, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (ensemble_manager.py:326) -    RBF: 0.1986 → 0.2004 (MAE:46.6576, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (ensemble_manager.py:326) -    TABPFN: 0.5898 → 0.5849 (MAE:29.6927, 数据:9) [0m
[37m INFO:2025-09-04 16:13:01,130 - (bounce.py:864) - 🚀 Iteration 13: No improvement. Best function value -117.637 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:01,131 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 14 [0m
[37m INFO:2025-09-04 16:13:01,131 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,173 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 14, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:01,174 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,175 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 14 [0m
[37m INFO:2025-09-04 16:13:01,270 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 14 [0m
[37m WARNING:2025-09-04 16:13:01,386 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:01,390 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:01,392 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m WARNING:2025-09-04 16:13:01,393 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m INFO:2025-09-04 16:13:05,329 - (genetic_algorithm.py:367) - 集成代理模型评估了76个GA个体，使用权重: {'gp': 0.2146915483388602, 'rbf': 0.20036872180530474, 'tabpfn': 0.5849397298558351} [0m
[37m INFO:2025-09-04 16:13:05,333 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:05,333 - (ensemble_manager.py:718) - 📊 输入训练数据: 14个样本 (上次: 14) [0m
[37m WARNING:2025-09-04 16:13:05,338 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:05,340 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-77.926143 [0m
[37m WARNING:2025-09-04 16:13:05,341 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m WARNING:2025-09-04 16:13:05,342 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-74.736636 [0m
[37m INFO:2025-09-04 16:13:08,346 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:08,346 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.2146915483388602, 'rbf': 0.20036872180530474, 'tabpfn': 0.5849397298558351} [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-77.9261, -77.9261] → 归一化[0.0000, 1.0000], 均值-77.9261→0.9799 [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-74.7366, -74.7366] → 归一化[0.5000, 0.5000], 均值-74.7366→0.5000 [0m
[37m INFO:2025-09-04 16:13:08,347 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-98.6572, -59.8825] → 归一化[0.0000, 1.0000], 均值-86.4066→0.3159 [0m
[37m INFO:2025-09-04 16:13:08,348 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第37个候选点 tensor([ 1., -1.,  1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 GP: 原始分-77.9261 → 归一化1.0000, 权重0.2147, 加权分0.2147 [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 RBF: 原始分-74.7366 → 归一化0.5000, 权重0.2004, 加权分0.1002 [0m
[37m INFO:2025-09-04 16:13:08,349 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-71.1047 → 归一化0.7106, 权重0.5849, 加权分0.4156 [0m
[37m INFO:2025-09-04 16:13:08,350 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1., -1.,  1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:09,860 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:128) -    🟡 GP: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,861 - (ensemble_manager.py:128) -    🟡 RBF: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:128) -    🟡 TABPFN: 10/50 (20.0%) [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.5882)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01208): [0m
[37m INFO:2025-09-04 16:13:09,862 - (ensemble_manager.py:326) -    GP: 0.2147 ↓ 0.2092 (MAE:43.5465, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (ensemble_manager.py:326) -    RBF: 0.2004 ↓ 0.1937 (MAE:45.7105, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (ensemble_manager.py:326) -    TABPFN: 0.5849 ↗ 0.5970 (MAE:28.1223, 数据:10) [0m
[37m INFO:2025-09-04 16:13:09,863 - (bounce.py:864) - 🚀 Iteration 14: No improvement. Best function value -117.637 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:09,864 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 15 [0m
[37m INFO:2025-09-04 16:13:09,864 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 15 [0m
[37m INFO:2025-09-04 16:13:09,910 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 15, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:09,911 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 15 [0m
[37m INFO:2025-09-04 16:13:09,912 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 15 [0m
[37m INFO:2025-09-04 16:13:10,009 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 15 [0m
[37m WARNING:2025-09-04 16:13:10,113 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-80.352085 [0m
[37m WARNING:2025-09-04 16:13:10,115 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-80.352085 [0m
[37m WARNING:2025-09-04 16:13:10,116 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m WARNING:2025-09-04 16:13:10,117 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m INFO:2025-09-04 16:13:13,427 - (genetic_algorithm.py:367) - 集成代理模型评估了69个GA个体，使用权重: {'gp': 0.20923969099889522, 'rbf': 0.19374254816163344, 'tabpfn': 0.5970177608394713} [0m
[37m INFO:2025-09-04 16:13:13,435 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:13,435 - (ensemble_manager.py:718) - 📊 输入训练数据: 15个样本 (上次: 15) [0m
[37m WARNING:2025-09-04 16:13:13,439 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-80.352069 [0m
[37m WARNING:2025-09-04 16:13:13,441 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-80.352069 [0m
[37m WARNING:2025-09-04 16:13:13,443 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m WARNING:2025-09-04 16:13:13,444 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-77.215712 [0m
[37m INFO:2025-09-04 16:13:16,202 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:16,203 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.20923969099889522, 'rbf': 0.19374254816163344, 'tabpfn': 0.5970177608394713} [0m
[37m INFO:2025-09-04 16:13:16,203 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-80.3521, -80.3521] → 归一化[0.0000, 1.0000], 均值-80.3521→0.9795 [0m
[37m INFO:2025-09-04 16:13:16,204 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-77.2157, -77.2157] → 归一化[0.5000, 0.5000], 均值-77.2157→0.5000 [0m
[37m INFO:2025-09-04 16:13:16,204 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-100.6431, -76.5565] → 归一化[0.0000, 1.0000], 均值-91.5231→0.3786 [0m
[37m INFO:2025-09-04 16:13:16,207 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第37个候选点 tensor([-1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:16,207 - (ensemble_manager.py:848) -    📊 GP: 原始分-80.3521 → 归一化1.0000, 权重0.2092, 加权分0.2092 [0m
[37m INFO:2025-09-04 16:13:16,208 - (ensemble_manager.py:848) -    📊 RBF: 原始分-77.2157 → 归一化0.5000, 权重0.1937, 加权分0.0969 [0m
[37m INFO:2025-09-04 16:13:16,208 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-81.5026 → 归一化0.7947, 权重0.5970, 加权分0.4744 [0m
[37m INFO:2025-09-04 16:13:16,208 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([-1.,  1., -1., -1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:17,729 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 GP: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 RBF: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,730 - (ensemble_manager.py:128) -    🟡 TABPFN: 11/50 (22.0%) [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.1934)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:304) - 🔄 权重保持稳定 (最大变化: 0.00404): [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:326) -    GP: 0.2092 → 0.2116 (MAE:43.1166, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,731 - (ensemble_manager.py:326) -    RBF: 0.1937 → 0.1954 (MAE:45.3690, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,732 - (ensemble_manager.py:326) -    TABPFN: 0.5970 → 0.5930 (MAE:28.1757, 数据:11) [0m
[37m INFO:2025-09-04 16:13:17,732 - (bounce.py:857) - ✨ Iteration 15: [92mNew incumbent function value -119.170[0m [全局模型策略有效] [0m
[37m INFO:2025-09-04 16:13:17,732 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 16 [0m
[37m INFO:2025-09-04 16:13:17,733 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,775 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 16, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:17,776 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,778 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 16 [0m
[37m INFO:2025-09-04 16:13:17,873 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 16 [0m
[37m WARNING:2025-09-04 16:13:17,979 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-82.940878 [0m
[37m WARNING:2025-09-04 16:13:17,987 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-82.940878 [0m
[37m WARNING:2025-09-04 16:13:17,989 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m WARNING:2025-09-04 16:13:17,990 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m INFO:2025-09-04 16:13:21,143 - (genetic_algorithm.py:367) - 集成代理模型评估了73个GA个体，使用权重: {'gp': 0.21163946435028272, 'rbf': 0.195379222771705, 'tabpfn': 0.5929813128780123} [0m
[37m INFO:2025-09-04 16:13:21,151 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:21,151 - (ensemble_manager.py:718) - 📊 输入训练数据: 16个样本 (上次: 16) [0m
[37m WARNING:2025-09-04 16:13:21,156 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-82.940866 [0m
[37m WARNING:2025-09-04 16:13:21,157 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-82.940866 [0m
[37m WARNING:2025-09-04 16:13:21,159 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m WARNING:2025-09-04 16:13:21,160 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-79.837875 [0m
[37m INFO:2025-09-04 16:13:23,739 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.21163946435028272, 'rbf': 0.195379222771705, 'tabpfn': 0.5929813128780123} [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-82.9409, -82.9409] → 归一化[0.0000, 1.0000], 均值-82.9409→0.9740 [0m
[37m INFO:2025-09-04 16:13:23,740 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-79.8379, -79.8379] → 归一化[0.5000, 0.5000], 均值-79.8379→0.5000 [0m
[37m INFO:2025-09-04 16:13:23,741 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-104.3765, -89.1961] → 归一化[0.0000, 1.0000], 均值-95.7077→0.5711 [0m
[37m INFO:2025-09-04 16:13:23,742 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第43个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 GP: 原始分-82.9409 → 归一化1.0000, 权重0.2116, 加权分0.2116 [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 RBF: 原始分-79.8379 → 归一化0.5000, 权重0.1954, 加权分0.0977 [0m
[37m INFO:2025-09-04 16:13:23,743 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-90.2266 → 归一化0.9321, 权重0.5930, 加权分0.5527 [0m
[37m INFO:2025-09-04 16:13:23,743 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:25,159 - (ensemble_manager.py:900) - 📊 性能记录完成: 3条记录已添加到权重管理系统 [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:125) - 📊 滑动窗口状态汇总: [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 GP: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 RBF: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:128) -    🟡 TABPFN: 12/50 (24.0%) [0m
[37m INFO:2025-09-04 16:13:25,160 - (ensemble_manager.py:227) - 🔄 检测到性能差异(17.7472)，启动温和权重调整 [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:302) - 🔄 权重适度调整 (最大变化: 0.01252): [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    GP: 0.2116 ↓ 0.2058 (MAE:42.0665, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    RBF: 0.1954 ↓ 0.1887 (MAE:44.3898, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,161 - (ensemble_manager.py:326) -    TABPFN: 0.5930 ↗ 0.6055 (MAE:26.6426, 数据:12) [0m
[37m INFO:2025-09-04 16:13:25,162 - (bounce.py:864) - 🚀 Iteration 16: No improvement. Best function value -119.170 [全局模型策略] [0m
[37m INFO:2025-09-04 16:13:25,162 - (ga_tabpfn_integration.py:139) - 🔄 更新集成代理模型，数据量: 17 [0m
[37m INFO:2025-09-04 16:13:25,163 - (gp_surrogate.py:267) - GP确认完整数据集更新，直接重新训练，样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,194 - (gp_surrogate.py:119) - GP模型训练完成，设备: cpu, 训练样本数: 17, 输入维度: 60 [0m
[37m INFO:2025-09-04 16:13:25,195 - (rbf_surrogate.py:382) - RBF确认完整数据集更新，直接重新训练，样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,196 - (rbf_surrogate.py:139) - RBF模型训练完成，设备: cpu, 训练样本数: 17 [0m
[37m INFO:2025-09-04 16:13:25,286 - (tabpfn_surrogate.py:502) - TabPFN模型更新完成，总样本数: 17 [0m
[37m WARNING:2025-09-04 16:13:25,397 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m WARNING:2025-09-04 16:13:25,399 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m INFO:2025-09-04 16:13:28,478 - (genetic_algorithm.py:367) - 集成代理模型评估了72个GA个体，使用权重: {'gp': 0.205846566601404, 'rbf': 0.1886566828199899, 'tabpfn': 0.6054967505786062} [0m
[37m INFO:2025-09-04 16:13:28,483 - (ensemble_manager.py:706) - 🎯 集成模型开始预测最佳中心点，候选点数: 50 [0m
[37m INFO:2025-09-04 16:13:28,483 - (ensemble_manager.py:718) - 📊 输入训练数据: 17个样本 (上次: 17) [0m
[37m WARNING:2025-09-04 16:13:28,488 - (gp_surrogate.py:195) - ⚠️ GP预测值方差过小，所有预测几乎相同：-84.842715 [0m
[37m WARNING:2025-09-04 16:13:28,489 - (ensemble_manager.py:658) - 🚨 GP模型预测值方差过小，所有预测几乎相同：-84.842715 [0m
[37m WARNING:2025-09-04 16:13:28,490 - (rbf_surrogate.py:284) - ⚠️ RBF预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m WARNING:2025-09-04 16:13:28,491 - (ensemble_manager.py:658) - 🚨 RBF模型预测值方差过小，所有预测几乎相同：-81.815418 [0m
[37m INFO:2025-09-04 16:13:31,180 - (ensemble_manager.py:800) - 💡 集成模型预测详情: [0m
[37m INFO:2025-09-04 16:13:31,180 - (ensemble_manager.py:801) -    📊 各模型权重: {'gp': 0.205846566601404, 'rbf': 0.1886566828199899, 'tabpfn': 0.6054967505786062} [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 GP: 原始范围[-84.8427, -84.8427] → 归一化[0.0000, 1.0000], 均值-84.8427→0.9547 [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 RBF: 原始范围[-81.8154, -81.8154] → 归一化[0.5000, 0.5000], 均值-81.8154→0.5000 [0m
[37m INFO:2025-09-04 16:13:31,181 - (ensemble_manager.py:813) -    📈 TABPFN: 原始范围[-111.2996, -81.9093] → 归一化[0.0000, 1.0000], 均值-97.5443→0.4680 [0m
[37m INFO:2025-09-04 16:13:31,183 - (ensemble_manager.py:840) - 🎯 集成选择结果: 第40个候选点 tensor([ 1.,  1., -1.], dtype=torch.float64)... [0m
[37m INFO:2025-09-04 16:13:31,183 - (ensemble_manager.py:848) -    📊 GP: 原始分-84.8427 → 归一化1.0000, 权重0.2058, 加权分0.2058 [0m
[37m INFO:2025-09-04 16:13:31,184 - (ensemble_manager.py:848) -    📊 RBF: 原始分-81.8154 → 归一化0.5000, 权重0.1887, 加权分0.0943 [0m
[37m INFO:2025-09-04 16:13:31,184 - (ensemble_manager.py:848) -    📊 TABPFN: 原始分-81.9093 → 归一化1.0000, 权重0.6055, 加权分0.6055 [0m
[37m INFO:2025-09-04 16:13:31,184 - (bounce.py:598) - 🎯 [gp] 全局模型预测的TR中心: tensor([ 1.,  1., -1., -1., -1.], dtype=torch.float64)... [0m
