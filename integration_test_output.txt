2025-09-02 13:10:31,663 - INFO - TabPFN代理模型变量类型分析完成: 连续变量: 0, 二分变量: 5, 类别变量: 0
2025-09-02 13:10:31,664 - INFO - 创建TabPFN代理模型，n_bins: 3
2025-09-02 13:10:31,664 - INFO - 使用TABPFN作为全局代理模型
2025-09-02 13:10:31,664 - INFO - GP全局代理模型初始化完成，参数: {'lengthscale_prior_shape': 1.5, 'lengthscale_prior_rate': 0.1, 'outputscale_prior_shape': 1.5, 'outputscale_prior_rate': 0.5, 'noise_prior_shape': 1.1, 'noise_prior_rate': 0.05, 'discrete_ard': False, 'continuous_ard': True}
2025-09-02 13:10:31,664 - INFO - 创建GP代理模型，参数: {}
2025-09-02 13:10:31,665 - INFO - ✅ GP模型初始化完成
2025-09-02 13:10:31,665 - INFO - RBF全局代理模型初始化完成，核函数: gaussian, epsilon: 1.0
2025-09-02 13:10:31,665 - INFO - 创建RBF代理模型，核函数: gaussian, epsilon: 1.0
2025-09-02 13:10:31,665 - INFO - ✅ RBF模型初始化完成
2025-09-02 13:10:31,665 - INFO - ✅ TabPFN模型初始化完成
2025-09-02 13:10:31,665 - INFO - 🌟 集成代理模型管理器初始化完成
2025-09-02 13:10:31,666 - INFO -    支持模型: ['gp', 'rbf', 'tabpfn']
2025-09-02 13:10:31,666 - INFO -    初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333}
2025-09-02 13:10:31,666 - INFO - 🌟 集成代理模型系统已启用
2025-09-02 13:10:31,666 - INFO - GA-全局模型集成系统初始化完成
🌟 开始测试集成代理模型系统...
✅ 基准函数创建完成，维度: 5, 表示维度: 5
🧬 测试集成代理模型模式...
✅ 集成代理模型管理器创建成功
   支持的模型: ['gp', 'rbf', 'tabpfn']
   初始权重: {'gp': 0.3333333333333333, 'rbf': 0.3333333333333333, 'tabpfn': 0.3333333333333333}

📊 生成测试数据...
   训练数据形状: torch.Size([10, 5])
   目标值范围: [0.000, 5.000]

🌟 测试集成代理模型预测...
❌ 集成预测测试失败: stack expects a non-empty TensorList

❌ 测试失败！请检查集成代理模型系统。
