#!/usr/bin/env python3
"""
调试GA初始化，检查种群中是否有连续值
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_ga_initialization():
    """调试GA初始化"""
    
    print("🔍 调试GA初始化")
    print("=" * 50)
    
    from bounce.genetic_algorithm import MixedVariableGA, GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建纯二分变量基准
    parameters = [
        Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
        for i in range(5)
    ]
    
    axus = AxUS(parameters=parameters, n_bins=5)
    
    print(f"参数类型: {[p.type for p in parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    print(f"AxUS输入维度: {axus.input_dim}")
    
    # 创建GA配置
    ga_config = GAConfig(population_size=5, max_generations=1)
    
    # 创建一个简单的基准函数
    class SimpleBenchmark:
        def __init__(self):
            self.parameters = parameters
            self.dim = len(parameters)
            self.representation_dim = axus.input_dim
            self.lb_vec = torch.zeros(self.representation_dim, dtype=torch.float64)
            self.ub_vec = torch.ones(self.representation_dim, dtype=torch.float64)

        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)

    benchmark = SimpleBenchmark()

    # 创建GA
    ga = MixedVariableGA(ga_config, axus, benchmark)
    
    print(f"\nGA初始化完成，种群大小: {len(ga.population)}")
    
    # 检查种群中每个个体的基因
    all_discrete = True
    for i, individual in enumerate(ga.population):
        genes = individual.genes
        print(f"\n个体 {i+1}:")
        print(f"  基因: {genes}")
        
        # 检查是否为{-1,1}值
        unique_values = torch.unique(genes)
        is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
        
        print(f"  唯一值: {unique_values}")
        print(f"  是否为{-1,1}值: {is_discrete}")
        
        if not is_discrete:
            all_discrete = False
            print(f"  ❌ 个体{i+1}包含非{-1,1}值")
        else:
            print(f"  ✅ 个体{i+1}完全离散")
    
    if all_discrete:
        print("\n✅ 所有个体都是{-1,1}值")
        return True
    else:
        print("\n❌ 部分个体包含非{-1,1}值")
        return False


def debug_candidate_generation():
    """调试候选中心生成"""
    
    print("\n" + "=" * 50)
    print("🔍 调试候选中心生成")
    print("=" * 50)
    
    from bounce.ga_tabpfn_integration import GATabPFNIntegration
    from bounce.genetic_algorithm import GAConfig
    from bounce.projection import AxUS
    from bounce.util.benchmark import Parameter, ParameterType
    
    # 创建纯二分变量基准
    class BinaryBenchmark:
        def __init__(self):
            self.parameters = [
                Parameter(name=f"x{i}", type=ParameterType.BINARY, lower_bound=0, upper_bound=1)
                for i in range(5)
            ]
            self.dim = 5
            axus_temp = AxUS(parameters=self.parameters, n_bins=5)
            self.representation_dim = axus_temp.input_dim
            self.lb_vec = torch.zeros(self.representation_dim, dtype=torch.float64)
            self.ub_vec = torch.ones(self.representation_dim, dtype=torch.float64)
            
        def __call__(self, x):
            if x.dim() == 1:
                x = x.unsqueeze(0)
            return torch.sum(x**2, dim=1)
    
    benchmark = BinaryBenchmark()
    axus = AxUS(parameters=benchmark.parameters, n_bins=5)
    
    # 创建GA配置
    ga_config = GAConfig(population_size=5, max_generations=1)
    
    # 创建GA-TabPFN集成
    integration = GATabPFNIntegration(axus, benchmark, ga_config)
    
    print(f"基准参数类型: {[p.type for p in benchmark.parameters]}")
    print(f"AxUS目标维度: {axus.target_dim}")
    print(f"AxUS输入维度: {axus.input_dim}")
    
    # 检查GA种群
    print(f"\nGA种群大小: {len(integration.ga.population)}")
    
    all_ga_discrete = True
    for i, individual in enumerate(integration.ga.population):
        genes = individual.genes
        unique_values = torch.unique(genes)
        is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
        
        print(f"GA个体 {i+1}: {genes}")
        print(f"  唯一值: {unique_values}")
        print(f"  是否为{-1,1}值: {is_discrete}")
        
        if not is_discrete:
            all_ga_discrete = False
    
    # 生成候选中心
    candidates = integration._generate_candidate_centers(n_candidates=3)
    print(f"\n生成了 {len(candidates)} 个候选中心")
    
    all_candidates_discrete = True
    for i, candidate in enumerate(candidates):
        unique_values = torch.unique(candidate)
        is_discrete = torch.all(torch.isin(unique_values, torch.tensor([-1.0, 1.0])))
        
        print(f"候选中心 {i+1}: {candidate}")
        print(f"  唯一值: {unique_values}")
        print(f"  是否为{-1,1}值: {is_discrete}")
        
        if not is_discrete:
            all_candidates_discrete = False
    
    print(f"\n结果:")
    print(f"  GA种群离散性: {'✅ 通过' if all_ga_discrete else '❌ 失败'}")
    print(f"  候选中心离散性: {'✅ 通过' if all_candidates_discrete else '❌ 失败'}")
    
    return all_ga_discrete and all_candidates_discrete


if __name__ == "__main__":
    print("🚀 开始调试GA初始化和候选中心生成")
    print("=" * 60)
    
    try:
        # 调试GA初始化
        ga_ok = debug_ga_initialization()
        
        # 调试候选中心生成
        candidates_ok = debug_candidate_generation()
        
        print("\n" + "=" * 60)
        print("📊 调试结果总结:")
        print(f"   GA初始化: {'✅ 通过' if ga_ok else '❌ 失败'}")
        print(f"   候选中心生成: {'✅ 通过' if candidates_ok else '❌ 失败'}")
        
        if ga_ok and candidates_ok:
            print("\n🎉 所有调试通过！")
            print("GA初始化和候选中心生成都是离散的")
        else:
            print("\n❌ 发现问题，需要进一步修复")
        
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
