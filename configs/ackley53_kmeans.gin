import bounce.bounce
import bounce.trust_region
import bounce.benchmarks
import bounce.projection
import bounce.gaussian_process
import torch

# Macros

FUNCTION_DIMENSIONALITY = 53
DTYPE = "float32"
DEVICE = "cpu"

Benchmark.flip = True

# GP config
get_gp.lengthscale_prior_shape = 1.5
get_gp.lengthscale_prior_rate = 0.1
get_gp.outputscale_prior_shape = 1.5
get_gp.outputscale_prior_rate = 0.5
get_gp.noise_prior_shape = 1.1
get_gp.noise_prior_rate=0.05

# TR config
TrustRegion.length_init_discrete = 10

# Bounce configuration for Ackley53 with K-means elite selection
Bounce.number_initial_points = 5
Bounce.initial_target_dimensionality = 5
Bounce.number_new_bins_on_split = 2
Bounce.maximum_number_evaluations = 300
Bounce.batch_size = 1
Bounce.use_surrogate_local=False
Bounce.local_model_type='rbf'
Bounce.benchmark = @Ackley53()
Bounce.results_dir = "results"
Bounce.device = %DEVICE
Bounce.dtype = %DTYPE
Bounce.use_scipy_lbfgs = True
Bounce.maximum_number_evaluations_until_input_dim = 100

# GA-Global Model Integration configuration
GATabPFNIntegration.global_model_type = 'gp'

# K-means Elite Selection Configuration
GAConfig.use_kmeans_elite_selection = True
GAConfig.elite_clusters_ratio = 0.5
GAConfig.elitism_rate = 0.3
GAConfig.population_size = 150
GAConfig.max_generations = 50
GAConfig.crossover_rate = 0.8
GAConfig.mutation_rate = 0.15

# Benchmark configuration
AckleyEffectiveDim.dim = %FUNCTION_DIMENSIONALITY