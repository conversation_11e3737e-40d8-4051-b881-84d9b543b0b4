# ===================================================================
#           机器人臂路径规划问题的完整 Gin 配置文件
# ===================================================================

# 导入包含我们新 benchmark 类的文件
import bounce.benchmarks

# --- 1. 全局宏定义 ---
DEVICE = "cpu"      # 对于此问题，CPU足够。如果需要GPU加速，可改为 "cuda"
DTYPE = "float32"

# --- 2. 全局 Benchmark 配置 ---

# --- 3. GP (高斯过程) 代理模型超参数 ---
# 如果您的集成模型中包含GP，这些参数会非常重要
import bounce.gaussian_process
get_gp.lengthscale_prior_shape = 1.5
get_gp.lengthscale_prior_rate = 0.1
get_gp.outputscale_prior_shape = 1.5
get_gp.outputscale_prior_rate = 0.5
get_gp.noise_prior_shape = 1.1
get_gp.noise_prior_rate=0.05

# --- 4. TR (置信域) 超参数 ---
import bounce.trust_region
TrustRegion.length_init_discrete = 10

# --- 5. Bounce 算法核心配置 ---
Bounce.number_initial_points = 5           # 初始采样点数 (高维问题建议多一些)
Bounce.maximum_number_evaluations = 200     # 最大评估预算
Bounce.batch_size = 1
Bounce.results_dir = "results/robot_arm_52d" # 结果保存路径
Bounce.device = %DEVICE
Bounce.dtype = %DTYPE

# Bounce 必需的运行参数
Bounce.initial_target_dimensionality = 5
Bounce.number_new_bins_on_split = 2

# Bounce 其他重要策略参数 
Bounce.use_scipy_lbfgs = True               # 使用L-BFGS优化采集函数，通常更高效
Bounce.maximum_number_evaluations_until_input_dim = 50 # Bounce的一个策略参数

# 🚀 关键修复：启用局部代理模型
Bounce.use_surrogate_local = True           # 启用局部代理模型


# --- 6. 高级功能：集成代理模型配置 (您最关心的部分) ---
# 假设您的集成模型配置是在这个类下
# import bounce.integration # 请确认这个导入路径是否正确，如果不是请修改
GATabPFNIntegration.global_model_type = 'gp'  # 指定默认模型
GATabPFNIntegration.enable_ensemble = True    # 关键：启动集成模式！

# --- 7. 最终绑定 Benchmark ---
# 将 Bounce 的优化目标指向我们新创建的 RobotArmPath 类
Bounce.benchmark = @RobotArmPath()