#!/usr/bin/env python3
"""
滑动窗口机制验证脚本
测试滑动窗口是否正常工作，确保旧数据能被正确移除
"""

import torch
import logging
import sys
import os

# 设置日志级别为INFO以查看关键信息
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_sliding_window():
    """测试滑动窗口机制"""
    print("🔬 测试滑动窗口机制")
    print("=" * 60)
    
    try:
        from bounce.benchmarks import Ackley53
        from bounce.projection import AxUS
        from bounce.ensemble_manager import EnsembleManager, WeightManager
        
        # 创建测试环境
        benchmark = Ackley53()
        axus = AxUS(parameters=benchmark.parameters, n_bins=5)
        
        # 创建权重管理器（小窗口便于测试）
        weight_manager = WeightManager(
            model_names=['gp', 'rbf', 'tabpfn'],
            window_size=5,  # 小窗口便于观察
            min_weight_protection=0.1
        )
        
        print(f"✅ 权重管理器创建成功，窗口大小: {weight_manager.window_size}")
        
        # 模拟添加超过窗口大小的数据
        print(f"\n🔄 模拟添加数据（窗口大小: {weight_manager.window_size}）:")
        
        for i in range(8):  # 添加8条数据，超过窗口大小
            # 模拟预测值和实际值
            predicted = 100.0 + i * 10  # 100, 110, 120, ...
            actual = 95.0 + i * 10      # 95, 105, 115, ...
            
            # 记录性能
            weight_manager.record_prediction('gp', predicted, actual)
            
            # 检查当前数据量
            current_count = len(weight_manager.performance_history['gp'])
            print(f"   第{i+1}条数据: 预测={predicted}, 实际={actual}, 当前数据量={current_count}")
            
            # 检查是否触发了滑动窗口
            if i >= weight_manager.window_size:
                expected_count = weight_manager.window_size
                if current_count == expected_count:
                    print(f"   ✅ 滑动窗口正常工作: 保持{expected_count}条数据")
                else:
                    print(f"   ❌ 滑动窗口异常: 期望{expected_count}条，实际{current_count}条")
        
        # 检查最终状态
        final_count = len(weight_manager.performance_history['gp'])
        print(f"\n📊 最终状态: {final_count}条数据")
        
        # 显示最后几条数据
        last_data = weight_manager.performance_history['gp'][-3:]  # 显示最后3条
        print(f"   最后几条数据: {last_data}")
        
        # 验证是否只保留了最新的数据
        expected_last_data = [(150.0, 145.0), (160.0, 155.0), (170.0, 165.0)]  # 最后3条
        if last_data == expected_last_data:
            print(f"   ✅ 数据正确: 只保留了最新的{weight_manager.window_size}条数据")
            success = True
        else:
            print(f"   ❌ 数据错误: 期望{expected_last_data}, 实际{last_data}")
            success = False
            
        # 测试MAE计算
        mae = weight_manager.calculate_mae('gp')
        print(f"   MAE计算结果: {mae:.4f}")
        
        # 测试权重更新
        print(f"\n🔄 测试权重更新:")
        weight_manager.update_weights()
        weights = weight_manager.get_weights()
        print(f"   权重更新后: {weights}")
        
        return success
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 滑动窗口机制验证")
    print("=" * 80)
    
    success = test_sliding_window()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 滑动窗口机制验证通过！")
        print("💡 滑动窗口能正确移除旧数据，确保权重计算基于最新的性能数据")
        print("🔧 窗口大小为50时，当数据超过50条时会自动移除最旧的数据")
    else:
        print("⚠️ 滑动窗口机制验证失败，需要进一步检查。")
    
    print("=" * 80)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)