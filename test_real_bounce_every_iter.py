#!/usr/bin/env python3
"""
真实测试Bounce主循环中每次迭代都进行GA+TabPFN
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

import torch
import logging
import gin
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_real_bounce_every_iteration():
    """真实测试Bounce主循环的每次迭代GA模式"""
    
    print("🚀 真实测试Bounce主循环 - 每次迭代GA+TabPFN模式")
    print("=" * 60)
    
    # 模拟TabPFN以避免网络调用
    with patch('bounce.tabpfn_surrogate.TabPFNClassifier') as mock_classifier:
        # 设置TabPFN模拟器
        mock_instance = Mock()
        mock_instance.fit.return_value = None
        # 模拟预测概率
        mock_instance.predict_proba.return_value = [
            [0.8, 0.2],  # 候选点1：很可能是最好的
            [0.3, 0.7],  # 候选点2：可能较差
        ]
        mock_classifier.return_value = mock_instance
        
        # 清除之前的gin配置
        gin.clear_config()
        
        # 导入必要的模块
        from bounce.bounce import Bounce
        from bounce.benchmarks import MaxSat60
        
        # 配置Bounce算法参数（小规模测试）
        gin.parse_config([
            "Bounce.number_initial_points = 5",
            "Bounce.initial_target_dimensionality = 4", 
            "Bounce.number_new_bins_on_split = 1",
            "Bounce.maximum_number_evaluations = 20",  # 小规模测试
            "Bounce.batch_size = 1",
            "Bounce.results_dir = 'test_results_real_bounce'",
            "Bounce.device = 'cpu'",
            "Bounce.dtype = 'float64'",
            "Bounce.use_scipy_lbfgs = False",
            "Bounce.maximum_number_evaluations_until_input_dim = 18"
        ])
        
        # 创建基准函数
        benchmark = MaxSat60()
        
        # 创建Bounce实例
        bounce = Bounce(benchmark=benchmark)
        
        print(f"📊 基准函数: {benchmark.__class__.__name__}")
        print(f"📏 问题维度: {benchmark.dim}")
        print(f"⚙️ 每次迭代都进行GA: {bounce.enable_ga_every_iteration}")
        print(f"⚙️ TabPFN最少数据点: {bounce.min_data_for_tabpfn}")
        
        # 记录GA状态的变化
        class GAMonitor:
            def __init__(self):
                self.ga_calls = []
                self.generation_history = []
                self.fitness_history = []
                
            def record_ga_state(self, bounce_instance, iteration):
                ga_info = bounce_instance.ga_tabpfn_integration.get_integration_info()['ga_info']
                generation = ga_info['current_generation']
                fitness = ga_info['best_fitness']
                
                self.generation_history.append(generation)
                self.fitness_history.append(fitness)
                
                print(f"   迭代{iteration}: GA代数={generation}, 最佳适应度={fitness}")
                
                return generation, fitness
        
        monitor = GAMonitor()
        
        # 修改Bounce的run方法来监控GA状态
        original_run = bounce.run
        
        def monitored_run():
            # 初始采样
            bounce.sample_init()
            print(f"\n✅ 初始采样完成，评估次数: {bounce._n_evals}")
            
            iteration_count = 0
            
            # 主循环
            while bounce._n_evals < bounce.maximum_number_evaluations:
                iteration_count += 1
                print(f"\n--- 主循环迭代 {iteration_count} (评估次数: {bounce._n_evals}) ---")
                
                # 记录迭代前的GA状态
                pre_generation, pre_fitness = monitor.record_ga_state(bounce, f"{iteration_count}-前")
                
                # 检查是否会触发GA
                will_trigger_ga = (
                    bounce.enable_ga_every_iteration and 
                    len(bounce.x_up_global) >= bounce.min_data_for_tabpfn
                )
                print(f"🧬 是否会触发GA: {will_trigger_ga} (数据点数: {len(bounce.x_up_global)})")
                
                # 执行一次主循环迭代
                axus = bounce.random_embedding
                x = bounce.x_tr
                fx = bounce.fx_tr

                # 这里是主循环的核心部分，包含GA调用
                predicted_center = None
                if (bounce.enable_ga_every_iteration and 
                    len(bounce.x_up_global) >= bounce.min_data_for_tabpfn):
                    
                    try:
                        logging.info(f"🧬 执行全局搜索 (评估次数: {bounce._n_evals})")
                        
                        # 运行遗传算法进行全局搜索
                        best_low_dim, best_high_dim = bounce.ga_tabpfn_integration.run_global_search(
                            existing_X=bounce.x_up_global,
                            existing_y=bounce.fx_global
                        )
                        
                        # 使用TabPFN预测最佳TR中心点
                        predicted_center = bounce.ga_tabpfn_integration.predict_best_center_with_tabpfn(
                            existing_X=bounce.x_up_global,
                            existing_y=bounce.fx_global
                        )
                        
                        logging.info(f"🎯 全局搜索完成，GA最佳解适应度: {bounce.ga_tabpfn_integration.ga.best_individual.fitness:.6f}")
                        logging.info(f"🎯 TabPFN预测的TR中心: {predicted_center[:3]}...")
                    except Exception as e:
                        logging.warning(f"全局搜索失败，使用默认策略: {e}")
                        predicted_center = None
                
                # 记录迭代后的GA状态
                post_generation, post_fitness = monitor.record_ga_state(bounce, f"{iteration_count}-后")
                
                # 检查GA是否进化了
                if will_trigger_ga:
                    if post_generation > pre_generation:
                        print(f"✅ GA成功进化：{pre_generation} -> {post_generation}")
                    else:
                        print(f"⚠️  GA没有进化：{pre_generation} -> {post_generation}")
                
                # 添加一个随机评估点来继续迭代
                random_point_low = torch.rand(axus.target_dim, dtype=torch.float64) * 2 - 1
                from bounce.util.data_handling import from_1_around_origin
                random_point_01 = (random_point_low + 1) / 2
                high_dim = axus.project_up(random_point_01.unsqueeze(0).T).T
                random_point_high = from_1_around_origin(
                    x=high_dim,
                    lb=benchmark.lb_vec, 
                    ub=benchmark.ub_vec
                ).squeeze(0)
                
                random_fx = benchmark(random_point_high.unsqueeze(0))
                
                bounce._add_data_to_tr_observations(
                    xs_down=random_point_low.unsqueeze(0),
                    xs_up=random_point_high.unsqueeze(0),
                    fxs=random_fx
                )
                bounce._n_evals += 1
                
                print(f"   当前最佳值: {bounce.fx_tr.min().item():.6f}")
                
                # 限制测试迭代次数
                if iteration_count >= 8:
                    break
        
        # 运行监控版本
        monitored_run()
        
        print(f"\n📊 GA进化分析:")
        print(f"   GA代数历史: {monitor.generation_history}")
        print(f"   适应度历史: {monitor.fitness_history}")
        
        # 分析GA是否在每次迭代中都进化
        ga_evolution_pairs = []
        for i in range(0, len(monitor.generation_history), 2):
            if i + 1 < len(monitor.generation_history):
                pre = monitor.generation_history[i]
                post = monitor.generation_history[i + 1]
                evolved = post > pre
                ga_evolution_pairs.append((pre, post, evolved))
        
        print(f"\n🔍 GA进化详情:")
        for i, (pre, post, evolved) in enumerate(ga_evolution_pairs):
            status = "✅ 进化" if evolved else "❌ 未进化"
            print(f"   迭代{i+1}: {pre} -> {post} ({status})")
        
        # 最终验证
        total_evolutions = sum(1 for _, _, evolved in ga_evolution_pairs if evolved)
        print(f"\n📈 总结:")
        print(f"   总迭代次数: {len(ga_evolution_pairs)}")
        print(f"   GA进化次数: {total_evolutions}")
        print(f"   进化成功率: {total_evolutions/len(ga_evolution_pairs)*100:.1f}%" if ga_evolution_pairs else "0%")
        
        if total_evolutions == len(ga_evolution_pairs) and len(ga_evolution_pairs) > 0:
            print("🎉 验证成功：GA在每次迭代中都进行了进化！")
        elif total_evolutions > 0:
            print("⚠️  部分成功：GA在部分迭代中进化了")
        else:
            print("❌ 验证失败：GA没有进化")
        
        return bounce


if __name__ == "__main__":
    try:
        bounce_result = test_real_bounce_every_iteration()
        
        print("\n" + "=" * 60)
        print("🎉 真实Bounce测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        gin.clear_config()
