import pandas as pd
import glob
import pathlib
import os
import numpy as np

# 递归查找所有实验的eval_history.csv
search_pattern = "results/Ackley53/*/*/eval_history.csv"
result_files = sorted(glob.glob(search_pattern), key=lambda x: pathlib.Path(x).stat().st_mtime)

print(f"搜索模式: {search_pattern}")
print(f"找到的文件数量: {len(result_files)}")

if len(result_files) == 0:
    print("❌ 没有找到任何eval_history.csv文件！")
    print("当前工作目录:", os.getcwd())
    print("尝试绝对路径搜索...")
    abs_pattern = os.path.join(os.getcwd(), search_pattern)
    result_files = sorted(glob.glob(abs_pattern), key=lambda x: pathlib.Path(x).stat().st_mtime)
    print(f"绝对路径搜索找到的文件数量: {len(result_files)}")

print("\n" + "="*50)
print("详细文件检查:")
print("="*50)

all_histories = []
all_steps = []
problem_files = []

for i, f in enumerate(result_files):
    try:
        df = pd.read_csv(f)
        abs_path = os.path.abspath(f)
        
        print(f"\n文件 {i+1}: {os.path.basename(f)}")
        print(f"绝对路径: {abs_path}")
        print(f"文件大小: {os.path.getsize(f)} bytes")
        
        # 检查列是否存在
        if "当前最优值" not in df.columns or "评估次数" not in df.columns:
            print("❌ 缺少必要的列!")
            if "当前最优值" not in df.columns:
                print("   缺少列: 当前最优值")
            if "评估次数" not in df.columns:
                print("   缺少列: 评估次数")
            print("   现有列:", df.columns.tolist())
            problem_files.append(f)
            continue
            
        history_values = df["当前最优值"].values
        steps_values = df["评估次数"].values
        
        print(f"  当前最优值长度: {len(history_values)}")
        print(f"  评估次数长度: {len(steps_values)}")
        
        # 检查NaN值
        history_nan = np.isnan(history_values).sum()
        steps_nan = np.isnan(steps_values).sum()
        print(f"  当前最优值NaN数量: {history_nan}")
        print(f"  评估次数NaN数量: {steps_nan}")
        
        if len(history_values) != len(steps_values):
            print("❌ 长度不一致!")
            problem_files.append(f)
        else:
            print("✅ 长度一致")
            
        all_histories.append(history_values)
        all_steps.append(steps_values)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        problem_files.append(f)

print("\n" + "="*50)
print("汇总报告:")
print("="*50)
print(f"总文件数: {len(result_files)}")
print(f"问题文件数: {len(problem_files)}")
print(f"正常文件数: {len(result_files) - len(problem_files)}")

if problem_files:
    print("\n❌ 问题文件列表:")
    for i, f in enumerate(problem_files, 1):
        print(f"{i}. {os.path.abspath(f)}")
else:
    print("\n✅ 所有文件数据列长度一致")

# 如果所有文件都正常，但原始代码仍报错，检查数组形状
if not problem_files and len(all_histories) > 0:
    print("\n检查数组形状兼容性...")
    for i, (h, s) in enumerate(zip(all_histories, all_steps)):
        print(f"文件 {i+1}: history shape {h.shape}, steps shape {s.shape}")